import{d as J,a as K,r as v,G as Z,o as tt,as as et,p as r,c as p,e as k,f as t,g as e,w as s,t as l,h as u,X as M,Y as B,V as st,n as R,a8 as nt,m as f,a0 as at,_ as ot}from"./index-329ed960.js";import{g as U,m as lt,w as ct,u as rt}from"./index-0d74a956.js";import{u as it}from"./dataService-42a3bdf6.js";import{p as x,H as i}from"./signalr-7c260a48.js";import{a as ut}from"./alarm-635d9769.js";const dt={class:"dashboard-container"},_t={class:"welcome-section"},mt={class:"welcome-content"},vt={class:"welcome-text"},pt={class:"welcome-time"},ft={class:"current-time"},gt={class:"current-date"},ht={class:"system-overview"},wt={class:"status-content"},yt={class:"status-icon online"},bt={class:"status-content"},Ct={class:"status-icon"},At={class:"status-info"},Tt={class:"status-value"},kt={class:"status-content"},xt={class:"status-icon warning"},St={class:"status-info"},It={class:"status-value warning"},Dt={class:"status-content"},Lt={class:"status-icon"},Ht={class:"status-info"},Vt={class:"status-value"},zt={class:"quick-actions"},Mt=["onClick"],Bt={class:"action-text"},Rt={class:"recent-alarms"},Ut={class:"card-header"},Nt={class:"signalr-status"},Wt={class:"card-header"},Pt={class:"hub-status"},$t={class:"hub-name"},qt={class:"hub-state"},Gt=J({__name:"index",setup(Et){const N=K(),W=it(),S=v(""),I=v(""),w=v(!1),D=v([]),L=v({}),g=Z({connectedDevices:12,activeAlarms:0,onlineUsers:3}),y=W.userInfo,P=[{name:"警報監控",icon:"Warning",color:"#f56c6c",route:"/plc-alarm/monitor"},{name:"即時數據",icon:"DataBoard",color:"#409eff",route:"/plc-database/realtime"},{name:"GUI監控",icon:"Monitor",color:"#67c23a",route:"/plc-gui/monitor"},{name:"CCTV",icon:"VideoCamera",color:"#e6a23c",route:"/plc-cctv/monitor"},{name:"系統設定",icon:"Setting",color:"#909399",route:"/plc-system/settings"},{name:"用戶管理",icon:"User",color:"#606266",route:"/plc-user/list"}],H=()=>{const n=new Date;S.value=n.toLocaleTimeString("zh-TW"),I.value=n.toLocaleDateString("zh-TW",{year:"numeric",month:"long",day:"numeric",weekday:"long"})},$=async()=>{try{const n=await ut.getAlarmList({pageIndex:1,pageSize:5,customerId:y.customerId});D.value=n.items||[],g.activeAlarms=n.items?.filter(a=>!a.IsAcknowledged).length||0}catch(n){console.error("載入最近警報失敗:",n)}},h=()=>{L.value=x.getAllHubStates()},q=async()=>{try{w.value=!0,await x.connectAllHubs(),h()}catch(n){console.error("重新連接失敗:",n)}finally{w.value=!1}},G=n=>{n.route&&N.push(n.route)},E=n=>new Date(n).toLocaleString("zh-TW"),F=n=>{switch(n){case 1:return"danger";case 2:return"warning";case 3:return"info";default:return"info"}},O=n=>{switch(n){case 1:return"嚴重";case 2:return"警告";case 3:return"資訊";default:return"未知"}},Q=n=>{switch(n){case i.Connected:return"connected";case i.Connecting:return"connecting";case i.Reconnecting:return"reconnecting";case i.Disconnecting:return"disconnecting";default:return"disconnected"}},X=n=>({alarm:"警報",pageTag:"頁面標籤",desigoCC:"DesigoCC",tagModel:"標籤模型",obix:"Obix",cctv:"CCTV"})[n]||n,Y=n=>{switch(n){case i.Connected:return"已連接";case i.Connecting:return"連接中";case i.Reconnecting:return"重連中";case i.Disconnecting:return"斷開中";default:return"已斷開"}};let b=null,C=null;return tt(async()=>{H(),b=setInterval(H,1e3),await $(),h(),C=setInterval(h,5e3);try{await x.connectAllHubs(),h()}catch(n){console.error("連接 Hub 失敗:",n)}}),et(()=>{b&&clearInterval(b),C&&clearInterval(C)}),(n,a)=>{const c=r("el-card"),d=r("el-icon"),_=r("el-col"),A=r("el-row"),V=r("el-button"),m=r("el-table-column"),z=r("el-tag"),j=r("el-table");return p(),k("div",dt,[t("div",_t,[e(c,{class:"welcome-card"},{default:s(()=>[t("div",mt,[t("div",vt,[t("h2",null,"歡迎回來，"+l(u(y).staffName||u(y).account),1),a[1]||(a[1]=t("p",null,"PLC 控制系統運行正常，所有服務已就緒",-1))]),t("div",pt,[t("div",ft,l(S.value),1),t("div",gt,l(I.value),1)])])]),_:1})]),t("div",ht,[e(A,{gutter:20},{default:s(()=>[e(_,{span:6},{default:s(()=>[e(c,{class:"status-card"},{default:s(()=>[t("div",wt,[t("div",yt,[e(d,null,{default:s(()=>[e(u(U))]),_:1})]),a[2]||(a[2]=t("div",{class:"status-info"},[t("div",{class:"status-title"},"系統狀態"),t("div",{class:"status-value online"},"運行中")],-1))])]),_:1})]),_:1}),e(_,{span:6},{default:s(()=>[e(c,{class:"status-card"},{default:s(()=>[t("div",bt,[t("div",Ct,[e(d,null,{default:s(()=>[e(u(lt))]),_:1})]),t("div",At,[a[3]||(a[3]=t("div",{class:"status-title"},"連接設備",-1)),t("div",Tt,l(g.connectedDevices),1)])])]),_:1})]),_:1}),e(_,{span:6},{default:s(()=>[e(c,{class:"status-card"},{default:s(()=>[t("div",kt,[t("div",xt,[e(d,null,{default:s(()=>[e(u(ct))]),_:1})]),t("div",St,[a[4]||(a[4]=t("div",{class:"status-title"},"活躍警報",-1)),t("div",It,l(g.activeAlarms),1)])])]),_:1})]),_:1}),e(_,{span:6},{default:s(()=>[e(c,{class:"status-card"},{default:s(()=>[t("div",Dt,[t("div",Lt,[e(d,null,{default:s(()=>[e(u(rt))]),_:1})]),t("div",Ht,[a[5]||(a[5]=t("div",{class:"status-title"},"在線用戶",-1)),t("div",Vt,l(g.onlineUsers),1)])])]),_:1})]),_:1})]),_:1})]),t("div",zt,[e(c,null,{header:s(()=>a[6]||(a[6]=[t("div",{class:"card-header"},[t("span",null,"快速操作")],-1)])),default:s(()=>[e(A,{gutter:20},{default:s(()=>[(p(),k(M,null,B(P,o=>e(_,{span:4,key:o.name},{default:s(()=>[t("div",{class:"action-item",onClick:T=>G(o)},[t("div",{class:"action-icon",style:st({backgroundColor:o.color})},[e(d,null,{default:s(()=>[(p(),R(nt(o.icon)))]),_:2},1024)],4),t("div",Bt,l(o.name),1)],8,Mt)]),_:2},1024)),64))]),_:1})]),_:1})]),t("div",Rt,[e(c,null,{header:s(()=>[t("div",Ut,[a[8]||(a[8]=t("span",null,"最近警報",-1)),e(V,{type:"text",onClick:a[0]||(a[0]=o=>n.$router.push("/plc-alarm/monitor"))},{default:s(()=>a[7]||(a[7]=[f(" 查看全部 ",-1)])),_:1,__:[7]})])]),default:s(()=>[e(j,{data:D.value,style:{width:"100%"},"max-height":"300"},{default:s(()=>[e(m,{prop:"AlarmTime",label:"時間",width:"180"},{default:s(({row:o})=>[f(l(E(o.AlarmTime)),1)]),_:1}),e(m,{prop:"TagName",label:"標籤",width:"150"}),e(m,{prop:"AlarmMessage",label:"訊息","min-width":"200"}),e(m,{prop:"AlarmLevel",label:"等級",width:"100"},{default:s(({row:o})=>[e(z,{type:F(o.AlarmLevel)},{default:s(()=>[f(l(O(o.AlarmLevel)),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"IsAcknowledged",label:"狀態",width:"100"},{default:s(({row:o})=>[e(z,{type:o.IsAcknowledged?"success":"danger"},{default:s(()=>[f(l(o.IsAcknowledged?"已確認":"未確認"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),_:1})]),t("div",Nt,[e(c,null,{header:s(()=>[t("div",Wt,[a[10]||(a[10]=t("span",null,"服務連接狀態",-1)),e(V,{type:"text",onClick:q,loading:w.value},{default:s(()=>a[9]||(a[9]=[f(" 重新連接 ",-1)])),_:1,__:[9]},8,["loading"])])]),default:s(()=>[e(A,{gutter:20},{default:s(()=>[(p(!0),k(M,null,B(L.value,(o,T)=>(p(),R(_,{span:4,key:T},{default:s(()=>[t("div",Pt,[t("div",{class:at(["hub-icon",Q(o)])},[e(d,null,{default:s(()=>[e(u(U))]),_:1})],2),t("div",$t,l(X(T)),1),t("div",qt,l(Y(o)),1)])]),_:2},1024))),128))]),_:1})]),_:1})])])}}});const jt=ot(Gt,[["__scopeId","data-v-032e4402"]]);export{jt as default};
