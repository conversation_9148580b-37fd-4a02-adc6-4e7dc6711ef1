import { getPluginsList } from "./build/plugins";
import { include, exclude } from "./build/optimize";
import { type UserConfigExport, type ConfigEnv, loadEnv } from "vite";
import {
  root,
  alias,
  wrapperEnv,
  pathResolve,
  __APP_INFO__
} from "./build/utils";

export default ({ mode }: ConfigEnv): UserConfigExport => {
  const { VITE_CDN, VITE_PORT, VITE_COMPRESSION, VITE_PUBLIC_PATH } =
    wrapperEnv(loadEnv(mode, root));
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias
    },
    // 服务端渲染
    server: {
      // 端口号
      port: VITE_PORT,
      host: "0.0.0.0",
      // 允許外部訪問
      strictPort: true, // 固定端口，不自動分配
      // 本地跨域代理 https://cn.vitejs.dev/config/server-options.html#server-proxy
      proxy: {
        // ⚠️ 【重要】PLC API 代理配置 - 請勿修改此區塊！
        // 🚫 絕對不能添加 rewrite 或 pathRewrite，否則會返回 HTML 而不是 JSON
        // ✅ 必須直接轉發 /api 路徑到後端，完全按照舊系統配置
        // 📝 參考：plc-frontend/customize-vue-config.js 第117-129行
        "/api": {
          target: "http://*************:8345",
          changeOrigin: true,
          secure: false,
          // 🚫 【禁止】不要添加 rewrite: (path) => path.replace(/^\/api/, '')
          // ✅ 直接轉發 /api 路徑到後端，不做任何路徑重寫
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('PLC API 代理錯誤:', err);
            });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('PLC API 代理請求:', req.method, req.url);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('PLC API 代理響應:', proxyRes.statusCode, req.url);
            });
          }
        }
      },
      // 预热文件以提前转换和缓存结果，降低启动期间的初始页面加载时长并防止转换瀑布
      warmup: {
        clientFiles: ["./index.html", "./src/{views,components}/*"]
      },
      historyApiFallback: true
    },
    plugins: getPluginsList(VITE_CDN, VITE_COMPRESSION),
    // https://cn.vitejs.dev/config/dep-optimization-options.html#dep-optimization-options
    optimizeDeps: {
      include,
      exclude
    },
    build: {
      // https://cn.vitejs.dev/guide/build.html#browser-compatibility
      target: "es2020", // 更新為 es2020 以支援 BigInt literals
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: pathResolve("./index.html", import.meta.url)
        },
        // 静态资源分类打包
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    }
  };
};
