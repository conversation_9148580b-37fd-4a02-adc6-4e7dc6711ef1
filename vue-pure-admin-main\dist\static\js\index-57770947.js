import{d as c,p as n,c as d,e as r,g as s,w as o,f as e,_ as l}from"./index-329ed960.js";const p={class:"cctv-container"},i={class:"content"},f=c({__name:"index",setup(m){return(u,t)=>{const _=n("el-empty"),a=n("el-card");return d(),r("div",p,[s(a,null,{header:o(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"CCTV 監控管理")],-1)])),default:o(()=>[e("div",i,[s(_,{description:"CCTV 監控管理功能開發中..."})])]),_:1})])}}});const x=l(f,[["__scopeId","data-v-1d5bf73f"]]);export{x as default};
