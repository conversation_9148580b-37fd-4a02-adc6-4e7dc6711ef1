import{d as s,ap as n,D as c,r as p,aY as l,R as d,a3 as f,c as m,e as y}from"./index-329ed960.js";const b=s({__name:"ChartBar",props:{requireData:{type:Array,default:()=>[]},questionData:{type:Array,default:()=>[]}},setup(a){const e=a,{isDark:r}=n(),o=c(()=>r.value?"dark":"light"),t=p(),{setOptions:i}=l(t,{theme:o});return d(()=>e,async()=>{await f(),i({container:".bar-card",color:["#41b6ff","#e85f33"],tooltip:{trigger:"axis",axisPointer:{type:"none"}},grid:{top:"20px",left:"50px",right:0},legend:{data:["需求人数","提问数量"],textStyle:{color:"#606266",fontSize:"0.875rem"},bottom:0},xAxis:[{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"],axisLabel:{fontSize:"0.875rem"},axisPointer:{type:"shadow"}}],yAxis:[{type:"value",axisLabel:{fontSize:"0.875rem"},splitLine:{show:!1}}],series:[{name:"需求人数",type:"bar",barWidth:10,itemStyle:{color:"#41b6ff",borderRadius:[10,10,0,0]},data:e.requireData},{name:"提问数量",type:"bar",barWidth:10,itemStyle:{color:"#e86033ce",borderRadius:[10,10,0,0]},data:e.questionData}]})},{deep:!0,immediate:!0}),(u,h)=>(m(),y("div",{ref_key:"chartRef",ref:t,style:{width:"100%",height:"365px"}},null,512))}});export{b as _};
