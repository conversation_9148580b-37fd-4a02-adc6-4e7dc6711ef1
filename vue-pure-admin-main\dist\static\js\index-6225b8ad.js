import{d as c,p as s,c as d,e as l,g as n,w as o,f as e,_ as r}from"./index-329ed960.js";const p={class:"schedule-container"},i={class:"content"},m=c({__name:"index",setup(u){return(f,t)=>{const _=s("el-empty"),a=s("el-card");return d(),l("div",p,[n(a,null,{header:o(()=>t[0]||(t[0]=[e("div",{class:"card-header"},[e("span",null,"排程管理")],-1)])),default:o(()=>[e("div",i,[n(_,{description:"排程管理功能開發中..."})])]),_:1})])}}});const v=r(m,[["__scopeId","data-v-92067f17"]]);export{v as default};
