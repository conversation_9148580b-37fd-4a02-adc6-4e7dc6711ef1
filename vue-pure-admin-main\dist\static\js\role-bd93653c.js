import{d as ve,r as u,G as ye,D as I,o as ge,p as r,q as be,c as d,e as _,g as l,w as t,f as n,h as x,m as a,aL as ke,l as Ve,n as z,t as f,X as h,Y as T,j as F,E as y,aF as A,_ as we}from"./index-329ed960.js";import{v as Re,z as Ce,a as xe,r as he}from"./index-0d74a956.js";const Te={class:"role-container"},Ue={class:"toolbar"},Be={class:"toolbar-left"},De={class:"toolbar-right"},Pe={class:"permission-settings"},Ee={class:"permission-grid"},Le={class:"feature-name"},$e={class:"permission-checkboxes"},ze={class:"permission-form"},Fe={class:"feature-label"},Se={class:"dialog-footer"},je={key:0},Ne={key:0,class:"root-permission"},Ke={key:1,class:"permission-details"},Me={class:"feature-name"},Oe={class:"permission-tags"},qe={key:0,class:"no-permission"},Ge=ve({__name:"role",setup(Ie){const U=u(!1),B=u(!1),g=u(!1),D=u(!1),S=u("list"),R=u(""),P=u([]),E=u([]),v=u(null),i=ye({id:null,name:"",isRoot:!1,permissions:{}}),L=u(),X={name:[{required:!0,message:"請輸入角色名稱",trigger:"blur"}]},V=[{key:"alarm",name:"警報管理"},{key:"database",name:"數據中心"},{key:"tags",name:"標籤管理"},{key:"user",name:"用戶管理"},{key:"system",name:"系統管理"},{key:"notify",name:"通知系統"},{key:"schedule",name:"排程系統"},{key:"gui",name:"GUI監控"}],Y=u(V.map(s=>({...s,permissions:[]}))),H=I(()=>i.id?"編輯角色":"新增角色"),J=I(()=>R.value?E.value.filter(s=>s.name.toLowerCase().includes(R.value.toLowerCase())):E.value),w=async()=>{try{U.value=!0,await new Promise(s=>setTimeout(s,1e3)),E.value=[{id:"1",name:"超級管理員",isRoot:!0,permissions:{}},{id:"2",name:"操作員",isRoot:!1,permissions:{alarm:["r","u"],database:["r"],tags:["r","c","u"]}},{id:"3",name:"觀察員",isRoot:!1,permissions:{alarm:["r"],database:["r"],tags:["r"]}}]}catch(s){console.error("載入角色列表失敗:",s),y.error("載入角色列表失敗")}finally{U.value=!1}},j=()=>{},Q=()=>{Object.assign(i,{id:null,name:"",isRoot:!1,permissions:{}}),V.forEach(s=>{i.permissions[s.key]=[]}),g.value=!0},W=s=>{Object.assign(i,{id:s.id,name:s.name,isRoot:s.isRoot,permissions:{...s.permissions}}),V.forEach(e=>{i.permissions[e.key]||(i.permissions[e.key]=[])}),g.value=!0},Z=async()=>{try{await L.value?.validate(),B.value=!0,await new Promise(s=>setTimeout(s,1e3)),y.success(i.id?"角色更新成功":"角色創建成功"),g.value=!1,w()}catch(s){console.error("提交失敗:",s)}finally{B.value=!1}},ee=()=>{L.value?.resetFields()},le=s=>{v.value=s,D.value=!0},te=async s=>{try{await A.confirm(`確定要刪除角色 "${s.name}" 嗎？此操作不可恢復。`,"確認刪除",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,500)),y.success("角色刪除成功"),w()}catch(e){e!=="cancel"&&(console.error("刪除失敗:",e),y.error("刪除失敗"))}},se=s=>{P.value=s},oe=async()=>{try{const s=P.value.filter(e=>!e.isRoot);if(s.length===0){y.warning("無法刪除根權限角色");return}await A.confirm(`確定要刪除選中的 ${s.length} 個角色嗎？此操作不可恢復。`,"確認批量刪除",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,1e3)),y.success(`成功刪除 ${s.length} 個角色`),w()}catch(s){s!=="cancel"&&(console.error("批量刪除失敗:",s),y.error("批量刪除失敗"))}},ne=s=>{if(s.isRoot)return"全部";let e=0;return Object.values(s.permissions||{}).forEach(b=>{e+=b.length}),e},ae=s=>({1:1,2:5,3:3})[s.id]||0,N=s=>!v.value||v.value.isRoot?[]:v.value.permissions[s]||[],ie=s=>({r:"info",c:"success",u:"warning",d:"danger"})[s]||"info",re=s=>({r:"讀取",c:"創建",u:"更新",d:"刪除"})[s]||s;return ge(()=>{w()}),(s,e)=>{const b=r("el-icon"),p=r("el-button"),K=r("el-input"),k=r("el-table-column"),C=r("el-tag"),de=r("el-table"),M=r("el-tab-pane"),O=r("el-alert"),c=r("el-checkbox"),q=r("el-checkbox-group"),ue=r("el-card"),me=r("el-tabs"),$=r("el-form-item"),_e=r("el-switch"),pe=r("el-form"),G=r("el-dialog"),ce=r("el-divider"),fe=be("loading");return d(),_("div",Te,[l(me,{modelValue:S.value,"onUpdate:modelValue":e[1]||(e[1]=o=>S.value=o),type:"border-card"},{default:t(()=>[l(M,{label:"角色列表",name:"list"},{default:t(()=>[n("div",Ue,[n("div",Be,[l(p,{type:"primary",onClick:Q},{default:t(()=>[l(b,null,{default:t(()=>[l(x(Re))]),_:1}),e[7]||(e[7]=a(" 新增角色 ",-1))]),_:1,__:[7]}),l(p,{type:"danger",disabled:P.value.length===0,onClick:oe},{default:t(()=>[l(b,null,{default:t(()=>[l(x(Ce))]),_:1}),e[8]||(e[8]=a(" 批量刪除 ",-1))]),_:1,__:[8]},8,["disabled"])]),n("div",De,[l(K,{modelValue:R.value,"onUpdate:modelValue":e[0]||(e[0]=o=>R.value=o),placeholder:"搜尋角色名稱",clearable:"",onKeyup:ke(j,["enter"]),style:{width:"200px"}},{append:t(()=>[l(p,{onClick:j},{default:t(()=>[l(b,null,{default:t(()=>[l(x(xe))]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(p,{onClick:w},{default:t(()=>[l(b,null,{default:t(()=>[l(x(he))]),_:1}),e[9]||(e[9]=a(" 重新載入 ",-1))]),_:1,__:[9]})])]),Ve((d(),z(de,{data:J.value,onSelectionChange:se,stripe:"",border:"",style:{"margin-top":"20px"}},{default:t(()=>[l(k,{type:"selection",width:"55"}),l(k,{prop:"name",label:"角色名稱","min-width":"150"}),l(k,{label:"是否為根權限",width:"120"},{default:t(({row:o})=>[l(C,{type:o.isRoot?"danger":"primary"},{default:t(()=>[a(f(o.isRoot?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),l(k,{label:"權限數量",width:"100"},{default:t(({row:o})=>[l(C,{type:"info"},{default:t(()=>[a(f(ne(o)),1)]),_:2},1024)]),_:1}),l(k,{label:"使用人數",width:"100"},{default:t(({row:o})=>[l(C,{type:"success"},{default:t(()=>[a(f(ae(o)),1)]),_:2},1024)]),_:1}),l(k,{label:"操作",width:"200",fixed:"right"},{default:t(({row:o})=>[l(p,{type:"primary",size:"small",onClick:m=>W(o)},{default:t(()=>e[10]||(e[10]=[a(" 編輯 ",-1)])),_:2,__:[10]},1032,["onClick"]),l(p,{type:"info",size:"small",onClick:m=>le(o)},{default:t(()=>e[11]||(e[11]=[a(" 查看權限 ",-1)])),_:2,__:[11]},1032,["onClick"]),l(p,{type:"danger",size:"small",disabled:o.isRoot,onClick:m=>te(o)},{default:t(()=>e[12]||(e[12]=[a(" 刪除 ",-1)])),_:2,__:[12]},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])),[[fe,U.value]])]),_:1}),l(M,{label:"權限設定",name:"permissions"},{default:t(()=>[n("div",Pe,[l(O,{title:"權限說明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:t(()=>e[13]||(e[13]=[n("p",null,"權限分為四種類型：",-1),n("ul",null,[n("li",null,[n("strong",null,"讀取(R)"),a("：可以查看數據")]),n("li",null,[n("strong",null,"創建(C)"),a("：可以新增數據")]),n("li",null,[n("strong",null,"更新(U)"),a("：可以修改數據")]),n("li",null,[n("strong",null,"刪除(D)"),a("：可以刪除數據")])],-1)])),_:1}),l(ue,null,{header:t(()=>e[14]||(e[14]=[n("span",null,"系統功能權限",-1)])),default:t(()=>[n("div",Ee,[(d(!0),_(h,null,T(Y.value,o=>(d(),_("div",{key:o.key,class:"permission-item"},[n("div",Le,f(o.name),1),n("div",$e,[l(q,{modelValue:o.permissions,"onUpdate:modelValue":m=>o.permissions=m},{default:t(()=>[l(c,{label:"r"},{default:t(()=>e[15]||(e[15]=[a("讀取",-1)])),_:1,__:[15]}),l(c,{label:"c"},{default:t(()=>e[16]||(e[16]=[a("創建",-1)])),_:1,__:[16]}),l(c,{label:"u"},{default:t(()=>e[17]||(e[17]=[a("更新",-1)])),_:1,__:[17]}),l(c,{label:"d"},{default:t(()=>e[18]||(e[18]=[a("刪除",-1)])),_:1,__:[18]})]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),128))])]),_:1})])]),_:1})]),_:1},8,["modelValue"]),l(G,{modelValue:g.value,"onUpdate:modelValue":e[5]||(e[5]=o=>g.value=o),title:H.value,width:"800px",onClose:ee},{footer:t(()=>[n("div",Se,[l(p,{onClick:e[4]||(e[4]=o=>g.value=!1)},{default:t(()=>e[24]||(e[24]=[a("取消",-1)])),_:1,__:[24]}),l(p,{type:"primary",onClick:Z,loading:B.value},{default:t(()=>e[25]||(e[25]=[a(" 確定 ",-1)])),_:1,__:[25]},8,["loading"])])]),default:t(()=>[l(pe,{ref_key:"formRef",ref:L,model:i,rules:X,"label-width":"100px"},{default:t(()=>[l($,{label:"角色名稱",prop:"name"},{default:t(()=>[l(K,{modelValue:i.name,"onUpdate:modelValue":e[2]||(e[2]=o=>i.name=o),placeholder:"請輸入角色名稱"},null,8,["modelValue"])]),_:1}),l($,{label:"是否為根權限"},{default:t(()=>[l(_e,{modelValue:i.isRoot,"onUpdate:modelValue":e[3]||(e[3]=o=>i.isRoot=o),disabled:i.id&&i.isRoot},null,8,["modelValue","disabled"]),e[19]||(e[19]=n("div",{class:"form-tip"},"根權限擁有所有功能的完整權限",-1))]),_:1,__:[19]}),i.isRoot?F("",!0):(d(),z($,{key:0,label:"功能權限"},{default:t(()=>[n("div",ze,[(d(),_(h,null,T(V,o=>n("div",{key:o.key,class:"permission-row"},[n("div",Fe,f(o.name),1),l(q,{modelValue:i.permissions[o.key],"onUpdate:modelValue":m=>i.permissions[o.key]=m},{default:t(()=>[l(c,{label:"r"},{default:t(()=>e[20]||(e[20]=[a("讀取",-1)])),_:1,__:[20]}),l(c,{label:"c"},{default:t(()=>e[21]||(e[21]=[a("創建",-1)])),_:1,__:[21]}),l(c,{label:"u"},{default:t(()=>e[22]||(e[22]=[a("更新",-1)])),_:1,__:[22]}),l(c,{label:"d"},{default:t(()=>e[23]||(e[23]=[a("刪除",-1)])),_:1,__:[23]})]),_:2},1032,["modelValue","onUpdate:modelValue"])])),64))])]),_:1}))]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(G,{modelValue:D.value,"onUpdate:modelValue":e[6]||(e[6]=o=>D.value=o),title:"權限詳情",width:"600px"},{default:t(()=>[v.value?(d(),_("div",je,[n("h4",null,f(v.value.name),1),l(ce),v.value.isRoot?(d(),_("div",Ne,[l(O,{title:"根權限角色",type:"warning",closable:!1},{default:t(()=>e[26]||(e[26]=[a(" 此角色擁有系統所有功能的完整權限 ",-1)])),_:1,__:[26]})])):(d(),_("div",Ke,[(d(),_(h,null,T(V,o=>n("div",{key:o.key,class:"permission-detail-item"},[n("div",Me,f(o.name),1),n("div",Oe,[(d(!0),_(h,null,T(N(o.key),m=>(d(),z(C,{key:m,type:ie(m),size:"small",style:{"margin-right":"8px"}},{default:t(()=>[a(f(re(m)),1)]),_:2},1032,["type"]))),128)),N(o.key).length===0?(d(),_("span",qe," 無權限 ")):F("",!0)])])),64))]))])):F("",!0)]),_:1},8,["modelValue"])])}}});const Ye=we(Ge,[["__scopeId","data-v-5982997b"]]);export{Ye as default};
