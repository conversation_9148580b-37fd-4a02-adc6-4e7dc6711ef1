import{u as i}from"./dataService-42a3bdf6.js";import{d as c,G as V,e as b,f as r,g as t,w as o,p as n,c as x,m,_ as v}from"./index-329ed960.js";const N={class:"account-settings-container"},g={class:"settings-content"},y=c({__name:"index",setup(C){const d=i(),l=V({username:d.userInfo.account||"",staffName:d.userInfo.staffName||"",role:d.userInfo.isRoot?"管理員":"一般用戶"});return(I,e)=>{const u=n("el-input"),s=n("el-form-item"),_=n("el-button"),f=n("el-form"),p=n("el-card");return x(),b("div",N,[r("div",g,[t(p,null,{header:o(()=>e[3]||(e[3]=[r("div",{class:"card-header"},[r("span",null,"帳戶設定")],-1)])),default:o(()=>[t(f,{model:l,"label-width":"120px"},{default:o(()=>[t(s,{label:"用戶名"},{default:o(()=>[t(u,{modelValue:l.username,"onUpdate:modelValue":e[0]||(e[0]=a=>l.username=a),disabled:""},null,8,["modelValue"])]),_:1}),t(s,{label:"員工姓名"},{default:o(()=>[t(u,{modelValue:l.staffName,"onUpdate:modelValue":e[1]||(e[1]=a=>l.staffName=a)},null,8,["modelValue"])]),_:1}),t(s,{label:"角色"},{default:o(()=>[t(u,{modelValue:l.role,"onUpdate:modelValue":e[2]||(e[2]=a=>l.role=a),disabled:""},null,8,["modelValue"])]),_:1}),t(s,null,{default:o(()=>[t(_,{type:"primary"},{default:o(()=>e[4]||(e[4]=[m("保存設定",-1)])),_:1,__:[4]}),t(_,null,{default:o(()=>e[5]||(e[5]=[m("重置",-1)])),_:1,__:[5]})]),_:1})]),_:1},8,["model"])]),_:1})])])}}});const U=v(y,[["__scopeId","data-v-a7474a6b"]]);export{U as default};
