import{d as b,a as g,r as k,G as x,o as C,p as i,c,e as p,f as t,g as e,w as a,X as w,Y as T,n as v,a8 as B,t as o,_ as M}from"./index-329ed960.js";import{m as D,f as N,h as P,j as R}from"./index-0d74a956.js";const S={class:"database-container"},U={class:"module-content"},V={class:"stat-item"},j={class:"stat-value"},z={class:"stat-item"},E={class:"stat-value"},F={class:"stat-item"},G={class:"stat-value"},I={class:"stat-item"},L={class:"stat-value"},X=b({__name:"index",setup(Y){const m=g(),f=k([{key:"realtime",title:"即時資料",description:"查看即時數據和監控資訊",icon:D,path:"/plc/database/realtime"},{key:"history",title:"歷史報表",description:"查詢、排程和下載歷史數據",icon:N,path:"/plc/database/history"},{key:"runtime",title:"運轉時數",description:"設備運轉時間統計分析",icon:P,path:"/plc/database/runtime"},{key:"customReport",title:"匯出報表",description:"自定義報表匯出功能",icon:R,path:"/plc/database/custom-report"}]),d=x({totalTags:1248,activeTags:1156,dataPoints:"2.3M",lastUpdate:"2分鐘前"}),h=_=>{m.push(_)};return C(async()=>{}),(_,s)=>{const y=i("el-icon"),r=i("el-card"),l=i("el-col"),u=i("el-row");return c(),p("div",S,[s[6]||(s[6]=t("div",{class:"page-header"},[t("h2",null,"數據中心"),t("p",null,"統一管理系統數據，包含即時資料、歷史報表、運轉時數和自定義報表")],-1)),e(r,{class:"modules-card"},{header:a(()=>s[0]||(s[0]=[t("div",{class:"card-header"},[t("span",null,"功能模組")],-1)])),default:a(()=>[e(u,{gutter:20},{default:a(()=>[(c(!0),p(w,null,T(f.value,n=>(c(),v(l,{span:6,key:n.key},{default:a(()=>[e(r,{class:"module-card",shadow:"hover",onClick:$=>h(n.path)},{default:a(()=>[t("div",U,[e(y,{size:40,class:"module-icon"},{default:a(()=>[(c(),v(B(n.icon)))]),_:2},1024),t("h3",null,o(n.title),1),t("p",null,o(n.description),1)])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:1}),e(r,{class:"stats-card"},{header:a(()=>s[1]||(s[1]=[t("div",{class:"card-header"},[t("span",null,"系統概覽")],-1)])),default:a(()=>[e(u,{gutter:20},{default:a(()=>[e(l,{span:6},{default:a(()=>[t("div",V,[t("div",j,o(d.totalTags),1),s[2]||(s[2]=t("div",{class:"stat-label"},"總標籤數",-1))])]),_:1}),e(l,{span:6},{default:a(()=>[t("div",z,[t("div",E,o(d.activeTags),1),s[3]||(s[3]=t("div",{class:"stat-label"},"活躍標籤",-1))])]),_:1}),e(l,{span:6},{default:a(()=>[t("div",F,[t("div",G,o(d.dataPoints),1),s[4]||(s[4]=t("div",{class:"stat-label"},"數據點數",-1))])]),_:1}),e(l,{span:6},{default:a(()=>[t("div",I,[t("div",L,o(d.lastUpdate),1),s[5]||(s[5]=t("div",{class:"stat-label"},"最後更新",-1))])]),_:1})]),_:1})]),_:1})])}}});const H=M(X,[["__scopeId","data-v-916b4eaf"]]);export{H as default};
