import{d as I,a as U,r as d,p as o,c as h,e as G,g as e,w as t,f as n,m as k,_ as B}from"./index-329ed960.js";const N={class:"gui-setting-container"},L={class:"content"},R=I({__name:"setting",setup(S){const v=U(),u=d(!1),m=d(5),p=d(!1),b=d([{id:1,name:"消防系統監控",category:"圖形監控"},{id:2,name:"CCTV監控系統",category:"視頻監控"},{id:3,name:"電力系統監控",category:"圖形監控"},{id:4,name:"空調系統監控",category:"圖形監控"},{id:5,name:"照明系統監控",category:"圖形監控"}]),g=i=>{v.push({name:"PLCGUIMain",params:{id:i.toString()}})};return(i,l)=>{const s=o("el-table-column"),w=o("el-button"),V=o("el-table"),_=o("el-card"),c=o("el-col"),f=o("el-switch"),r=o("el-form-item"),y=o("el-input-number"),x=o("el-form"),C=o("el-row");return h(),G("div",N,[e(_,null,{header:t(()=>l[3]||(l[3]=[n("div",{class:"card-header"},[n("span",null,"GUI設定")],-1)])),default:t(()=>[n("div",L,[e(C,{gutter:20},{default:t(()=>[e(c,{span:12},{default:t(()=>[e(_,{shadow:"hover"},{header:t(()=>l[4]||(l[4]=[n("span",null,"可用的GUI監控頁面",-1)])),default:t(()=>[e(V,{data:b.value,style:{width:"100%"}},{default:t(()=>[e(s,{prop:"id",label:"ID",width:"80"}),e(s,{prop:"name",label:"名稱"}),e(s,{prop:"category",label:"類型"}),e(s,{label:"操作",width:"120"},{default:t(a=>[e(w,{type:"primary",size:"small",onClick:T=>g(a.row.id)},{default:t(()=>l[5]||(l[5]=[k(" 開啟 ",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),e(c,{span:12},{default:t(()=>[e(_,{shadow:"hover"},{header:t(()=>l[6]||(l[6]=[n("span",null,"GUI設定選項",-1)])),default:t(()=>[e(x,{"label-width":"120px"},{default:t(()=>[e(r,{label:"自動刷新"},{default:t(()=>[e(f,{modelValue:u.value,"onUpdate:modelValue":l[0]||(l[0]=a=>u.value=a)},null,8,["modelValue"])]),_:1}),e(r,{label:"刷新間隔"},{default:t(()=>[e(y,{modelValue:m.value,"onUpdate:modelValue":l[1]||(l[1]=a=>m.value=a),min:1,max:60,disabled:!u.value},null,8,["modelValue","disabled"]),l[7]||(l[7]=n("span",{style:{"margin-left":"8px"}},"秒",-1))]),_:1,__:[7]}),e(r,{label:"全螢幕模式"},{default:t(()=>[e(f,{modelValue:p.value,"onUpdate:modelValue":l[2]||(l[2]=a=>p.value=a)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])]),_:1})])}}});const D=B(R,[["__scopeId","data-v-d92866d6"]]);export{D as default};
