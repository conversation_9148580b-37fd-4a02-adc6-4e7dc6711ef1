import{M as d}from"./motion-7f60b73b.js";import{A as z,c as R,e as D,f as P,d as N,a9 as A,r as x,G as F,p,n as G,w as o,g as l,h as e,I as f,$ as m,m as c,t as g,b9 as k,b as L}from"./index-329ed960.js";import{I as M,K as H,u as h,a as K}from"./shield-keyhole-line-6938cc87.js";import{u as _}from"./hooks-7d897f55.js";import{L as C}from"./lock-fill-24793778.js";const W={viewBox:"0 0 24 24",width:"1em",height:"1em"};function j(U,r){return R(),D("svg",W,r[0]||(r[0]=[P("path",{fill:"currentColor",d:"M20 22H4v-2a5 5 0 0 1 5-5h6a5 5 0 0 1 5 5zm-8-9a6 6 0 1 1 0-12a6 6 0 0 1 0 12"},null,-1)]))}const q=z({name:"ri-user-3-fill",render:j}),J={class:"w-full flex justify-between"},ee=N({__name:"LoginRegist",setup(U){const{t:r}=A(),v=x(!1),i=x(!1),n=F({username:"",phone:"",verifyCode:"",password:"",repeatPassword:""}),V=x(),{isDisabled:B,text:b}=h(),I=[{validator:(w,a,t)=>{a===""?t(new Error(f(m("login.purePassWordSureReg")))):n.password!==a?t(new Error(f(m("login.purePassWordDifferentReg")))):t()},trigger:"blur"}],S=async w=>{i.value=!0,w&&await w.validate(a=>{a?v.value?setTimeout(()=>{k(f(m("login.pureRegisterSuccess")),{type:"success"}),i.value=!1},2e3):(i.value=!1,k(f(m("login.pureTickPrivacy")),{type:"warning"})):i.value=!1})};function E(){h().end(),L().SET_CURRENTPAGE(0)}return(w,a)=>{const t=p("el-input"),u=p("el-form-item"),y=p("el-button"),T=p("el-checkbox"),$=p("el-form");return R(),G($,{ref_key:"ruleFormRef",ref:V,model:n,rules:e(K),size:"large"},{default:o(()=>[l(e(d),null,{default:o(()=>[l(u,{rules:[{required:!0,message:e(f)(e(m)("login.pureUsernameReg")),trigger:"blur"}],prop:"username"},{default:o(()=>[l(t,{modelValue:n.username,"onUpdate:modelValue":a[0]||(a[0]=s=>n.username=s),clearable:"",placeholder:e(r)("login.pureUsername"),"prefix-icon":e(_)(e(q))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1},8,["rules"])]),_:1}),l(e(d),{delay:100},{default:o(()=>[l(u,{prop:"phone"},{default:o(()=>[l(t,{modelValue:n.phone,"onUpdate:modelValue":a[1]||(a[1]=s=>n.phone=s),clearable:"",placeholder:e(r)("login.purePhone"),"prefix-icon":e(_)(e(M))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),l(e(d),{delay:150},{default:o(()=>[l(u,{prop:"verifyCode"},{default:o(()=>[P("div",J,[l(t,{modelValue:n.verifyCode,"onUpdate:modelValue":a[2]||(a[2]=s=>n.verifyCode=s),clearable:"",placeholder:e(r)("login.pureSmsVerifyCode"),"prefix-icon":e(_)(e(H))},null,8,["modelValue","placeholder","prefix-icon"]),l(y,{disabled:e(B),class:"ml-2!",onClick:a[3]||(a[3]=s=>e(h)().start(V.value,"phone"))},{default:o(()=>[c(g(e(b).length>0?e(b)+e(r)("login.pureInfo"):e(r)("login.pureGetVerifyCode")),1)]),_:1},8,["disabled"])])]),_:1})]),_:1}),l(e(d),{delay:200},{default:o(()=>[l(u,{prop:"password"},{default:o(()=>[l(t,{modelValue:n.password,"onUpdate:modelValue":a[4]||(a[4]=s=>n.password=s),clearable:"","show-password":"",placeholder:e(r)("login.purePassword"),"prefix-icon":e(_)(e(C))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),l(e(d),{delay:250},{default:o(()=>[l(u,{rules:I,prop:"repeatPassword"},{default:o(()=>[l(t,{modelValue:n.repeatPassword,"onUpdate:modelValue":a[5]||(a[5]=s=>n.repeatPassword=s),clearable:"","show-password":"",placeholder:e(r)("login.pureSure"),"prefix-icon":e(_)(e(C))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),l(e(d),{delay:300},{default:o(()=>[l(u,null,{default:o(()=>[l(T,{modelValue:v.value,"onUpdate:modelValue":a[6]||(a[6]=s=>v.value=s)},{default:o(()=>[c(g(e(r)("login.pureReadAccept")),1)]),_:1},8,["modelValue"]),l(y,{link:"",type:"primary"},{default:o(()=>[c(g(e(r)("login.purePrivacyPolicy")),1)]),_:1})]),_:1})]),_:1}),l(e(d),{delay:350},{default:o(()=>[l(u,null,{default:o(()=>[l(y,{class:"w-full",size:"default",type:"primary",loading:i.value,onClick:a[7]||(a[7]=s=>S(V.value))},{default:o(()=>[c(g(e(r)("login.pureDefinite")),1)]),_:1},8,["loading"])]),_:1})]),_:1}),l(e(d),{delay:400},{default:o(()=>[l(u,null,{default:o(()=>[l(y,{class:"w-full",size:"default",onClick:E},{default:o(()=>[c(g(e(r)("login.pureBack")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])}}});export{q as U,ee as _};
