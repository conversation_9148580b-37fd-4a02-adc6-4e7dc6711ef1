import{d as c,p as t,c as d,e as r,g as s,w as n,f as e,_ as l}from"./index-329ed960.js";const p={class:"schedule-work-container"},i={class:"content"},m=c({__name:"work",setup(u){return(f,o)=>{const _=t("el-empty"),a=t("el-card");return d(),r("div",p,[s(a,null,{header:n(()=>o[0]||(o[0]=[e("div",{class:"card-header"},[e("span",null,"工作排程")],-1)])),default:n(()=>[e("div",i,[s(_,{description:"工作排程功能開發中..."})])]),_:1})])}}});const k=l(m,[["__scopeId","data-v-d984edf1"]]);export{k as default};
