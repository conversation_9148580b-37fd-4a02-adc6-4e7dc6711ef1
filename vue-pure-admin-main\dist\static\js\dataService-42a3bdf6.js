var y=Object.defineProperty;var A=(t,e,s)=>e in t?y(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var u=(t,e,s)=>(A(t,typeof e!="symbol"?e+"":e,s),s);import{y as w,a_ as L,aI as g,a$ as E,aK as x,b0 as D,b1 as l,b2 as b,b3 as d,E as i,aO as k,b4 as C}from"./index-329ed960.js";const I={login:t=>p.post("/Identity/Login",t),refreshToken:t=>p.post("/Identity/RefreshToken",t),logout:()=>p.post("/Identity/Logout"),validateToken:()=>p.get("/Identity/ValidateToken"),getUserInfo:()=>p.get("/Identity/UserInfo")},m=w("plc-auth",{state:()=>({customerId:"",account:"",staffId:"",permission:0,staffName:"",roleId:"",isRoot:!1,avatar:"",accessToken:"",refreshToken:"",expires:0,isLoggedIn:!1,loading:!1}),getters:{userInfo:t=>({customerId:t.customerId,account:t.account,staffId:t.staffId,permission:t.permission,staffName:t.staffName,roleId:t.roleId,isRoot:t.isRoot,avatar:t.avatar}),tokenInfo:t=>({accessToken:t.accessToken,refreshToken:t.refreshToken,expires:t.expires,userInfo:{customerId:t.customerId,account:t.account,staffId:t.staffId,permission:t.permission,staffName:t.staffName,roleId:t.roleId,isRoot:t.isRoot,avatar:t.avatar}}),hasPermission:t=>e=>t.isRoot?!0:(t.permission&e)===e},actions:{setAuthData(t){const{accessToken:e,refreshToken:s,expires:r,userInfo:o}=t;this.accessToken=e,this.refreshToken=s||"",this.expires=r,this.customerId=o.customerId,this.account=o.account,this.staffId=o.staffId,this.permission=o.permission,this.staffName=o.staffName||"",this.roleId=o.roleId||"",this.isRoot=o.isRoot||!1,this.avatar=o.avatar||"",this.isLoggedIn=!0,L(t)},loadAuthData(){const t=g();return t?(this.setAuthData(t),!0):!1},clearAuthData(){this.customerId="",this.account="",this.staffId="",this.permission=0,this.staffName="",this.roleId="",this.isRoot=!1,this.avatar="",this.accessToken="",this.refreshToken="",this.expires=0,this.isLoggedIn=!1,E()},async login(t){this.loading=!0;try{const e=await I.login(t),s={accessToken:e.accessToken,refreshToken:e.refreshToken,expires:new Date(e.expires).getTime(),userInfo:e.userInfo};return this.setAuthData(s),{success:!0,message:"登入成功",data:e}}catch(e){throw console.error("登入失敗:",e),new Error(e.message||"登入失敗，請檢查帳號密碼")}finally{this.loading=!1}},async logout(){this.loading=!0;try{return await I.logout(),this.clearAuthData(),{success:!0,message:"登出成功"}}catch(t){return console.error("登出失敗:",t),this.clearAuthData(),{success:!1,message:t.message||"登出失敗"}}finally{this.loading=!1}},async refreshToken(){if(!this.refreshToken)throw new Error("沒有 Refresh Token");try{const t=await I.refreshToken({refreshToken:this.refreshToken}),e={accessToken:t.accessToken,refreshToken:t.refreshToken,expires:new Date(t.expires).getTime(),userInfo:this.userInfo};return this.setAuthData(e),{success:!0,message:"Token 刷新成功",data:t}}catch(t){throw console.error("Token 刷新失敗:",t),this.clearAuthData(),new Error(t.message||"Token 刷新失敗")}},checkAuthStatus(){return x()}}}),S={baseURL:{}.VITE_API_BASE_URL||"http://192.168.1.152:8345",timeout:3e4,headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},paramsSerializer:{serialize:C.stringify}},a=class a{constructor(){u(this,"axiosInstance");this.axiosInstance=D.create(S),this.setupInterceptors()}setupInterceptors(){this.setupRequestInterceptor(),this.setupResponseInterceptor()}setupRequestInterceptor(){this.axiosInstance.interceptors.request.use(async e=>(l.start(),["/Identity/Login","/Identity/RefreshToken","/api/Staff/StaffLogin"].some(o=>e.url?.includes(o))?e:new Promise(o=>{const c=localStorage.getItem("access_token");if(c){e.headers.Authorization=`Bearer ${c}`,o(e);return}const n=g();n&&b(n.accessToken)?(e.headers.Authorization=d(n.accessToken),o(e)):n?.refreshToken&&!a.isRefreshing?(a.isRefreshing=!0,this.refreshToken(n.refreshToken).then(h=>{e.headers.Authorization=d(h),a.requests.forEach(R=>R(h)),a.requests=[],o(e)}).catch(()=>{this.handleAuthError(),o(e)}).finally(()=>{a.isRefreshing=!1})):a.isRefreshing?a.requests.push(h=>{e.headers.Authorization=d(h),o(e)}):o(e)})),e=>(l.done(),Promise.reject(e)))}setupResponseInterceptor(){this.axiosInstance.interceptors.response.use(e=>{l.done();const{data:s}=e;if(s&&typeof s=="object"&&"ReturnCode"in s)return Number(s.ReturnCode)!==1&&(s.Message,(s.ErrorCode==="AUTH_FAILED"||s.ErrorCode==="TOKEN_EXPIRED")&&this.handleAuthError()),e;if(s&&typeof s=="object"&&"Success"in s){if(!s.Success){const r=s.Message||"請求失敗";return i.error(r),(s.ErrorCode==="AUTH_FAILED"||s.ErrorCode==="TOKEN_EXPIRED")&&this.handleAuthError(),Promise.reject(new Error(r))}return{...e,data:s.Detail}}return e},e=>{if(l.done(),e.response){const{status:s,data:r}=e.response;switch(s){case 401:this.handleAuthError(),i.error("登入已過期，請重新登入");break;case 403:i.error("沒有權限執行此操作");break;case 404:i.error("請求的資源不存在");break;case 500:const o=r?.Message||r?.message||"伺服器內部錯誤";console.error("500 錯誤詳情:",r),i.error(o);break;default:const c=r?.Message||r?.message||`請求失敗 (${s})`;i.error(c)}}else e.request?i.error("網路連接失敗，請檢查網路設定"):i.error(e.message||"請求失敗");return Promise.reject(e)})}async refreshToken(e){try{const s=await this.axiosInstance.post("/api/Identity/RefreshToken",{refreshToken:e}),{accessToken:r,refreshToken:o,expires:c}=s.data,n=m(),h=n.userInfo;return n.setAuthData({accessToken:r,refreshToken:o,expires:new Date(c).getTime(),userInfo:h}),r}catch(s){throw console.error("刷新 Token 失敗:",s),s}}handleAuthError(){m().clearAuthData(),k.currentRoute.value.path!=="/login"&&k.push("/login")}get(e,s){return this.axiosInstance.get(e,s)}post(e,s,r){return this.axiosInstance.post(e,s,r)}put(e,s,r){return this.axiosInstance.put(e,s,r)}delete(e,s){return this.axiosInstance.delete(e,s)}patch(e,s,r){return this.axiosInstance.patch(e,s,r)}};u(a,"requests",[]),u(a,"isRefreshing",!1);let T=a;const f=new T;class P{constructor(e=""){u(this,"baseURL");this.baseURL=e}async get(e,s,r){const o={};return s&&(o.params=s),r&&(o.headers=r),(await f.get(`${this.baseURL}${e}`,o)).data}async post(e,s){let r={};return s instanceof URLSearchParams?r["Content-Type"]="application/x-www-form-urlencoded":s instanceof FormData?r["Content-Type"]="multipart/form-data":r["Content-Type"]="application/json",(await f.post(`${this.baseURL}${e}`,s,{headers:r})).data}async put(e,s){return(await f.put(`${this.baseURL}${e}`,s)).data}async delete(e){return(await f.delete(`${this.baseURL}${e}`)).data}async postForm(e,s){return(await f.post(`${this.baseURL}${e}`,s,{headers:{"Content-Type":"multipart/form-data"}})).data}}const p=new P("");export{p,m as u};
