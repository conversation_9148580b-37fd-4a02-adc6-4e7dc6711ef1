import{aG as m,d as _e,r as h,G as P,D as be,o as ve,p,q as ye,c as $,e as E,g as t,w as a,aL as we,X as O,Y as j,n as x,h as S,m as u,f as I,l as Se,t as F,j as Ve,E as i,aF as R,_ as Ce}from"./index-329ed960.js";import{a as he,r as G,v as $e,c as Ue,I as ke,e as Ie,E as qe}from"./index-0d74a956.js";import{p as K}from"./dataService-42a3bdf6.js";const L={getUserList:n=>K.get("/staff-members"),getAllUserList:()=>m.request("get","/api/staff-members/all"),createUser:n=>m.request("post","/api/staff-members",{data:n}),updateUser:n=>m.request("put",`/api/staff-members/${n.id}`,{data:n}),deleteUser:(n,r)=>{const g=r?`/api/staff/${r}/status/0`:`/api/staff-members/${n}/status/0`;return m.request("put",g)},permanentDeleteUser:(n,r)=>{const g=r?`/api/staff/${r}`:`/api/staff-members/${n}`;return m.request("delete",g)},toggleUserStatus:(n,r,g)=>{const v=g?`/api/staff/${g}/status/${r}`:`/api/staff-members/${n}/status/${r}`;return m.request("put",v)},batchOperation:n=>m.request("post","/api/staff-members/batch",{data:n}),exportUsers:n=>m.request("post","/api/staff-members/export",{data:n,responseType:"blob"}),importUsers:n=>{const r=new FormData;return r.append("file",n.file),r.append("overwriteExisting",n.overwriteExisting.toString()),m.request("post","/api/staff-members/import",{data:r,headers:{"Content-Type":"multipart/form-data"}})}},xe={getRoleList:()=>K.get("/roles"),getRoleDetail:n=>m.request("get",`/api/role/${n}`),createRole:n=>m.request("post","/api/role",{data:n}),updateRole:(n,r)=>m.request("put",`/api/role/${n}`,{data:r}),deleteRole:n=>m.request("delete",`/api/role/${n}`),batchDeleteRoles:n=>m.request("post","/api/roles/batch-delete",{data:{roleIds:n}}),checkRoleUsage:n=>m.request("get",`/api/role/${n}/usage`)},Re={class:"user-list-container"},De={class:"toolbar"},Te={class:"toolbar-left"},ze={class:"toolbar-right"},Be={class:"pagination-container"},Pe={class:"dialog-footer"},Ee=_e({__name:"list",setup(n){const r=h(!1),g=h(!1),v=h(!1),V=h([]),q=h([]),U=h([]),y=P({name:"",roleId:"",status:""}),f=P({currentPage:1,pageSize:20,total:0}),s=P({id:null,name:"",account:"",email:"",password:"",roleId:"",enableState:1}),D=h(),X={name:[{required:!0,message:"請輸入用戶名稱",trigger:"blur"}],account:[{required:!0,message:"請輸入帳號",trigger:"blur"}],email:[{required:!0,message:"請輸入電子郵件",trigger:"blur"},{type:"email",message:"請輸入正確的電子郵件格式",trigger:"blur"}],password:[{required:!0,message:"請輸入密碼",trigger:"blur"},{min:6,message:"密碼長度不能少於6位",trigger:"blur"}],roleId:[{required:!0,message:"請選擇權限角色",trigger:"change"}]},Y=be(()=>s.id?"編輯用戶":"新增用戶"),_=async()=>{try{r.value=!0;const l=await L.getUserList({page:f.currentPage,pageSize:f.pageSize});if(l&&l.Detail&&l.Detail.StaffMembers){const e=l.Detail.StaffMembers;q.value=e.map(d=>({id:d.StaffId,name:d.StaffName,account:d.Account,email:d.Email,enableState:d.EnableState,permission:{id:d.RoleId,name:d.RoleName}})),f.total=e.length,i.success(`成功載入 ${q.value.length} 個用戶`)}else q.value=[],f.total=0,i.warning("未找到用戶數據")}catch(l){console.error("載入用戶列表失敗:",l),i.error("載入用戶列表失敗")}finally{r.value=!1}},H=async()=>{try{const l=await xe.getRoleList();l&&l.Detail&&l.Detail.Roles?(U.value=l.Detail.Roles.map(e=>({id:e.RoleId,name:e.RoleName})),i.success(`成功載入 ${U.value.length} 個角色`)):(U.value=[],i.warning("未找到角色數據"))}catch(l){console.error("載入角色選項失敗:",l),i.error(`載入角色選項失敗: ${l.message||"未知錯誤"}`),U.value=[]}},T=()=>{f.currentPage=1,_()},J=()=>{Object.assign(y,{name:"",roleId:"",status:""}),T()},Q=()=>{Object.assign(s,{id:null,name:"",account:"",email:"",password:"",roleId:"",enableState:1}),v.value=!0},W=l=>{Object.assign(s,{id:l.id,name:l.name,account:l.account,email:l.email,password:"",roleId:l.permission.id,enableState:l.enableState}),v.value=!0},Z=async()=>{try{await D.value?.validate(),g.value=!0,s.id?(await L.updateUser({id:s.id,name:s.name,account:s.account,email:s.email,roleId:s.roleId,enableState:s.enableState}),i.success("用戶更新成功")):(await L.createUser({name:s.name,account:s.account,email:s.email,password:s.password,roleId:s.roleId,enableState:s.enableState}),i.success("用戶創建成功")),v.value=!1,_()}catch(l){console.error("提交失敗:",l)}finally{g.value=!1}},ee=()=>{D.value?.resetFields()},te=async l=>{try{const e=l.enableState===1?"停用":"啟用";await R.confirm(`確定要${e}用戶 "${l.name}" 嗎？`,"確認操作",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),await new Promise(d=>setTimeout(d,500)),i.success(`用戶${e}成功`),_()}catch(e){e!=="cancel"&&(console.error("切換狀態失敗:",e),i.error("操作失敗"))}},ae=async l=>{try{await R.confirm(`確定要刪除用戶 "${l.name}" 嗎？此操作不可恢復。`,"確認刪除",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,500)),i.success("用戶刪除成功"),_()}catch(e){e!=="cancel"&&(console.error("刪除失敗:",e),i.error("刪除失敗"))}},le=l=>{V.value=l},oe=async()=>{try{await R.confirm(`確定要啟用選中的 ${V.value.length} 個用戶嗎？`,"確認批量啟用",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),await new Promise(l=>setTimeout(l,1e3)),i.success(`成功啟用 ${V.value.length} 個用戶`),_()}catch(l){l!=="cancel"&&(console.error("批量啟用失敗:",l),i.error("批量啟用失敗"))}},ne=async()=>{try{await R.confirm(`確定要停用選中的 ${V.value.length} 個用戶嗎？`,"確認批量停用",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),await new Promise(l=>setTimeout(l,1e3)),i.success(`成功停用 ${V.value.length} 個用戶`),_()}catch(l){l!=="cancel"&&(console.error("批量停用失敗:",l),i.error("批量停用失敗"))}},se=()=>{i.info("匯出功能開發中...")},re=()=>{i.info("匯入功能開發中...")},ie=l=>{f.pageSize=l,_()},ue=l=>{f.currentPage=l,_()};return ve(()=>{_(),H()}),(l,e)=>{const d=p("el-input"),b=p("el-form-item"),k=p("el-option"),z=p("el-select"),w=p("el-icon"),c=p("el-button"),N=p("el-form"),B=p("el-card"),C=p("el-table-column"),de=p("el-tag"),pe=p("el-table"),me=p("el-pagination"),A=p("el-radio"),ce=p("el-radio-group"),fe=p("el-dialog"),ge=ye("loading");return $(),E("div",Re,[t(B,{class:"search-card"},{default:a(()=>[t(N,{model:y,inline:""},{default:a(()=>[t(b,{label:"用戶名稱"},{default:a(()=>[t(d,{modelValue:y.name,"onUpdate:modelValue":e[0]||(e[0]=o=>y.name=o),placeholder:"請輸入用戶名稱",clearable:"",onKeyup:we(T,["enter"])},null,8,["modelValue"])]),_:1}),t(b,{label:"權限角色"},{default:a(()=>[t(z,{modelValue:y.roleId,"onUpdate:modelValue":e[1]||(e[1]=o=>y.roleId=o),placeholder:"請選擇權限角色",clearable:""},{default:a(()=>[($(!0),E(O,null,j(U.value,o=>($(),x(k,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(b,{label:"狀態"},{default:a(()=>[t(z,{modelValue:y.status,"onUpdate:modelValue":e[2]||(e[2]=o=>y.status=o),placeholder:"請選擇狀態",clearable:""},{default:a(()=>[t(k,{label:"全部",value:""}),t(k,{label:"啟用",value:"1"}),t(k,{label:"停用",value:"0"})]),_:1},8,["modelValue"])]),_:1}),t(b,null,{default:a(()=>[t(c,{type:"primary",onClick:T},{default:a(()=>[t(w,null,{default:a(()=>[t(S(he))]),_:1}),e[13]||(e[13]=u(" 搜尋 ",-1))]),_:1,__:[13]}),t(c,{onClick:J},{default:a(()=>[t(w,null,{default:a(()=>[t(S(G))]),_:1}),e[14]||(e[14]=u(" 重置 ",-1))]),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),_:1}),t(B,{class:"toolbar-card"},{default:a(()=>[I("div",De,[I("div",Te,[t(c,{type:"primary",onClick:Q},{default:a(()=>[t(w,null,{default:a(()=>[t(S($e))]),_:1}),e[15]||(e[15]=u(" 新增用戶 ",-1))]),_:1,__:[15]}),t(c,{type:"success",disabled:V.value.length===0,onClick:oe},{default:a(()=>[t(w,null,{default:a(()=>[t(S(Ue))]),_:1}),e[16]||(e[16]=u(" 批量啟用 ",-1))]),_:1,__:[16]},8,["disabled"]),t(c,{type:"warning",disabled:V.value.length===0,onClick:ne},{default:a(()=>[t(w,null,{default:a(()=>[t(S(ke))]),_:1}),e[17]||(e[17]=u(" 批量停用 ",-1))]),_:1,__:[17]},8,["disabled"])]),I("div",ze,[t(c,{onClick:se},{default:a(()=>[t(w,null,{default:a(()=>[t(S(Ie))]),_:1}),e[18]||(e[18]=u(" 匯出 ",-1))]),_:1,__:[18]}),t(c,{onClick:re},{default:a(()=>[t(w,null,{default:a(()=>[t(S(qe))]),_:1}),e[19]||(e[19]=u(" 匯入 ",-1))]),_:1,__:[19]}),t(c,{onClick:_},{default:a(()=>[t(w,null,{default:a(()=>[t(S(G))]),_:1}),e[20]||(e[20]=u(" 重新載入 ",-1))]),_:1,__:[20]})])])]),_:1}),t(B,{class:"table-card"},{default:a(()=>[Se(($(),x(pe,{data:q.value,onSelectionChange:le,stripe:"",border:""},{default:a(()=>[t(C,{type:"selection",width:"55"}),t(C,{prop:"name",label:"用戶名稱","min-width":"120"}),t(C,{prop:"account",label:"帳號","min-width":"120"}),t(C,{prop:"email",label:"電子郵件","min-width":"180"}),t(C,{prop:"permission.name",label:"權限角色","min-width":"120"}),t(C,{label:"狀態",width:"100"},{default:a(({row:o})=>[t(de,{type:o.enableState===1?"success":"danger"},{default:a(()=>[u(F(o.enableState===1?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(C,{label:"操作",width:"200",fixed:"right"},{default:a(({row:o})=>[t(c,{type:"primary",size:"small",onClick:M=>W(o)},{default:a(()=>e[21]||(e[21]=[u(" 編輯 ",-1)])),_:2,__:[21]},1032,["onClick"]),t(c,{type:o.enableState===1?"warning":"success",size:"small",onClick:M=>te(o)},{default:a(()=>[u(F(o.enableState===1?"停用":"啟用"),1)]),_:2},1032,["type","onClick"]),t(c,{type:"danger",size:"small",onClick:M=>ae(o)},{default:a(()=>e[22]||(e[22]=[u(" 刪除 ",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ge,r.value]]),I("div",Be,[t(me,{"current-page":f.currentPage,"onUpdate:currentPage":e[3]||(e[3]=o=>f.currentPage=o),"page-size":f.pageSize,"onUpdate:pageSize":e[4]||(e[4]=o=>f.pageSize=o),total:f.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ie,onCurrentChange:ue},null,8,["current-page","page-size","total"])])]),_:1}),t(fe,{modelValue:v.value,"onUpdate:modelValue":e[12]||(e[12]=o=>v.value=o),title:Y.value,width:"600px",onClose:ee},{footer:a(()=>[I("div",Pe,[t(c,{onClick:e[11]||(e[11]=o=>v.value=!1)},{default:a(()=>e[25]||(e[25]=[u("取消",-1)])),_:1,__:[25]}),t(c,{type:"primary",onClick:Z,loading:g.value},{default:a(()=>e[26]||(e[26]=[u(" 確定 ",-1)])),_:1,__:[26]},8,["loading"])])]),default:a(()=>[t(N,{ref_key:"formRef",ref:D,model:s,rules:X,"label-width":"100px"},{default:a(()=>[t(b,{label:"用戶名稱",prop:"name"},{default:a(()=>[t(d,{modelValue:s.name,"onUpdate:modelValue":e[5]||(e[5]=o=>s.name=o),placeholder:"請輸入用戶名稱"},null,8,["modelValue"])]),_:1}),t(b,{label:"帳號",prop:"account"},{default:a(()=>[t(d,{modelValue:s.account,"onUpdate:modelValue":e[6]||(e[6]=o=>s.account=o),placeholder:"請輸入帳號"},null,8,["modelValue"])]),_:1}),t(b,{label:"電子郵件",prop:"email"},{default:a(()=>[t(d,{modelValue:s.email,"onUpdate:modelValue":e[7]||(e[7]=o=>s.email=o),placeholder:"請輸入電子郵件"},null,8,["modelValue"])]),_:1}),s.id?Ve("",!0):($(),x(b,{key:0,label:"密碼",prop:"password"},{default:a(()=>[t(d,{modelValue:s.password,"onUpdate:modelValue":e[8]||(e[8]=o=>s.password=o),type:"password",placeholder:"請輸入密碼","show-password":""},null,8,["modelValue"])]),_:1})),t(b,{label:"權限角色",prop:"roleId"},{default:a(()=>[t(z,{modelValue:s.roleId,"onUpdate:modelValue":e[9]||(e[9]=o=>s.roleId=o),placeholder:"請選擇權限角色"},{default:a(()=>[($(!0),E(O,null,j(U.value,o=>($(),x(k,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(b,{label:"狀態",prop:"enableState"},{default:a(()=>[t(ce,{modelValue:s.enableState,"onUpdate:modelValue":e[10]||(e[10]=o=>s.enableState=o)},{default:a(()=>[t(A,{label:1},{default:a(()=>e[23]||(e[23]=[u("啟用",-1)])),_:1,__:[23]}),t(A,{label:0},{default:a(()=>e[24]||(e[24]=[u("停用",-1)])),_:1,__:[24]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const Me=Ce(Ee,[["__scopeId","data-v-bb6c0631"]]);export{Me as default};
