import{a as J}from"./index-0d74a956.js";import{u as O}from"./dataService-42a3bdf6.js";import{d as W,r as u,G as V,D as Z,p as d,q as ee,c as p,e as R,f as s,n as v,w as e,X as H,Y as M,m as i,t as n,j as D,g as a,h as S,aH as $,l as le,a0 as ae}from"./index-329ed960.js";const te={class:"runtime-container"},se={class:"common-search-list"},oe={style:{float:"left"}},de={style:{float:"right",color:"#8492a6","font-size":"13px"}},ne={class:"summary-item"},ie={class:"summary-value"},re={class:"summary-item"},ue={class:"summary-value"},me={class:"summary-item"},pe={class:"summary-value"},fe={class:"summary-item"},ce={class:"summary-value"},_e={class:"card-header"},ve={class:"header-actions"},we=W({__name:"runtime",setup(ge){O();const A=u(),B=u(),k=u(!1),L=u(!1),q=u(""),y=u(!1),r=V({selectedTags:[],dateRange:[],statisticType:"total",threshold:50}),C=V({name:""}),z={selectedTags:[{required:!0,message:"請選擇至少一個標籤",trigger:"change"}],dateRange:[{required:!0,message:"請選擇時間範圍",trigger:"change"}]},I=u([]),j=u([]),G=u([]),Y=u([]),b=V({totalDevices:0,totalRuntime:"0小時",avgRuntime:"0小時",efficiency:0}),h=Z(()=>I.value.length>0);return(o,l)=>{const E=d("el-tag"),w=d("el-card"),g=d("el-option"),F=d("el-select"),_=d("el-form-item"),f=d("el-col"),K=d("el-date-picker"),T=d("el-row"),P=d("el-input-number"),c=d("el-button"),U=d("el-input"),m=d("el-table-column"),Q=d("el-table"),X=d("el-dialog"),x=ee("loading");return p(),R("div",te,[l[23]||(l[23]=s("div",{class:"page-header"},[s("h2",null,"運轉時數"),s("p",null,"設備運轉時間統計分析，支援常用搜尋和數據匯出")],-1)),Y.value.length>0?(p(),v(w,{key:0,class:"common-search-card"},{header:e(()=>l[9]||(l[9]=[s("div",{class:"card-header"},[s("span",null,"常用搜尋")],-1)])),default:e(()=>[s("div",se,[(p(!0),R(H,null,M(Y.value,t=>(p(),v(E,{key:t.id,class:"common-search-tag",closable:"",onClick:N=>o.useCommonSearch(t),onClose:N=>o.deleteCommonSearch(t)},{default:e(()=>[i(n(t.name),1)]),_:2},1032,["onClick","onClose"]))),128))])]),_:1})):D("",!0),a(w,{class:"query-card"},{header:e(()=>l[10]||(l[10]=[s("div",{class:"card-header"},[s("span",null,"查詢條件")],-1)])),default:e(()=>[a(S($),{ref_key:"queryFormRef",ref:A,model:r,rules:z,"label-width":"120px",class:"query-form"},{default:e(()=>[a(T,{gutter:20},{default:e(()=>[a(f,{span:12},{default:e(()=>[a(_,{label:"選擇標籤",prop:"selectedTags"},{default:e(()=>[a(F,{modelValue:r.selectedTags,"onUpdate:modelValue":l[0]||(l[0]=t=>r.selectedTags=t),multiple:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"請選擇要查詢的標籤","remote-method":o.searchTags,loading:L.value,style:{width:"100%"}},{default:e(()=>[(p(!0),R(H,null,M(G.value,t=>(p(),v(g,{key:t.id,label:t.name,value:t.id},{default:e(()=>[s("span",oe,n(t.name),1),s("span",de,n(t.description),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading"])]),_:1})]),_:1}),a(f,{span:12},{default:e(()=>[a(_,{label:"時間範圍",prop:"dateRange"},{default:e(()=>[a(K,{modelValue:r.dateRange,"onUpdate:modelValue":l[1]||(l[1]=t=>r.dateRange=t),type:"datetimerange","range-separator":"至","start-placeholder":"開始時間","end-placeholder":"結束時間",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(T,{gutter:20},{default:e(()=>[a(f,{span:12},{default:e(()=>[a(_,{label:"統計方式"},{default:e(()=>[a(F,{modelValue:r.statisticType,"onUpdate:modelValue":l[2]||(l[2]=t=>r.statisticType=t),style:{width:"100%"}},{default:e(()=>[a(g,{label:"總運轉時數",value:"total"}),a(g,{label:"日平均運轉時數",value:"daily_avg"}),a(g,{label:"週平均運轉時數",value:"weekly_avg"}),a(g,{label:"月平均運轉時數",value:"monthly_avg"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(f,{span:12},{default:e(()=>[a(_,{label:"運轉閾值"},{default:e(()=>[a(P,{modelValue:r.threshold,"onUpdate:modelValue":l[3]||(l[3]=t=>r.threshold=t),min:0,max:100,precision:2,style:{width:"100%"},placeholder:"設定運轉判斷閾值"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(_,null,{default:e(()=>[a(c,{type:"primary",loading:k.value,disabled:!r.selectedTags.length||!r.dateRange,onClick:o.handleQuery},{default:e(()=>[i(n(k.value?"查詢中...":"開始查詢"),1)]),_:1},8,["loading","disabled","onClick"]),a(c,{type:"success",disabled:!h.value,onClick:o.exportData},{default:e(()=>l[11]||(l[11]=[i(" 匯出數據 ",-1)])),_:1,__:[11]},8,["disabled","onClick"]),a(c,{type:"info",disabled:!r.selectedTags.length,onClick:l[4]||(l[4]=t=>y.value=!0)},{default:e(()=>l[12]||(l[12]=[i(" 加入常用搜尋 ",-1)])),_:1,__:[12]},8,["disabled"]),a(c,{type:"warning",disabled:!h.value,onClick:o.clearData},{default:e(()=>l[13]||(l[13]=[i(" 清除數據 ",-1)])),_:1,__:[13]},8,["disabled","onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),h.value?(p(),v(w,{key:1,class:"summary-card"},{header:e(()=>l[14]||(l[14]=[s("div",{class:"card-header"},[s("span",null,"統計摘要")],-1)])),default:e(()=>[a(T,{gutter:20},{default:e(()=>[a(f,{span:6},{default:e(()=>[s("div",ne,[s("div",ie,n(b.totalDevices),1),l[15]||(l[15]=s("div",{class:"summary-label"},"設備總數",-1))])]),_:1}),a(f,{span:6},{default:e(()=>[s("div",re,[s("div",ue,n(b.totalRuntime),1),l[16]||(l[16]=s("div",{class:"summary-label"},"總運轉時數",-1))])]),_:1}),a(f,{span:6},{default:e(()=>[s("div",me,[s("div",pe,n(b.avgRuntime),1),l[17]||(l[17]=s("div",{class:"summary-label"},"平均運轉時數",-1))])]),_:1}),a(f,{span:6},{default:e(()=>[s("div",fe,[s("div",ce,n(b.efficiency)+"%",1),l[18]||(l[18]=s("div",{class:"summary-label"},"運轉效率",-1))])]),_:1})]),_:1})]),_:1})):D("",!0),h.value?(p(),v(w,{key:2,class:"data-table-card"},{header:e(()=>[s("div",_e,[l[19]||(l[19]=s("span",null,"運轉時數統計",-1)),s("div",ve,[a(U,{modelValue:q.value,"onUpdate:modelValue":l[5]||(l[5]=t=>q.value=t),placeholder:"搜尋設備名稱...","prefix-icon":S(J),clearable:"",style:{width:"250px"},onInput:o.handleSearch},null,8,["modelValue","prefix-icon","onInput"])])])]),default:e(()=>[le((p(),v(Q,{ref_key:"dataTableRef",ref:B,data:j.value,height:"500",stripe:"",border:"","row-class-name":o.getRowClassName},{default:e(()=>[a(m,{prop:"TagName",label:"設備名稱",width:"200",fixed:"left"}),a(m,{prop:"TotalRuntime",label:"總運轉時數",width:"150",align:"right"},{default:e(({row:t})=>[i(n(o.formatRuntime(t.TotalRuntime)),1)]),_:1}),a(m,{prop:"AvgRuntime",label:"平均運轉時數",width:"150",align:"right"},{default:e(({row:t})=>[i(n(o.formatRuntime(t.AvgRuntime)),1)]),_:1}),a(m,{prop:"Efficiency",label:"運轉效率",width:"120",align:"right"},{default:e(({row:t})=>[s("span",{class:ae(o.getEfficiencyClass(t.Efficiency))},n(t.Efficiency.toFixed(2))+"% ",3)]),_:1}),a(m,{prop:"StartTime",label:"開始時間",width:"180"},{default:e(({row:t})=>[i(n(o.formatDateTime(t.StartTime)),1)]),_:1}),a(m,{prop:"EndTime",label:"結束時間",width:"180"},{default:e(({row:t})=>[i(n(o.formatDateTime(t.EndTime)),1)]),_:1}),a(m,{prop:"Status",label:"狀態",width:"100"},{default:e(({row:t})=>[a(E,{type:o.getStatusType(t.Status)},{default:e(()=>[i(n(o.getStatusText(t.Status)),1)]),_:2},1032,["type"])]),_:1}),a(m,{prop:"Description",label:"描述","min-width":"200"}),a(m,{label:"操作",width:"120",fixed:"right"},{default:e(({row:t})=>[a(c,{type:"primary",size:"small",onClick:N=>o.viewDetail(t)},{default:e(()=>l[20]||(l[20]=[i(" 詳情 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data","row-class-name"])),[[x,k.value]])]),_:1})):D("",!0),a(X,{modelValue:y.value,"onUpdate:modelValue":l[8]||(l[8]=t=>y.value=t),title:"新增常用搜尋",width:"400px"},{footer:e(()=>[a(c,{onClick:l[7]||(l[7]=t=>y.value=!1)},{default:e(()=>l[21]||(l[21]=[i("取消",-1)])),_:1,__:[21]}),a(c,{type:"primary",onClick:o.addCommonSearch},{default:e(()=>l[22]||(l[22]=[i("保存",-1)])),_:1,__:[22]},8,["onClick"])]),default:e(()=>[a(S($),{model:C,"label-width":"80px"},{default:e(()=>[a(_,{label:"名稱",required:""},{default:e(()=>[a(U,{modelValue:C.name,"onUpdate:modelValue":l[6]||(l[6]=t=>C.name=t),placeholder:"請輸入搜尋名稱"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{we as default};
