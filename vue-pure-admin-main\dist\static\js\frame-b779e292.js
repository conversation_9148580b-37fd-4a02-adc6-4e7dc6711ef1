import{d,a9 as v,r as c,u as p,h as t,R as h,o as g,q as x,l as I,c as R,e as k,f as y,a3 as L,_ as S}from"./index-329ed960.js";const B=["element-loading-text"],E=["src"],P=d({name:"LayFrame",__name:"frame",props:{frameInfo:{}},setup(i){const r=i,{t:m}=v(),s=c(!0),a=p(),n=c(""),l=c(null);t(a.meta)?.frameSrc&&(n.value=t(a.meta)?.frameSrc),t(a.meta)?.frameLoading===!1&&o();function o(){s.value=!1}function u(){L(()=>{const e=t(l);if(!e)return;const f=e;f.attachEvent?f.attachEvent("onload",()=>{o()}):e.onload=()=>{o()}})}return h(()=>a.fullPath,e=>{a.name==="Redirect"&&e.includes(r.frameInfo?.fullPath)&&(n.value=e,s.value=!0),r.frameInfo?.fullPath===e&&(n.value=r.frameInfo?.frameSrc)}),g(()=>{u()}),(e,f)=>{const _=x("loading");return I((R(),k("div",{class:"frame","element-loading-text":t(m)("status.pureLoad")},[y("iframe",{ref_key:"frameRef",ref:l,src:n.value,class:"frame-iframe"},null,8,E)],8,B)),[[_,s.value]])}}});const D=S(P,[["__scopeId","data-v-5296aa19"]]);export{D as default};
