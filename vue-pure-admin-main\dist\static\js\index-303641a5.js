import{u as C}from"./hooks-7d897f55.js";import{d as M,r as u,ap as S,B as $,am as h,aD as B,R as y,g as s,Z as p,a3 as i,l as A,q as L,aE as R,ao as k,aw as X}from"./index-329ed960.js";const q={options:{type:Array,default:()=>[]},modelValue:{type:void 0,require:!1,default:"0"},block:{type:Boolean,default:!1},size:{type:String},disabled:{type:Boolean,default:!1},resize:{type:Boolean,default:!1}},W=M({name:"ReSegmented",props:q,emits:["change","update:modelValue"],setup(l,{emit:c}){const m=u(0),f=u(0),{isDark:v}=S(),b=u(!1),d=u(-1),r=u(""),z=$(),n=h(l.modelValue)?B(l,"modelValue"):u(0);function D({option:e,index:a},t){l.disabled||e.disabled||(t.preventDefault(),h(l.modelValue)?c("update:modelValue",a):n.value=a,r.value="",c("change",{index:a,option:e}))}function I({option:e,index:a},t){l.disabled||(t.preventDefault(),d.value=a,e.disabled||n.value===a?r.value="":r.value=v.value?"#1f1f1f":"rgba(0, 0, 0, 0.06)")}function V(e,a){l.disabled||(a.preventDefault(),d.value=-1)}function g(e=n.value){i(()=>{const a=z?.proxy?.$refs[`labelRef${e}`];a&&(m.value=a.clientWidth,f.value=a.offsetLeft,b.value=!0)})}function o(){p(".pure-segmented",()=>{i(()=>{g(n.value)})})}(l.block||l.resize)&&o(),y(()=>n.value,e=>{i(()=>{g(e)})},{immediate:!0}),y(()=>l.size,o,{immediate:!0});const w=()=>l.options.map((e,a)=>s("label",{ref:`labelRef${a}`,class:["pure-segmented-item",(l.disabled||e?.disabled)&&"pure-segmented-item-disabled"],style:{background:d.value===a?r.value:"",color:l.disabled?null:!e.disabled&&(n.value===a||d.value===a)?v.value?"rgba(255, 255, 255, 0.85)":"rgba(0,0,0,.88)":""},onMouseenter:t=>I({option:e,index:a},t),onMouseleave:t=>V({option:e,index:a},t),onClick:t=>D({option:e,index:a},t)},[s("input",{type:"radio",name:"segmented"},null),A(s("div",{class:"pure-segmented-item-label"},[e.icon&&!R(e.label)?s("span",{class:"pure-segmented-item-icon",style:{marginRight:e.label?"6px":0}},[k(C(e.icon,{...e?.iconAttrs}))]):null,e.label?R(e.label)?k(e.label):s("span",null,[e.label]):null]),[[L("tippy"),{content:e?.tip,zIndex:41e3}]])]));return()=>s("div",{class:{"pure-segmented":!0,"pure-segmented-block":l.block,"pure-segmented--large":l.size==="large","pure-segmented--small":l.size==="small"}},[s("div",{class:"pure-segmented-group"},[s("div",{class:"pure-segmented-item-selected",style:{width:`${m.value}px`,transform:`translateX(${f.value}px)`,display:b.value?"block":"none"}},null),w()])])}}),N=X(W);export{N as R};
