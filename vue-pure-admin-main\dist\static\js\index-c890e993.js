import{d as N,a as q,G as b,o as $,p as m,c as B,e as I,f as s,g as t,w as o,h as a,t as i,m as c,E as V,_ as G}from"./index-329ed960.js";import{n as C,o as h,p as L,s as S,q as g,t as T,v as U,x as A,b as D}from"./index-0d74a956.js";const H={class:"notify-container"},R={class:"stats-grid"},j={class:"stat-content"},z={class:"stat-icon group-icon"},F={class:"stat-info"},J={class:"stat-number"},K={class:"stat-content"},O={class:"stat-icon message-icon"},P={class:"stat-info"},Q={class:"stat-number"},W={class:"stat-content"},X={class:"stat-icon member-icon"},Y={class:"stat-info"},Z={class:"stat-number"},ss={class:"stat-content"},ts={class:"stat-icon service-icon"},es={class:"stat-info"},os={class:"stat-number"},ls={class:"module-grid"},as={class:"module-content"},ns={class:"module-icon"},ds={class:"module-arrow"},is={class:"module-content"},us={class:"module-icon"},cs={class:"module-arrow"},_s={class:"module-content"},rs={class:"module-icon"},vs={class:"module-arrow"},ms={class:"quick-actions"},fs={class:"action-buttons"},ps={class:"system-status"},gs={class:"status-grid"},ys={class:"status-item"},bs={class:"status-item"},Cs={class:"status-item"},hs={class:"status-item"},Ss={class:"status-time"},ks=N({__name:"index",setup(ws){const _=q(),d=b({groupCount:0,messageCount:0,memberCount:0,serviceCount:0}),u=b({lineService:!0,emailService:!0,smsService:!1,lastUpdate:"2024-01-20 10:30:00"}),f=r=>{_.push(`/plc-notify/${r}`)},k=()=>{_.push("/plc-notify/message?action=send")},w=()=>{_.push("/plc-notify/group?action=add")},x=()=>{_.push("/plc-notify/message?tab=history")},E=()=>{V.info("通知測試功能開發中...")},M=async()=>{try{d.groupCount=8,d.messageCount=45,d.memberCount=32,d.serviceCount=3}catch(r){console.error("載入系統統計失敗:",r)}};return $(async()=>{await M()}),(r,e)=>{const l=m("el-icon"),n=m("el-card"),v=m("el-button"),p=m("el-tag");return B(),I("div",H,[e[20]||(e[20]=s("div",{class:"page-header"},[s("h1",null,"通知系統"),s("p",null,"管理系統通知群組、訊息發送和通知設定")],-1)),s("div",R,[t(n,{class:"stat-card"},{default:o(()=>[s("div",j,[s("div",z,[t(l,null,{default:o(()=>[t(a(C))]),_:1})]),s("div",F,[s("div",J,i(d.groupCount),1),e[3]||(e[3]=s("div",{class:"stat-label"},"通知群組",-1))])])]),_:1}),t(n,{class:"stat-card"},{default:o(()=>[s("div",K,[s("div",O,[t(l,null,{default:o(()=>[t(a(h))]),_:1})]),s("div",P,[s("div",Q,i(d.messageCount),1),e[4]||(e[4]=s("div",{class:"stat-label"},"今日訊息",-1))])])]),_:1}),t(n,{class:"stat-card"},{default:o(()=>[s("div",W,[s("div",X,[t(l,null,{default:o(()=>[t(a(L))]),_:1})]),s("div",Y,[s("div",Z,i(d.memberCount),1),e[5]||(e[5]=s("div",{class:"stat-label"},"群組成員",-1))])])]),_:1}),t(n,{class:"stat-card"},{default:o(()=>[s("div",ss,[s("div",ts,[t(l,null,{default:o(()=>[t(a(S))]),_:1})]),s("div",es,[s("div",os,i(d.serviceCount),1),e[6]||(e[6]=s("div",{class:"stat-label"},"通知服務",-1))])])]),_:1})]),s("div",ls,[t(n,{class:"module-card",shadow:"hover",onClick:e[0]||(e[0]=y=>f("group"))},{default:o(()=>[s("div",as,[s("div",ns,[t(l,null,{default:o(()=>[t(a(C))]),_:1})]),e[7]||(e[7]=s("div",{class:"module-info"},[s("h3",null,"群組管理"),s("p",null,"管理通知群組、成員設定和群組權限")],-1)),s("div",ds,[t(l,null,{default:o(()=>[t(a(g))]),_:1})])])]),_:1}),t(n,{class:"module-card",shadow:"hover",onClick:e[1]||(e[1]=y=>f("message"))},{default:o(()=>[s("div",is,[s("div",us,[t(l,null,{default:o(()=>[t(a(h))]),_:1})]),e[8]||(e[8]=s("div",{class:"module-info"},[s("h3",null,"訊息管理"),s("p",null,"發送通知訊息、查看歷史記錄和訊息統計")],-1)),s("div",cs,[t(l,null,{default:o(()=>[t(a(g))]),_:1})])])]),_:1}),t(n,{class:"module-card",shadow:"hover",onClick:e[2]||(e[2]=y=>f("setting"))},{default:o(()=>[s("div",_s,[s("div",rs,[t(l,null,{default:o(()=>[t(a(S))]),_:1})]),e[9]||(e[9]=s("div",{class:"module-info"},[s("h3",null,"通知設定"),s("p",null,"設定 LINE、Email、SMS 等通知服務參數")],-1)),s("div",vs,[t(l,null,{default:o(()=>[t(a(g))]),_:1})])])]),_:1})]),s("div",ms,[t(n,null,{header:o(()=>e[10]||(e[10]=[s("span",null,"快速操作",-1)])),default:o(()=>[s("div",fs,[t(v,{type:"primary",onClick:k},{default:o(()=>[t(l,null,{default:o(()=>[t(a(T))]),_:1}),e[11]||(e[11]=c(" 快速發送 ",-1))]),_:1,__:[11]}),t(v,{type:"success",onClick:w},{default:o(()=>[t(l,null,{default:o(()=>[t(a(U))]),_:1}),e[12]||(e[12]=c(" 新增群組 ",-1))]),_:1,__:[12]}),t(v,{onClick:x},{default:o(()=>[t(l,null,{default:o(()=>[t(a(A))]),_:1}),e[13]||(e[13]=c(" 查看歷史 ",-1))]),_:1,__:[13]}),t(v,{onClick:E},{default:o(()=>[t(l,null,{default:o(()=>[t(a(D))]),_:1}),e[14]||(e[14]=c(" 測試通知 ",-1))]),_:1,__:[14]})])]),_:1})]),s("div",ps,[t(n,null,{header:o(()=>e[15]||(e[15]=[s("span",null,"服務狀態",-1)])),default:o(()=>[s("div",gs,[s("div",ys,[e[16]||(e[16]=s("span",{class:"status-label"},"LINE 服務",-1)),t(p,{type:u.lineService?"success":"danger"},{default:o(()=>[c(i(u.lineService?"正常":"異常"),1)]),_:1},8,["type"])]),s("div",bs,[e[17]||(e[17]=s("span",{class:"status-label"},"Email 服務",-1)),t(p,{type:u.emailService?"success":"danger"},{default:o(()=>[c(i(u.emailService?"正常":"異常"),1)]),_:1},8,["type"])]),s("div",Cs,[e[18]||(e[18]=s("span",{class:"status-label"},"SMS 服務",-1)),t(p,{type:u.smsService?"success":"danger"},{default:o(()=>[c(i(u.smsService?"正常":"異常"),1)]),_:1},8,["type"])]),s("div",hs,[e[19]||(e[19]=s("span",{class:"status-label"},"最後更新",-1)),s("span",Ss,i(u.lastUpdate),1)])])]),_:1})])])}}});const Ms=G(ks,[["__scopeId","data-v-85d0950d"]]);export{Ms as default};
