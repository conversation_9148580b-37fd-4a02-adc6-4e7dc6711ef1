import{u as te}from"./dataService-42a3bdf6.js";import{d as le,r as p,G as F,p as u,q as ae,c as k,e as M,f as o,g as e,w as t,m as d,l as $,n as U,t as r,h as z,aH as q,X as oe,Y as de,j as ne}from"./index-329ed960.js";const se={class:"bill-container"},re={class:"tab-content"},ie={class:"card-header"},ue={class:"tab-content"},pe={class:"card-header"},me={class:"tab-content"},fe={class:"card-header"},_e={class:"tab-content"},ge={key:0,class:"bill-result"},ve={class:"result-item"},be={class:"result-value"},ce={class:"result-item"},ye={class:"result-value"},ke={class:"result-item"},we={class:"result-value"},Ce={class:"result-item"},he={class:"result-value"},Ve={class:"tab-content"},Ye=le({__name:"bill",setup(De){te();const P=p(),S=p(),L=p("contract"),W=p(!1),E=p(!1),A=p(!1),B=p(!1),I=p(!1),V=p(!1),j=p(!1),G=p(!1),H=p([]),N=p([]),K=p([]),m=F({id:"",name:"",contractType:"",capacity:0,startDate:"",endDate:"",status:"active"}),C=F({meterId:"",dateRange:[]}),b=F({reportType:"monthly",format:"xlsx",dateRange:[]}),c=p(null),X={name:[{required:!0,message:"請輸入契約名稱",trigger:"blur"}],contractType:[{required:!0,message:"請選擇契約類型",trigger:"change"}],capacity:[{required:!0,message:"請輸入契約容量",trigger:"blur"}],startDate:[{required:!0,message:"請選擇開始日期",trigger:"change"}],endDate:[{required:!0,message:"請選擇結束日期",trigger:"change"}]},J={meterId:[{required:!0,message:"請選擇電表",trigger:"change"}],dateRange:[{required:!0,message:"請選擇計算期間",trigger:"change"}]};return(s,l)=>{const i=u("el-button"),n=u("el-table-column"),w=u("el-tag"),D=u("el-table"),y=u("el-card"),h=u("el-tab-pane"),_=u("el-option"),R=u("el-select"),f=u("el-form-item"),g=u("el-col"),T=u("el-date-picker"),Y=u("el-row"),O=u("el-tabs"),Q=u("el-input"),Z=u("el-input-number"),ee=u("el-dialog"),x=ae("loading");return k(),M("div",se,[l[40]||(l[40]=o("div",{class:"page-header"},[o("h2",null,"電費計算"),o("p",null,"電力費用計算與管理，包含契約設定、電表管理、電價設定等功能")],-1)),e(y,{class:"bill-card"},{default:t(()=>[e(O,{modelValue:L.value,"onUpdate:modelValue":l[8]||(l[8]=a=>L.value=a),type:"border-card"},{default:t(()=>[e(h,{label:"契約設定",name:"contract"},{default:t(()=>[o("div",re,[e(y,{shadow:"never"},{header:t(()=>[o("div",ie,[l[17]||(l[17]=o("span",null,"電力契約設定",-1)),e(i,{type:"primary",onClick:l[0]||(l[0]=a=>V.value=!0)},{default:t(()=>l[16]||(l[16]=[d(" 新增契約 ",-1)])),_:1,__:[16]})])]),default:t(()=>[$((k(),U(D,{data:H.value,stripe:"",border:""},{default:t(()=>[e(n,{prop:"name",label:"契約名稱",width:"200"}),e(n,{prop:"contractType",label:"契約類型",width:"150"},{default:t(({row:a})=>[e(w,{type:s.getContractTypeColor(a.contractType)},{default:t(()=>[d(r(s.getContractTypeText(a.contractType)),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"capacity",label:"契約容量(kW)",width:"150",align:"right"}),e(n,{prop:"startDate",label:"開始日期",width:"120"},{default:t(({row:a})=>[d(r(s.formatDate(a.startDate)),1)]),_:1}),e(n,{prop:"endDate",label:"結束日期",width:"120"},{default:t(({row:a})=>[d(r(s.formatDate(a.endDate)),1)]),_:1}),e(n,{prop:"status",label:"狀態",width:"100"},{default:t(({row:a})=>[e(w,{type:a.status==="active"?"success":"danger"},{default:t(()=>[d(r(a.status==="active"?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),e(n,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[e(i,{type:"primary",size:"small",onClick:v=>s.editContract(a)},{default:t(()=>l[18]||(l[18]=[d(" 編輯 ",-1)])),_:2,__:[18]},1032,["onClick"]),e(i,{type:"warning",size:"small",onClick:v=>s.toggleContractStatus(a)},{default:t(()=>[d(r(a.status==="active"?"停用":"啟用"),1)]),_:2},1032,["onClick"]),e(i,{type:"danger",size:"small",onClick:v=>s.deleteContract(a)},{default:t(()=>l[19]||(l[19]=[d(" 刪除 ",-1)])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[x,W.value]])]),_:1})])]),_:1}),e(h,{label:"電表管理",name:"meter"},{default:t(()=>[o("div",ue,[e(y,{shadow:"never"},{header:t(()=>[o("div",pe,[l[21]||(l[21]=o("span",null,"電表設定",-1)),e(i,{type:"primary",onClick:l[1]||(l[1]=a=>j.value=!0)},{default:t(()=>l[20]||(l[20]=[d(" 新增電表 ",-1)])),_:1,__:[20]})])]),default:t(()=>[$((k(),U(D,{data:N.value,stripe:"",border:""},{default:t(()=>[e(n,{prop:"name",label:"電表名稱",width:"200"}),e(n,{prop:"meterNo",label:"電表編號",width:"150"}),e(n,{prop:"location",label:"安裝位置",width:"200"}),e(n,{prop:"meterType",label:"電表類型",width:"120"},{default:t(({row:a})=>[e(w,null,{default:t(()=>[d(r(s.getMeterTypeText(a.meterType)),1)]),_:2},1024)]),_:1}),e(n,{prop:"multiplier",label:"倍率",width:"100",align:"right"}),e(n,{prop:"installDate",label:"安裝日期",width:"120"},{default:t(({row:a})=>[d(r(s.formatDate(a.installDate)),1)]),_:1}),e(n,{prop:"status",label:"狀態",width:"100"},{default:t(({row:a})=>[e(w,{type:a.status==="active"?"success":"danger"},{default:t(()=>[d(r(a.status==="active"?"正常":"異常"),1)]),_:2},1032,["type"])]),_:1}),e(n,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[e(i,{type:"primary",size:"small",onClick:v=>s.editMeter(a)},{default:t(()=>l[22]||(l[22]=[d(" 編輯 ",-1)])),_:2,__:[22]},1032,["onClick"]),e(i,{type:"info",size:"small",onClick:v=>s.viewMeterData(a)},{default:t(()=>l[23]||(l[23]=[d(" 數據 ",-1)])),_:2,__:[23]},1032,["onClick"]),e(i,{type:"danger",size:"small",onClick:v=>s.deleteMeter(a)},{default:t(()=>l[24]||(l[24]=[d(" 刪除 ",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[x,E.value]])]),_:1})])]),_:1}),e(h,{label:"電價設定",name:"rate"},{default:t(()=>[o("div",me,[e(y,{shadow:"never"},{header:t(()=>[o("div",fe,[l[26]||(l[26]=o("span",null,"電價費率設定",-1)),e(i,{type:"primary",onClick:l[2]||(l[2]=a=>G.value=!0)},{default:t(()=>l[25]||(l[25]=[d(" 新增費率 ",-1)])),_:1,__:[25]})])]),default:t(()=>[$((k(),U(D,{data:K.value,stripe:"",border:""},{default:t(()=>[e(n,{prop:"name",label:"費率名稱",width:"200"}),e(n,{prop:"rateType",label:"費率類型",width:"150"},{default:t(({row:a})=>[e(w,null,{default:t(()=>[d(r(s.getRateTypeText(a.rateType)),1)]),_:2},1024)]),_:1}),e(n,{prop:"peakRate",label:"尖峰電價(元/度)",width:"150",align:"right"},{default:t(({row:a})=>[d(r(a.peakRate?.toFixed(4)||"-"),1)]),_:1}),e(n,{prop:"offPeakRate",label:"離峰電價(元/度)",width:"150",align:"right"},{default:t(({row:a})=>[d(r(a.offPeakRate?.toFixed(4)||"-"),1)]),_:1}),e(n,{prop:"demandCharge",label:"需量費(元/kW)",width:"150",align:"right"},{default:t(({row:a})=>[d(r(a.demandCharge?.toFixed(2)||"-"),1)]),_:1}),e(n,{prop:"effectiveDate",label:"生效日期",width:"120"},{default:t(({row:a})=>[d(r(s.formatDate(a.effectiveDate)),1)]),_:1}),e(n,{prop:"status",label:"狀態",width:"100"},{default:t(({row:a})=>[e(w,{type:a.status==="active"?"success":"info"},{default:t(()=>[d(r(a.status==="active"?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),e(n,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[e(i,{type:"primary",size:"small",onClick:v=>s.editRate(a)},{default:t(()=>l[27]||(l[27]=[d(" 編輯 ",-1)])),_:2,__:[27]},1032,["onClick"]),e(i,{type:"warning",size:"small",onClick:v=>s.toggleRateStatus(a)},{default:t(()=>[d(r(a.status==="active"?"停用":"啟用"),1)]),_:2},1032,["onClick"]),e(i,{type:"danger",size:"small",onClick:v=>s.deleteRate(a)},{default:t(()=>l[28]||(l[28]=[d(" 刪除 ",-1)])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[x,A.value]])]),_:1})])]),_:1}),e(h,{label:"費用查詢",name:"calculate"},{default:t(()=>[o("div",_e,[e(y,{shadow:"never"},{header:t(()=>l[29]||(l[29]=[o("div",{class:"card-header"},[o("span",null,"電費計算查詢")],-1)])),default:t(()=>[e(z(q),{ref_key:"calculateFormRef",ref:S,model:C,rules:J,"label-width":"120px",class:"calculate-form"},{default:t(()=>[e(Y,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(f,{label:"選擇電表",prop:"meterId"},{default:t(()=>[e(R,{modelValue:C.meterId,"onUpdate:modelValue":l[3]||(l[3]=a=>C.meterId=a),placeholder:"請選擇電表",style:{width:"100%"}},{default:t(()=>[(k(!0),M(oe,null,de(N.value,a=>(k(),U(_,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(f,{label:"計算期間",prop:"dateRange"},{default:t(()=>[e(T,{modelValue:C.dateRange,"onUpdate:modelValue":l[4]||(l[4]=a=>C.dateRange=a),type:"monthrange","range-separator":"至","start-placeholder":"開始月份","end-placeholder":"結束月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,null,{default:t(()=>[e(i,{type:"primary",loading:B.value,onClick:s.calculateBill},{default:t(()=>[d(r(B.value?"計算中...":"開始計算"),1)]),_:1},8,["loading","onClick"]),e(i,{type:"success",disabled:!c.value,onClick:s.exportBillResult},{default:t(()=>l[30]||(l[30]=[d(" 匯出結果 ",-1)])),_:1,__:[30]},8,["disabled","onClick"])]),_:1})]),_:1},8,["model"]),c.value?(k(),M("div",ge,[e(y,{shadow:"never"},{header:t(()=>l[31]||(l[31]=[o("span",null,"計算結果",-1)])),default:t(()=>[e(Y,{gutter:20},{default:t(()=>[e(g,{span:6},{default:t(()=>[o("div",ve,[o("div",be,r(c.value.totalUsage),1),l[32]||(l[32]=o("div",{class:"result-label"},"總用電量(度)",-1))])]),_:1}),e(g,{span:6},{default:t(()=>[o("div",ce,[o("div",ye,r(c.value.totalAmount),1),l[33]||(l[33]=o("div",{class:"result-label"},"總電費(元)",-1))])]),_:1}),e(g,{span:6},{default:t(()=>[o("div",ke,[o("div",we,r(c.value.demandCharge),1),l[34]||(l[34]=o("div",{class:"result-label"},"需量費(元)",-1))])]),_:1}),e(g,{span:6},{default:t(()=>[o("div",Ce,[o("div",he,r(c.value.tax),1),l[35]||(l[35]=o("div",{class:"result-label"},"營業稅(元)",-1))])]),_:1})]),_:1}),e(D,{data:c.value.details,stripe:"",border:"",style:{"margin-top":"20px"}},{default:t(()=>[e(n,{prop:"month",label:"月份",width:"100"}),e(n,{prop:"usage",label:"用電量(度)",width:"120",align:"right"}),e(n,{prop:"peakUsage",label:"尖峰用電(度)",width:"120",align:"right"}),e(n,{prop:"offPeakUsage",label:"離峰用電(度)",width:"120",align:"right"}),e(n,{prop:"demandKw",label:"需量(kW)",width:"100",align:"right"}),e(n,{prop:"amount",label:"電費(元)",width:"120",align:"right"})]),_:1},8,["data"])]),_:1})])):ne("",!0)]),_:1})])]),_:1}),e(h,{label:"報表匯出",name:"export"},{default:t(()=>[o("div",Ve,[e(y,{shadow:"never"},{header:t(()=>l[36]||(l[36]=[o("div",{class:"card-header"},[o("span",null,"電費報表匯出")],-1)])),default:t(()=>[e(z(q),{model:b,"label-width":"120px",class:"export-form"},{default:t(()=>[e(Y,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(f,{label:"報表類型"},{default:t(()=>[e(R,{modelValue:b.reportType,"onUpdate:modelValue":l[5]||(l[5]=a=>b.reportType=a),style:{width:"100%"}},{default:t(()=>[e(_,{label:"月度電費報表",value:"monthly"}),e(_,{label:"年度電費報表",value:"yearly"}),e(_,{label:"電表用量報表",value:"meter_usage"}),e(_,{label:"費率分析報表",value:"rate_analysis"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(f,{label:"匯出格式"},{default:t(()=>[e(R,{modelValue:b.format,"onUpdate:modelValue":l[6]||(l[6]=a=>b.format=a),style:{width:"100%"}},{default:t(()=>[e(_,{label:"Excel (.xlsx)",value:"xlsx"}),e(_,{label:"PDF (.pdf)",value:"pdf"}),e(_,{label:"CSV (.csv)",value:"csv"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{label:"時間範圍"},{default:t(()=>[e(T,{modelValue:b.dateRange,"onUpdate:modelValue":l[7]||(l[7]=a=>b.dateRange=a),type:"monthrange","range-separator":"至","start-placeholder":"開始月份","end-placeholder":"結束月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"300px"}},null,8,["modelValue"])]),_:1}),e(f,null,{default:t(()=>[e(i,{type:"primary",loading:I.value,onClick:s.exportReport},{default:t(()=>[d(r(I.value?"匯出中...":"匯出報表"),1)]),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(ee,{modelValue:V.value,"onUpdate:modelValue":l[15]||(l[15]=a=>V.value=a),title:m.id?"編輯契約":"新增契約",width:"600px"},{footer:t(()=>[e(i,{onClick:l[14]||(l[14]=a=>V.value=!1)},{default:t(()=>l[38]||(l[38]=[d("取消",-1)])),_:1,__:[38]}),e(i,{type:"primary",onClick:s.saveContract},{default:t(()=>l[39]||(l[39]=[d("確認",-1)])),_:1,__:[39]},8,["onClick"])]),default:t(()=>[e(z(q),{ref_key:"contractFormRef",ref:P,model:m,rules:X,"label-width":"120px"},{default:t(()=>[e(f,{label:"契約名稱",prop:"name"},{default:t(()=>[e(Q,{modelValue:m.name,"onUpdate:modelValue":l[9]||(l[9]=a=>m.name=a),placeholder:"請輸入契約名稱"},null,8,["modelValue"])]),_:1}),e(f,{label:"契約類型",prop:"contractType"},{default:t(()=>[e(R,{modelValue:m.contractType,"onUpdate:modelValue":l[10]||(l[10]=a=>m.contractType=a),style:{width:"100%"}},{default:t(()=>[e(_,{label:"高壓用戶",value:"high_voltage"}),e(_,{label:"低壓用戶",value:"low_voltage"}),e(_,{label:"特高壓用戶",value:"extra_high_voltage"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"契約容量",prop:"capacity"},{default:t(()=>[e(Z,{modelValue:m.capacity,"onUpdate:modelValue":l[11]||(l[11]=a=>m.capacity=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),l[37]||(l[37]=o("span",{style:{"margin-left":"8px"}},"kW",-1))]),_:1,__:[37]}),e(Y,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(f,{label:"開始日期",prop:"startDate"},{default:t(()=>[e(T,{modelValue:m.startDate,"onUpdate:modelValue":l[12]||(l[12]=a=>m.startDate=a),type:"date",placeholder:"選擇開始日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(f,{label:"結束日期",prop:"endDate"},{default:t(()=>[e(T,{modelValue:m.endDate,"onUpdate:modelValue":l[13]||(l[13]=a=>m.endDate=a),type:"date",placeholder:"選擇結束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{Ye as default};
