import{a as pe,r as ce,g as me}from"./index-0d74a956.js";import{u as ge,p as ve}from"./dataService-42a3bdf6.js";import{d as fe,r as u,G as _e,D as Ce,o as ye,p as r,q as Te,c as y,e as B,f as g,g as t,w as s,m as d,h as S,l as he,n as F,t as T,aH as Me,X as A,Y as R,aF as q,E as p,_ as De}from"./index-329ed960.js";const be={class:"channel-container"},we={class:"card-header"},Ve={class:"filter-section"},xe={class:"channel-name"},Le={class:"pagination-container"},$e=fe({__name:"channel",setup(Se){ge();const k=u(),I=u(!1),f=u(!1),h=u(""),b=u(""),w=u(""),M=u(1),V=u(20),x=u(0),_=u([]),N=u([]),P=u([]),n=_e({id:"",name:"",driverType:"",dataMode:"",description:"",status:"active"}),X={name:[{required:!0,message:"請輸入通道名稱",trigger:"blur"}],driverType:[{required:!0,message:"請選擇驅動程式",trigger:"change"}],dataMode:[{required:!0,message:"請選擇資料獲取方式",trigger:"change"}],status:[{required:!0,message:"請選擇狀態",trigger:"change"}]},j=Ce(()=>{let a=_.value;return h.value&&(a=a.filter(e=>e.name.toLowerCase().includes(h.value.toLowerCase())||e.description?.toLowerCase().includes(h.value.toLowerCase()))),b.value&&(a=a.filter(e=>e.status===b.value)),w.value&&(a=a.filter(e=>e.driverType===w.value)),a}),G=a=>({TCP:"primary",OBIX:"success",DesigoCC:"warning",Virtual:"info"})[a]||"default",H=a=>a||"未知",K=a=>a||"未知",W=a=>a?new Date(a).toLocaleString("zh-TW"):"-",Y=()=>{},U=()=>{},J=a=>{V.value=a,M.value=1,C()},Q=a=>{M.value=a,C()},Z=()=>{C()},ee=a=>{Object.assign(n,{id:a.id,name:a.name,driverType:a.driverCode||a.driverType,dataMode:a.fetchDataModeCode||a.dataMode,description:a.description,status:a.status}),f.value=!0},ae=async a=>{try{const e=a.status==="active"?"inactive":"active",o=e==="active"?"啟用":"停用";await q.confirm(`確定要${o}通道 "${a.name}" 嗎？`,"狀態變更確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const i=await tagsAPI.setChannelStatus({TagChannelId:a.id,TargetStatus:e==="active"});if(i&&(i.success||i.ReturnCode===1))a.status=e,p.success(`通道${o}成功`,{duration:5e3,showClose:!0});else{let m=`通道${o}失敗`;i?.Message?m+=`: ${i.Message}`:i?.message?m+=`: ${i.message}`:i?.error&&(m+=`: ${i.error}`),p.error(m,{duration:5e3,showClose:!0})}}catch(e){if(e!=="cancel"){console.error("切換通道狀態失敗:",e);let o="切換通道狀態失敗";e?.response?.data?.Message?o+=`: ${e.response.data.Message}`:e?.response?.data?.message?o+=`: ${e.response.data.message}`:e?.message?o+=`: ${e.message}`:typeof e=="string"&&(o+=`: ${e}`),p.error(o,{duration:5e3,showClose:!0})}}},te=async a=>{try{await q.confirm(`確定要刪除通道 "${a.name}" 嗎？此操作不可恢復！`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const e={TagChannelId:a.id},o=await tagsAPI.deleteTagChannel(e);if(o&&(o.ReturnCode===1||o.success))p.success("通道刪除成功"),await C();else{const i=o?.Message||o?.message||"刪除失敗";throw new Error(i)}}catch(e){if(e!=="cancel"){console.error("刪除通道失敗:",e);let o="刪除通道失敗";e.message?o=e.message:e.response?.data?.Message?o=e.response.data.Message:e.response?.data?.message?o=e.response.data.message:typeof e=="string"&&(o=e),p.error({message:o,duration:5e3,showClose:!0})}}},le=async()=>{if(k.value)try{await k.value.validate();const a={Name:n.name,Description:n.description||void 0,ChannelType:n.driverType||void 0};let e;if(n.id){const o={Id:n.id,...a};e=await tagsAPI.updateTagChannel(o)}else e=await tagsAPI.createTagChannel(a);if(e&&(e.ReturnCode===1||e.success))p.success(n.id?"通道更新成功":"通道新增成功"),f.value=!1,Object.assign(n,{id:"",name:"",driverType:"",dataMode:"",description:"",status:"active"}),await C();else{const o=e?.Message||e?.message||"保存失敗";throw new Error(o)}}catch(a){console.error("保存通道失敗:",a);let e="保存通道失敗";a.message?e=a.message:a.response?.data?.Message?e=a.response.data.Message:a.response?.data?.message?e=a.response.data.message:typeof a=="string"&&(e=a),p.error({message:e,duration:5e3,showClose:!0})}},C=async()=>{try{I.value=!0;const a=await ve.get("/api/Tag/GetTagChannelListByCustomer",{pageIndex:M.value,pageSize:V.value});a&&a.Detail?(a.Detail.TagChannelDriverList&&(N.value=a.Detail.TagChannelDriverList),a.Detail.FetchDataModeList&&(P.value=a.Detail.FetchDataModeList),a.Detail.TagChannelList?(_.value=a.Detail.TagChannelList.map(e=>({id:e.TagChannelId||e.Id,name:e.TagChannelName||e.Name,driverType:e.DriverName||e.DriverType,dataMode:e.FetchDataModeName||e.DataMode,description:e.Description,status:e.Status===1?"active":"inactive",createTime:e.CreatedTime||new Date().toISOString(),customerName:e.CustomerName||"",driverCode:e.DriverCode||0,fetchDataModeCode:e.FetchDataModeCode||0})),x.value=_.value.length,p.success(`成功載入 ${_.value.length} 個通道`)):(_.value=[],x.value=0,p.warning("未找到通道數據"))):(_.value=[],x.value=0,p.warning("未找到通道數據"))}catch(a){console.error("載入通道列表失敗:",a),p.error(a.message||"載入通道列表失敗")}finally{I.value=!1}};return ye(async()=>{await C()}),(a,e)=>{const o=r("el-button"),i=r("el-icon"),m=r("el-input"),L=r("el-col"),c=r("el-option"),$=r("el-select"),se=r("el-row"),v=r("el-table-column"),z=r("el-tag"),oe=r("el-table"),ne=r("el-pagination"),re=r("el-card"),O=r("el-radio"),ie=r("el-radio-group"),D=r("el-form-item"),de=r("el-dialog"),ue=Te("loading");return y(),B("div",be,[e[22]||(e[22]=g("div",{class:"page-header"},[g("h2",null,"通道管理"),g("p",null,"管理 PLC 系統的通訊通道，包含不同的驅動程式和資料獲取方式")],-1)),t(re,{class:"channel-card"},{header:s(()=>[g("div",we,[e[14]||(e[14]=g("span",null,"通道列表",-1)),t(o,{type:"primary",onClick:e[0]||(e[0]=l=>f.value=!0)},{default:s(()=>e[13]||(e[13]=[d(" 新增通道 ",-1)])),_:1,__:[13]})])]),default:s(()=>[g("div",Ve,[t(se,{gutter:20},{default:s(()=>[t(L,{span:8},{default:s(()=>[t(m,{modelValue:h.value,"onUpdate:modelValue":e[1]||(e[1]=l=>h.value=l),placeholder:"搜尋通道名稱...",clearable:"",onInput:Y},{prefix:s(()=>[t(i,null,{default:s(()=>[t(S(pe))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(L,{span:6},{default:s(()=>[t($,{modelValue:b.value,"onUpdate:modelValue":e[2]||(e[2]=l=>b.value=l),placeholder:"狀態篩選",clearable:"",onChange:U},{default:s(()=>[t(c,{label:"全部",value:""}),t(c,{label:"啟用",value:"active"}),t(c,{label:"停用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),t(L,{span:6},{default:s(()=>[t($,{modelValue:w.value,"onUpdate:modelValue":e[3]||(e[3]=l=>w.value=l),placeholder:"驅動程式篩選",clearable:"",onChange:U},{default:s(()=>[t(c,{label:"全部",value:""}),t(c,{label:"TCP",value:"TCP"}),t(c,{label:"OBIX",value:"OBIX"}),t(c,{label:"Desigo CC",value:"DesigoCC"}),t(c,{label:"虛擬點",value:"Virtual"})]),_:1},8,["modelValue"])]),_:1}),t(L,{span:4},{default:s(()=>[t(o,{type:"info",onClick:Z},{default:s(()=>[t(i,null,{default:s(()=>[t(S(ce))]),_:1}),e[15]||(e[15]=d(" 刷新 ",-1))]),_:1,__:[15]})]),_:1})]),_:1})]),he((y(),F(oe,{data:j.value,stripe:"",border:""},{default:s(()=>[t(v,{prop:"name",label:"通道名稱",width:"200"},{default:s(({row:l})=>[g("div",xe,[t(i,{class:"channel-icon"},{default:s(()=>[t(S(me))]),_:1}),d(" "+T(l.name),1)])]),_:1}),t(v,{prop:"driverType",label:"驅動程式",width:"150"},{default:s(({row:l})=>[t(z,{type:G(l.driverType)},{default:s(()=>[d(T(H(l.driverType)),1)]),_:2},1032,["type"])]),_:1}),t(v,{prop:"dataMode",label:"資料獲取方式",width:"150"},{default:s(({row:l})=>[t(z,null,{default:s(()=>[d(T(K(l.dataMode)),1)]),_:2},1024)]),_:1}),t(v,{prop:"description",label:"說明","min-width":"200"}),t(v,{prop:"status",label:"狀態",width:"100"},{default:s(({row:l})=>[t(z,{type:l.status==="active"?"success":"danger"},{default:s(()=>[d(T(l.status==="active"?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(v,{prop:"createTime",label:"建立時間",width:"180"},{default:s(({row:l})=>[d(T(W(l.createTime)),1)]),_:1}),t(v,{label:"操作",width:"200",fixed:"right"},{default:s(({row:l})=>[t(o,{type:"primary",size:"small",onClick:E=>ee(l)},{default:s(()=>e[16]||(e[16]=[d(" 編輯 ",-1)])),_:2,__:[16]},1032,["onClick"]),t(o,{type:"warning",size:"small",onClick:E=>ae(l)},{default:s(()=>[d(T(l.status==="active"?"停用":"啟用"),1)]),_:2},1032,["onClick"]),t(o,{type:"danger",size:"small",onClick:E=>te(l)},{default:s(()=>e[17]||(e[17]=[d(" 刪除 ",-1)])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ue,I.value]]),g("div",Le,[t(ne,{"current-page":M.value,"onUpdate:currentPage":e[4]||(e[4]=l=>M.value=l),"page-size":V.value,"onUpdate:pageSize":e[5]||(e[5]=l=>V.value=l),total:x.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1}),t(de,{modelValue:f.value,"onUpdate:modelValue":e[12]||(e[12]=l=>f.value=l),title:n.id?"編輯通道":"新增通道",width:"600px"},{footer:s(()=>[t(o,{onClick:e[11]||(e[11]=l=>f.value=!1)},{default:s(()=>e[20]||(e[20]=[d("取消",-1)])),_:1,__:[20]}),t(o,{type:"primary",onClick:le},{default:s(()=>e[21]||(e[21]=[d("確認",-1)])),_:1,__:[21]})]),default:s(()=>[t(S(Me),{ref_key:"channelFormRef",ref:k,model:n,rules:X,"label-width":"120px"},{default:s(()=>[t(D,{label:"狀態",prop:"status"},{default:s(()=>[t(ie,{modelValue:n.status,"onUpdate:modelValue":e[6]||(e[6]=l=>n.status=l)},{default:s(()=>[t(O,{value:"active"},{default:s(()=>e[18]||(e[18]=[d("啟用",-1)])),_:1,__:[18]}),t(O,{value:"inactive"},{default:s(()=>e[19]||(e[19]=[d("停用",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"])]),_:1}),t(D,{label:"通道名稱",prop:"name"},{default:s(()=>[t(m,{modelValue:n.name,"onUpdate:modelValue":e[7]||(e[7]=l=>n.name=l),placeholder:"請輸入通道名稱"},null,8,["modelValue"])]),_:1}),t(D,{label:"驅動程式",prop:"driverType"},{default:s(()=>[t($,{modelValue:n.driverType,"onUpdate:modelValue":e[8]||(e[8]=l=>n.driverType=l),style:{width:"100%"}},{default:s(()=>[(y(!0),B(A,null,R(N.value,l=>(y(),F(c,{key:l.Code,label:l.Name,value:l.Code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(D,{label:"資料獲取方式",prop:"dataMode"},{default:s(()=>[t($,{modelValue:n.dataMode,"onUpdate:modelValue":e[9]||(e[9]=l=>n.dataMode=l),style:{width:"100%"}},{default:s(()=>[(y(!0),B(A,null,R(P.value,l=>(y(),F(c,{key:l.Code,label:l.Name,value:l.Code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(D,{label:"說明",prop:"description"},{default:s(()=>[t(m,{modelValue:n.description,"onUpdate:modelValue":e[10]||(e[10]=l=>n.description=l),placeholder:"請輸入說明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const Be=De($e,[["__scopeId","data-v-57266c50"]]);export{Be as default};
