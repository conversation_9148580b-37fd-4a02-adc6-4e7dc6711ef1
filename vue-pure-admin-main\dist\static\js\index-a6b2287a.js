import{u as m}from"./columns-30d3007e.js";import{u as g}from"./hooks-7d897f55.js";import{d as b,p as o,c as f,n as C,w as t,g as a,h as e,_ as h}from"./index-329ed960.js";const x=b({__name:"index",setup(y){const{loading:i,columns:s,dataList:r,pagination:n,Empty:c,onCurrentChange:_}=m();return(v,w)=>{const l=o("el-empty"),p=o("el-button"),u=o("pure-table");return f(),C(u,{"row-key":"id",alignWhole:"center",showOverflowTooltip:"",loading:e(i),"loading-config":{background:"transparent"},data:e(r).slice((e(n).currentPage-1)*e(n).pageSize,e(n).currentPage*e(n).pageSize),columns:e(s),pagination:e(n),onPageCurrentChange:e(_)},{empty:t(()=>[a(l,{description:"暂无数据","image-size":60},{image:t(()=>[a(e(c))]),_:1})]),operation:t(({row:d})=>[a(p,{plain:"",circle:"",size:"small",title:`查看序号为${d.id}的详情`,icon:e(g)("ri:search-line")},null,8,["title","icon"])]),_:1},8,["loading","data","columns","pagination","onPageCurrentChange"])}}});const B=h(x,[["__scopeId","data-v-db81b3a6"]]);export{B as default};
