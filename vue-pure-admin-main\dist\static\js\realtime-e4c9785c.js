import{s as xe,r as Ve,g as Ie,f as Re,d as Ce,a as ke,e as Le}from"./index-0d74a956.js";import{aG as w,d as Ue,r as m,G as j,D as G,o as qe,as as ze,p as r,q as Ae,c as D,e as K,f as n,g as a,w as l,h as _,m as c,aH as X,X as Ee,Y as Ne,n as U,t as f,a0 as Y,j as J,l as Be,E as v,aF as Fe,_ as Ge}from"./index-329ed960.js";import{p as q}from"./signalr-7c260a48.js";import{u as Qe}from"./dataService-42a3bdf6.js";const Q={getRealTimeData:o=>w.request("post","/api/Statistic/GetRealTimeStaisticSummary",{data:{TagIdList:o.TagIdList}}),searchTags:o=>w.request("get","/api/Tag/SearchTags",{params:{keyword:o.keyword,customerId:o.customerId,pageIndex:o.pageIndex||1,pageSize:o.pageSize||50}}),getAvailableTags:o=>w.request("get","/api/Tag/GetAvailableTags",{params:{customerId:o}}),getTagDetail:(o,x)=>w.request("get","/api/Tag/GetTagDetail",{params:{tagId:o,customerId:x}}),saveRealtimeDataSettings:o=>w.request("post","/api/Settings/SaveRealtimeDataSettings",{data:o}),getRealtimeDataSettings:o=>w.request("get","/api/Settings/GetRealtimeDataSettings",{params:{customerId:o}}),exportRealtimeData:o=>w.request("post","/api/Export/ExportRealtimeData",{data:o,responseType:"blob"})},We={class:"database-container"},He={class:"card-header"},Me={class:"header-actions"},$e={style:{float:"left"}},Pe={style:{float:"right",color:"#8492a6","font-size":"13px"}},je={class:"status-info"},Ke={class:"status-item"},Xe={class:"status-item"},Ye={class:"status-item"},Je={class:"card-header"},Oe={class:"header-actions"},Ze={class:"tag-name"},ea=Ue({__name:"realtime",setup(o){const x=Qe(),z=m(),O=m(),y=m(!1),A=m(!1),V=m(!1),I=m(""),R=m(""),T=m(!1),u=j({selectedTags:[],refreshInterval:1e4}),Z={selectedTags:[{required:!0,message:"請選擇至少一個標籤",trigger:"change"}]},p=m([]),E=m([]),C=m([]),h=j({enableRealtime:!0,showAbnormalData:!0,dataRetentionTime:216e5});let g=null;const k=G(()=>p.value.length>0),ee=G(()=>T.value?"已連線":"未連線"),ae=G(()=>({"status-connected":T.value,"status-disconnected":!T.value})),te=async e=>{if(!e){E.value=[];return}try{A.value=!0;const t=await Q.searchTags({keyword:e,customerId:x.userInfo.customerId});E.value=t.items||[]}catch(t){console.error("搜索標籤失敗:",t),v.error(t.message||"搜索標籤失敗")}finally{A.value=!1}},le=e=>{},se=async()=>{if(z.value)try{await z.value.validate(),y.value=!0;const e=await Q.getRealTimeData({TagIdList:u.selectedTags,customerId:x.userInfo.customerId});p.value=e.ValueList||[],R.value=new Date().toLocaleString("zh-TW"),L(),oe(),await re(),v.success("查詢成功")}catch(e){console.error("查詢失敗:",e),v.error(e.message||"查詢失敗")}finally{y.value=!1}},W=async()=>{if(u.selectedTags.length!==0)try{y.value=!0;const e=await Q.getRealTimeData({TagIdList:u.selectedTags,customerId:x.userInfo.customerId});p.value=e.ValueList||[],R.value=new Date().toLocaleString("zh-TW"),L()}catch(e){console.error("刷新數據失敗:",e),v.error(e.message||"刷新數據失敗")}finally{y.value=!1}},ne=async()=>{try{await Fe.confirm("確定要清除所有數據嗎？","清除數據",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"}),p.value=[],C.value=[],u.selectedTags=[],g&&(clearInterval(g),g=null),await H(),v.success("數據已清除")}catch(e){e!=="cancel"&&console.error("清除數據失敗:",e)}},L=()=>{if(!I.value){C.value=p.value;return}C.value=p.value.filter(e=>e.Name.toLowerCase().includes(I.value.toLowerCase())||e.Description&&e.Description.toLowerCase().includes(I.value.toLowerCase()))},oe=()=>{g&&(clearInterval(g),g=null),u.refreshInterval>0&&(g=setInterval(()=>{W()},u.refreshInterval))},re=async()=>{try{await q.connectHub("desigocc"),q.on("desigocc:realtime",e=>{ue(e)}),T.value=!0}catch(e){console.error("連接SignalR失敗:",e),T.value=!1}},H=async()=>{try{q.off("desigocc:realtime"),await q.disconnectHub("desigocc"),T.value=!1}catch(e){console.error("斷開SignalR連接失敗:",e)}},ue=e=>{!e||!e.tagValueList||(e.tagValueList.forEach(t=>{const i=p.value.findIndex(d=>d.TagId===t.TagId);i!==-1&&(p.value[i]={...p.value[i],...t})}),R.value=new Date().toLocaleString("zh-TW"),L())},ie=e=>e==null?"-":typeof e=="number"?e.toFixed(2):String(e),de=e=>e?new Date(e).toLocaleString("zh-TW"):"-",ce=({row:e})=>{switch(e.Status){case 1:return"row-normal";case 2:return"row-warning";case 3:return"row-error";default:return""}},me=e=>{switch(e){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},fe=e=>{switch(e){case 1:return"正常";case 2:return"警告";case 3:return"異常";default:return"未知"}},pe=e=>{switch(e){case 1:return"value-normal";case 2:return"value-warning";case 3:return"value-error";default:return""}},ge=e=>{switch(e){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},_e=e=>{switch(e){case 1:return"良好";case 2:return"一般";case 3:return"差";default:return"未知"}},ve=e=>{v.info("標籤詳情功能開發中...")},ye=()=>{v.info("數據匯出功能開發中...")},he=async()=>{try{v.success("設定保存成功"),V.value=!1}catch(e){console.error("保存設定失敗:",e),v.error(e.message||"保存設定失敗")}};return qe(async()=>{}),ze(()=>{g&&clearInterval(g),H()}),(e,t)=>{const i=r("el-button"),d=r("el-option"),N=r("el-select"),S=r("el-form-item"),M=r("el-col"),be=r("el-row"),B=r("el-card"),F=r("el-icon"),we=r("el-input"),$=r("el-tag"),b=r("el-table-column"),Te=r("el-table"),P=r("el-switch"),Se=r("el-dialog"),De=Ae("loading");return D(),K("div",We,[t[18]||(t[18]=n("div",{class:"page-header"},[n("h2",null,"即時資料管理"),n("p",null,"查詢和監控系統即時數據，支援多標籤選擇和即時更新")],-1)),a(B,{class:"query-card"},{header:l(()=>[n("div",He,[t[10]||(t[10]=n("span",null,"查詢條件",-1)),n("div",Me,[a(i,{type:"info",icon:_(xe),onClick:t[0]||(t[0]=s=>V.value=!0)},{default:l(()=>t[9]||(t[9]=[c(" 設定 ",-1)])),_:1,__:[9]},8,["icon"])])])]),default:l(()=>[a(_(X),{ref_key:"queryFormRef",ref:z,model:u,rules:Z,"label-width":"120px",class:"query-form"},{default:l(()=>[a(be,{gutter:20},{default:l(()=>[a(M,{span:12},{default:l(()=>[a(S,{label:"選擇標籤",prop:"selectedTags"},{default:l(()=>[a(N,{modelValue:u.selectedTags,"onUpdate:modelValue":t[1]||(t[1]=s=>u.selectedTags=s),multiple:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"請選擇要查詢的標籤","remote-method":te,loading:A.value,style:{width:"100%"},onChange:le},{default:l(()=>[(D(!0),K(Ee,null,Ne(E.value,s=>(D(),U(d,{key:s.id,label:s.name,value:s.id},{default:l(()=>[n("span",$e,f(s.name),1),n("span",Pe,f(s.description),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1}),a(M,{span:12},{default:l(()=>[a(S,{label:"更新間隔"},{default:l(()=>[a(N,{modelValue:u.refreshInterval,"onUpdate:modelValue":t[2]||(t[2]=s=>u.refreshInterval=s),style:{width:"100%"}},{default:l(()=>[a(d,{label:"不自動更新",value:0}),a(d,{label:"5秒",value:5e3}),a(d,{label:"10秒",value:1e4}),a(d,{label:"30秒",value:3e4}),a(d,{label:"1分鐘",value:6e4})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(S,null,{default:l(()=>[a(i,{type:"primary",loading:y.value,disabled:u.selectedTags.length===0,onClick:se},{default:l(()=>[c(f(y.value?"查詢中...":"開始查詢"),1)]),_:1},8,["loading","disabled"]),a(i,{type:"success",icon:_(Ve),disabled:!k.value,onClick:W},{default:l(()=>t[11]||(t[11]=[c(" 手動刷新 ",-1)])),_:1,__:[11]},8,["icon","disabled"]),a(i,{type:"warning",disabled:!k.value,onClick:ne},{default:l(()=>t[12]||(t[12]=[c(" 清除數據 ",-1)])),_:1,__:[12]},8,["disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),k.value?(D(),U(B,{key:0,class:"status-card"},{default:l(()=>[n("div",je,[n("div",Ke,[a(F,{class:Y(["status-icon",ae.value])},{default:l(()=>[a(_(Ie))]),_:1},8,["class"]),n("span",null,"連接狀態: "+f(ee.value),1)]),n("div",Xe,[a(F,{class:"status-icon"},{default:l(()=>[a(_(Re))]),_:1}),n("span",null,"最後更新: "+f(R.value),1)]),n("div",Ye,[a(F,{class:"status-icon"},{default:l(()=>[a(_(Ce))]),_:1}),n("span",null,"數據筆數: "+f(p.value.length),1)])])]),_:1})):J("",!0),k.value?(D(),U(B,{key:1,class:"data-table-card"},{header:l(()=>[n("div",Je,[t[14]||(t[14]=n("span",null,"即時數據",-1)),n("div",Oe,[a(we,{modelValue:I.value,"onUpdate:modelValue":t[3]||(t[3]=s=>I.value=s),placeholder:"搜尋標籤名稱...","prefix-icon":_(ke),clearable:"",style:{width:"250px","margin-right":"12px"},onInput:L},null,8,["modelValue","prefix-icon"]),a(i,{type:"primary",icon:_(Le),onClick:ye},{default:l(()=>t[13]||(t[13]=[c(" 匯出 ",-1)])),_:1,__:[13]},8,["icon"])])])]),default:l(()=>[Be((D(),U(Te,{ref_key:"dataTableRef",ref:O,data:C.value,height:"500",stripe:"",border:"","row-class-name":ce},{default:l(()=>[a(b,{prop:"Name",label:"標籤名稱",width:"200",fixed:"left"},{default:l(({row:s})=>[n("div",Ze,[a($,{type:me(s.Status),size:"small",style:{"margin-right":"8px"}},{default:l(()=>[c(f(fe(s.Status)),1)]),_:2},1032,["type"]),c(" "+f(s.Name),1)])]),_:1}),a(b,{prop:"Value",label:"當前值",width:"120",align:"right"},{default:l(({row:s})=>[n("span",{class:Y(["value-text",pe(s.Status)])},f(ie(s.Value)),3)]),_:1}),a(b,{prop:"Unit",label:"單位",width:"80"}),a(b,{prop:"Description",label:"描述","min-width":"200"}),a(b,{prop:"UpdateTime",label:"更新時間",width:"180"},{default:l(({row:s})=>[c(f(de(s.UpdateTime)),1)]),_:1}),a(b,{prop:"Quality",label:"品質",width:"100"},{default:l(({row:s})=>[a($,{type:ge(s.Quality)},{default:l(()=>[c(f(_e(s.Quality)),1)]),_:2},1032,["type"])]),_:1}),a(b,{label:"操作",width:"120",fixed:"right"},{default:l(({row:s})=>[a(i,{type:"primary",size:"small",onClick:aa=>ve(s)},{default:l(()=>t[15]||(t[15]=[c(" 詳情 ",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[De,y.value]])]),_:1})):J("",!0),a(Se,{modelValue:V.value,"onUpdate:modelValue":t[8]||(t[8]=s=>V.value=s),title:"即時資料設定",width:"500px"},{footer:l(()=>[a(i,{onClick:t[7]||(t[7]=s=>V.value=!1)},{default:l(()=>t[16]||(t[16]=[c("取消",-1)])),_:1,__:[16]}),a(i,{type:"primary",onClick:he},{default:l(()=>t[17]||(t[17]=[c("保存",-1)])),_:1,__:[17]})]),default:l(()=>[a(_(X),{model:h,"label-width":"120px"},{default:l(()=>[a(S,{label:"啟用即時更新"},{default:l(()=>[a(P,{modelValue:h.enableRealtime,"onUpdate:modelValue":t[4]||(t[4]=s=>h.enableRealtime=s)},null,8,["modelValue"])]),_:1}),a(S,{label:"顯示異常數據"},{default:l(()=>[a(P,{modelValue:h.showAbnormalData,"onUpdate:modelValue":t[5]||(t[5]=s=>h.showAbnormalData=s)},null,8,["modelValue"])]),_:1}),a(S,{label:"數據保留時間"},{default:l(()=>[a(N,{modelValue:h.dataRetentionTime,"onUpdate:modelValue":t[6]||(t[6]=s=>h.dataRetentionTime=s)},{default:l(()=>[a(d,{label:"1小時",value:36e5}),a(d,{label:"6小時",value:216e5}),a(d,{label:"24小時",value:864e5}),a(d,{label:"永久保留",value:0})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const oa=Ge(ea,[["__scopeId","data-v-dbeba954"]]);export{oa as default};
