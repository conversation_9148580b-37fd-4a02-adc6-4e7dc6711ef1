import{d as q,a as A,G as C,o as B,p as v,c as E,e as M,f as s,g as t,w as o,h as a,t as i,m as c,E as h,_ as N}from"./index-329ed960.js";import{u as V,G as m,H as $,k as D,n as G,q as k,v as R,x as T,e as H}from"./index-0d74a956.js";const I={class:"user-container"},j={class:"stats-grid"},z={class:"stat-content"},F={class:"stat-icon user-icon"},J={class:"stat-info"},K={class:"stat-number"},L={class:"stat-content"},O={class:"stat-icon role-icon"},P={class:"stat-info"},Q={class:"stat-number"},W={class:"stat-content"},X={class:"stat-icon active-icon"},Y={class:"stat-info"},Z={class:"stat-number"},ss={class:"stat-content"},ts={class:"stat-icon permission-icon"},es={class:"stat-info"},os={class:"stat-number"},ls={class:"module-grid"},as={class:"module-content"},ns={class:"module-icon"},ds={class:"module-arrow"},is={class:"module-content"},us={class:"module-icon"},cs={class:"module-arrow"},_s={class:"quick-actions"},rs={class:"action-buttons"},vs={class:"system-status"},ps={class:"status-grid"},fs={class:"status-item"},ms={class:"status-item"},ys={class:"status-item"},Cs={class:"status-item"},hs={class:"status-time"},ks=q({__name:"index",setup(bs){const p=A(),n=C({userCount:0,roleCount:0,activeUserCount:0,permissionCount:0}),u=C({authService:!0,permissionCheck:!0,userSync:!0,lastUpdate:"2024-01-20 10:30:00"}),y=_=>{p.push(`/plc-user/${_}`)},b=()=>{p.push("/plc-user/list?action=add")},g=()=>{p.push("/plc-user/role?action=add")},w=()=>{h.info("用戶活動記錄功能開發中...")},x=()=>{h.info("用戶資料匯出功能開發中...")},S=async()=>{try{n.userCount=25,n.roleCount=8,n.activeUserCount=22,n.permissionCount=45}catch(_){console.error("載入系統統計失敗:",_)}};return B(async()=>{await S()}),(_,e)=>{const l=v("el-icon"),d=v("el-card"),r=v("el-button"),f=v("el-tag");return E(),M("div",I,[e[18]||(e[18]=s("div",{class:"page-header"},[s("h1",null,"用戶管理"),s("p",null,"管理系統用戶和權限設定")],-1)),s("div",j,[t(d,{class:"stat-card"},{default:o(()=>[s("div",z,[s("div",F,[t(l,null,{default:o(()=>[t(a(V))]),_:1})]),s("div",J,[s("div",K,i(n.userCount),1),e[2]||(e[2]=s("div",{class:"stat-label"},"總用戶數",-1))])])]),_:1}),t(d,{class:"stat-card"},{default:o(()=>[s("div",L,[s("div",O,[t(l,null,{default:o(()=>[t(a(m))]),_:1})]),s("div",P,[s("div",Q,i(n.roleCount),1),e[3]||(e[3]=s("div",{class:"stat-label"},"權限角色",-1))])])]),_:1}),t(d,{class:"stat-card"},{default:o(()=>[s("div",W,[s("div",X,[t(l,null,{default:o(()=>[t(a($))]),_:1})]),s("div",Y,[s("div",Z,i(n.activeUserCount),1),e[4]||(e[4]=s("div",{class:"stat-label"},"活躍用戶",-1))])])]),_:1}),t(d,{class:"stat-card"},{default:o(()=>[s("div",ss,[s("div",ts,[t(l,null,{default:o(()=>[t(a(D))]),_:1})]),s("div",es,[s("div",os,i(n.permissionCount),1),e[5]||(e[5]=s("div",{class:"stat-label"},"權限功能",-1))])])]),_:1})]),s("div",ls,[t(d,{class:"module-card",shadow:"hover",onClick:e[0]||(e[0]=U=>y("list"))},{default:o(()=>[s("div",as,[s("div",ns,[t(l,null,{default:o(()=>[t(a(G))]),_:1})]),e[6]||(e[6]=s("div",{class:"module-info"},[s("h3",null,"用戶列表"),s("p",null,"管理系統用戶帳號、狀態和基本資訊")],-1)),s("div",ds,[t(l,null,{default:o(()=>[t(a(k))]),_:1})])])]),_:1}),t(d,{class:"module-card",shadow:"hover",onClick:e[1]||(e[1]=U=>y("role"))},{default:o(()=>[s("div",is,[s("div",us,[t(l,null,{default:o(()=>[t(a(m))]),_:1})]),e[7]||(e[7]=s("div",{class:"module-info"},[s("h3",null,"權限管理"),s("p",null,"設定用戶角色和功能權限")],-1)),s("div",cs,[t(l,null,{default:o(()=>[t(a(k))]),_:1})])])]),_:1})]),s("div",_s,[t(d,null,{header:o(()=>e[8]||(e[8]=[s("span",null,"快速操作",-1)])),default:o(()=>[s("div",rs,[t(r,{type:"primary",onClick:b},{default:o(()=>[t(l,null,{default:o(()=>[t(a(R))]),_:1}),e[9]||(e[9]=c(" 新增用戶 ",-1))]),_:1,__:[9]}),t(r,{type:"success",onClick:g},{default:o(()=>[t(l,null,{default:o(()=>[t(a(m))]),_:1}),e[10]||(e[10]=c(" 新增角色 ",-1))]),_:1,__:[10]}),t(r,{onClick:w},{default:o(()=>[t(l,null,{default:o(()=>[t(a(T))]),_:1}),e[11]||(e[11]=c(" 查看活動記錄 ",-1))]),_:1,__:[11]}),t(r,{onClick:x},{default:o(()=>[t(l,null,{default:o(()=>[t(a(H))]),_:1}),e[12]||(e[12]=c(" 匯出用戶資料 ",-1))]),_:1,__:[12]})])]),_:1})]),s("div",vs,[t(d,null,{header:o(()=>e[13]||(e[13]=[s("span",null,"系統狀態",-1)])),default:o(()=>[s("div",ps,[s("div",fs,[e[14]||(e[14]=s("span",{class:"status-label"},"認證服務",-1)),t(f,{type:u.authService?"success":"danger"},{default:o(()=>[c(i(u.authService?"正常":"異常"),1)]),_:1},8,["type"])]),s("div",ms,[e[15]||(e[15]=s("span",{class:"status-label"},"權限檢查",-1)),t(f,{type:u.permissionCheck?"success":"danger"},{default:o(()=>[c(i(u.permissionCheck?"正常":"異常"),1)]),_:1},8,["type"])]),s("div",ys,[e[16]||(e[16]=s("span",{class:"status-label"},"用戶同步",-1)),t(f,{type:u.userSync?"success":"danger"},{default:o(()=>[c(i(u.userSync?"正常":"異常"),1)]),_:1},8,["type"])]),s("div",Cs,[e[17]||(e[17]=s("span",{class:"status-label"},"最後更新",-1)),s("span",hs,i(u.lastUpdate),1)])])]),_:1})])])}}});const xs=N(ks,[["__scopeId","data-v-a2a896e4"]]);export{xs as default};
