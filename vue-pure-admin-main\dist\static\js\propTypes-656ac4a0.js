const V={silent:!1,logLevel:"warn"};function p(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}const N=()=>({func:()=>{},bool:!0,string:"",number:0,array:()=>[],object:()=>({}),integer:0}),$=Object.prototype,w=$.toString,C=$.hasOwnProperty,x=/^\s*function (\w+)/;function T(e){const t=e?.type??e;if(t){const n=t.toString().match(x);return n?n[1]:""}return""}function q(e){if(e==null)return"";const t=e.constructor.toString().match(x);return t?t[1].replace(/^Async/,""):""}function _(e){return"structuredClone"in globalThis?structuredClone(e):Array.isArray(e)?[...e]:p(e)?Object.assign({},e):e}function E(){}let a=E;const b=(e,t)=>C.call(e,t),B=Number.isInteger||function(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t},g=Array.isArray||function(t){return w.call(t)==="[object Array]"},v=e=>w.call(e)==="[object Function]",O=(e,t)=>p(e)&&b(e,"_vueTypes_name")&&(!t||e._vueTypes_name===t),P=e=>p(e)&&(b(e,"type")||["_vueTypes_name","validator","default","required"].some(t=>b(e,t)));function A(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function h(e,t,n=!1){let r,i=!0,o="";p(e)?r=e:r={type:e};const s=O(r)?r._vueTypes_name+" - ":"";if(P(r)&&r.type!==null){if(r.type===void 0||r.type===!0||!r.required&&t==null)return i;g(r.type)?(i=r.type.some(u=>h(u,t,!0)===!0),o=r.type.map(u=>T(u)).join(" or ")):(o=T(r),o==="Array"?i=g(t):o==="Object"?i=p(t):o==="String"||o==="Number"||o==="Boolean"||o==="Function"?i=q(t)===o:i=t instanceof r.type)}if(!i){const u=`${s}value "${t}" should be of type "${o}"`;return n===!1?(a(u),!1):u}if(b(r,"validator")&&v(r.validator)){const u=a,c=[];if(a=d=>{c.push(d)},i=r.validator(t),a=u,!i){const d=(c.length>1?"* ":"")+c.join(`
* `);return c.length=0,n===!1?(a(d),i):d}}return i}function f(e,t){const n=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get(){return this.required=!0,this}},def:{value(i){if(i===void 0){if(this.type===Boolean||Array.isArray(this.type)&&this.type.includes(Boolean)){this.default=void 0;return}return b(this,"default")&&delete this.default,this}return!v(i)&&h(this,i,!0)!==!0?(a(`${this._vueTypes_name} - invalid default value: "${i}"`),this):(g(i)?this.default=()=>_(i):p(i)?this.default=()=>_(i):this.default=i,this)}}}),{validator:r}=n;return v(r)&&(n.validator=A(r,n)),n}function y(e,t){const n=f(e,t);return Object.defineProperty(n,"validate",{value(r){return v(this.validator)&&a(`${this._vueTypes_name} - calling .validate() will overwrite the current custom validator function. Validator info:
${JSON.stringify(this)}`),this.validator=A(r,this),this}})}function m(e){return e.replace(/^(?!\s*$)/gm,"  ")}const D=()=>y("any",{}),F=()=>y("function",{type:Function}),j=()=>y("boolean",{type:Boolean}),L=()=>y("string",{type:String}),R=()=>y("number",{type:Number}),I=()=>y("array",{type:Array}),Y=()=>y("object",{type:Object}),K=()=>f("integer",{type:Number,validator(e){const t=B(e);return t===!1&&a(`integer - "${e}" is not an integer`),t}}),M=()=>f("symbol",{validator(e){const t=typeof e=="symbol";return t===!1&&a(`symbol - invalid value "${e}"`),t}}),G=()=>Object.defineProperty({type:null,validator(e){const t=e===null;return t===!1&&a("nullable - value should be null"),t}},"_vueTypes_name",{value:"nullable"});function H(e,t="custom validation failed"){if(typeof e!="function")throw new TypeError("[VueTypes error]: You must provide a function as argument");return f(e.name||"<<anonymous function>>",{type:null,validator(n){const r=e(n);return r||a(`${this._vueTypes_name} - ${t}`),r}})}function J(e){if(!g(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");const t=`oneOf - value should be one of "${e.map(r=>typeof r=="symbol"?r.toString():r).join('", "')}".`,n={validator(r){const i=e.indexOf(r)!==-1;return i||a(t),i}};if(e.indexOf(null)===-1){const r=e.reduce((i,o)=>{if(o!=null){const s=o.constructor;i.indexOf(s)===-1&&i.push(s)}return i},[]);r.length>0&&(n.type=r)}return f("oneOf",n)}function U(e){if(!g(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");let t=!1,n=!1,r=[];for(let o=0;o<e.length;o+=1){const s=e[o];if(P(s)){if(v(s.validator)&&(t=!0),O(s,"oneOf")&&s.type){r=r.concat(s.type);continue}if(O(s,"nullable")){n=!0;continue}if(s.type===!0||!s.type){a('oneOfType - invalid usage of "true" and "null" as types.');continue}r=r.concat(s.type)}else r.push(s)}r=r.filter((o,s)=>r.indexOf(o)===s);const i=n===!1&&r.length>0?r:null;return t?f("oneOfType",{type:i,validator(o){const s=[],u=e.some(c=>{const d=h(c,o,!0);return typeof d=="string"&&s.push(d),d===!0});return u||a(`oneOfType - provided value does not match any of the ${s.length} passed-in validators:
${m(s.join(`
`))}`),u}}):f("oneOfType",{type:i})}function W(e){return f("arrayOf",{type:Array,validator(t){let n="";const r=t.every(i=>(n=h(e,i,!0),n===!0));return r||a(`arrayOf - value validation error:
${m(n)}`),r}})}function X(e){return f("instanceOf",{type:e})}function k(e){return f("objectOf",{type:Object,validator(t){let n="";if(!p(t))return!1;const r=Object.keys(t).every(i=>(n=h(e,t[i],!0),n===!0));return r||a(`objectOf - value validation error:
${m(n)}`),r}})}function z(e){const t=Object.keys(e),n=t.filter(i=>!!e[i]?.required),r=f("shape",{type:Object,validator(i){if(!p(i))return!1;const o=Object.keys(i);if(n.length>0&&n.some(s=>o.indexOf(s)===-1)){const s=n.filter(u=>o.indexOf(u)===-1);return s.length===1?a(`shape - required property "${s[0]}" is not defined.`):a(`shape - required properties "${s.join('", "')}" are not defined.`),!1}return o.every(s=>{if(t.indexOf(s)===-1)return this._vueTypes_isLoose===!0?!0:(a(`shape - shape definition does not include a "${s}" property. Allowed keys: "${t.join('", "')}".`),!1);const u=e[s],c=h(u,i[s],!0);return typeof c=="string"&&a(`shape - "${s}" property validation error:
 ${m(c)}`),c===!0})}});return Object.defineProperty(r,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(r,"loose",{get(){return this._vueTypes_isLoose=!0,this}}),r}var Q=Object.defineProperty,Z=(e,t,n)=>t in e?Q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(Z(e,typeof t!="symbol"?t+"":t,n),n);const ee=(()=>{var e;return e=class{static get any(){return D()}static get func(){return F().def(this.defaults.func)}static get bool(){return this.defaults.bool===void 0?j():j().def(this.defaults.bool)}static get string(){return L().def(this.defaults.string)}static get number(){return R().def(this.defaults.number)}static get array(){return I().def(this.defaults.array)}static get object(){return Y().def(this.defaults.object)}static get integer(){return K().def(this.defaults.integer)}static get symbol(){return M()}static get nullable(){return G()}static extend(...t){a("VueTypes.extend has been removed. Use the ES6+ method instead. See https://dwightjack.github.io/vue-types/advanced/extending-vue-types.html#extending-namespaced-validators-in-es6 for details.")}},l(e,"defaults",{}),l(e,"sensibleDefaults"),l(e,"config",V),l(e,"custom",H),l(e,"oneOf",J),l(e,"instanceOf",X),l(e,"oneOfType",U),l(e,"arrayOf",W),l(e,"objectOf",k),l(e,"shape",z),l(e,"utils",{validate(t,n){return h(n,t,!0)===!0},toType(t,n,r=!1){return r?y(t,n):f(t,n)}}),e})();function S(e=N()){var t;return t=class extends ee{static get sensibleDefaults(){return{...this.defaults}}static set sensibleDefaults(n){if(n===!1){this.defaults={};return}if(n===!0){this.defaults={...e};return}this.defaults={...n}}},l(t,"defaults",{...e}),t}class re extends S(){}const te=S({func:void 0,bool:void 0,string:void 0,number:void 0,object:void 0,integer:void 0});class ne extends te{static get style(){return y("style",{type:[String,Object]})}static get VNodeChild(){return y("VNodeChild",{type:void 0})}}export{ne as p};
