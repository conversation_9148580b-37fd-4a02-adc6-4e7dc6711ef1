import{d as T,ao as B,aB as A,G as I,D as E,R as W,am as G,o as M,g as a,X as C,h as n,r as F,ar as U,T as L,m as f,aw as $,ap as O,e as D,w as c,p as v,q as P,c as d,Y as q,l as S,n as g,f as o,t as N,V as X,af as Y,a0 as H,A as J,aC as K,_ as Q}from"./index-329ed960.js";import Z from"./index-a6b2287a.js";import{p as m}from"./propTypes-656ac4a0.js";import{_ as ee}from"./ChartBar.vue_vue_type_script_setup_true_lang-e8dc91fe.js";import{_ as te}from"./ChartLine.vue_vue_type_script_setup_true_lang-0afd6a7b.js";import{_ as ae}from"./ChartRound.vue_vue_type_script_setup_true_lang-b495772c.js";import{R as le}from"./index-303641a5.js";import{c as ne,b as j,p as oe,l as se}from"./columns-30d3007e.js";import"./hooks-7d897f55.js";const k=T({name:"ReCol",props:{value:{type:Number,default:24}},render(){const t=this.$attrs,i=this.value;return B(A,{xs:i,sm:i,md:i,lg:i,xl:i,...t},{default:()=>this.$slots.default()})}}),re={startVal:m.number.def(0),endVal:m.number.def(2020),duration:m.number.def(1300),autoplay:m.bool.def(!0),decimals:{type:Number,required:!1,default:0,validator(t){return t>=0}},color:m.string.def(),fontSize:m.string.def(),decimal:m.string.def("."),separator:m.string.def(","),prefix:m.string.def(""),suffix:m.string.def(""),useEasing:m.bool.def(!0),easingFn:{type:Function,default(t,i,e,_){return e*(-Math.pow(2,-10*t/_)+1)*1024/1023+i}}},ie=T({name:"ReNormalCountTo",props:re,emits:["mounted","callback"],setup(t,{emit:i}){const e=I({localStartVal:t.startVal,displayValue:w(t.startVal),printVal:null,paused:!1,localDuration:t.duration,startTime:null,timestamp:null,remaining:null,rAF:null,color:null,fontSize:"16px"}),_=E(()=>t.startVal>t.endVal);W([()=>t.startVal,()=>t.endVal],()=>{t.autoplay&&h()});function h(){const{startVal:s,duration:V,color:b,fontSize:r}=t;e.localStartVal=s,e.startTime=null,e.localDuration=V,e.paused=!1,e.color=b,e.fontSize=r,e.rAF=requestAnimationFrame(u)}function u(s){const{useEasing:V,easingFn:b,endVal:r}=t;e.startTime||(e.startTime=s),e.timestamp=s;const p=s-e.startTime;e.remaining=e.localDuration-p,V?n(_)?e.printVal=e.localStartVal-b(p,0,e.localStartVal-r,e.localDuration):e.printVal=b(p,e.localStartVal,r-e.localStartVal,e.localDuration):n(_)?e.printVal=e.localStartVal-(e.localStartVal-r)*(p/e.localDuration):e.printVal=e.localStartVal+(r-e.localStartVal)*(p/e.localDuration),n(_)?e.printVal=e.printVal<r?r:e.printVal:e.printVal=e.printVal>r?r:e.printVal,e.displayValue=w(e.printVal),p<e.localDuration?e.rAF=requestAnimationFrame(u):i("callback")}function w(s){const{decimals:V,decimal:b,separator:r,suffix:p,prefix:R}=t;s=Number(s).toFixed(V),s+="";const y=s.split(".");let l=y[0];const x=y.length>1?b+y[1]:"",z=/(\d+)(\d{3})/;if(r&&!G(r))for(;z.test(l);)l=l.replace(z,"$1"+r+"$2");return R+l+x+p}return M(()=>{t.autoplay&&h(),i("mounted")}),()=>a(C,null,[a("span",{style:{color:t.color,fontSize:t.fontSize}},[e.displayValue])])}});const ue={delay:m.number.def(1),blur:m.number.def(2),i:{type:Number,required:!1,default:0,validator(t){return t<10&&t>=0&&Number.isInteger(t)}}},ce=T({name:"ReboundCountTo",props:ue,setup(t){const i=F(),e=F(null);return U(()=>{const _=navigator.userAgent.toLowerCase(),h=w=>w.test(_);h(/safari/g)&&!h(/chrome/g)&&(e.value=setTimeout(()=>{i.value.setAttribute("style",`
        animation: none;
        transform: translateY(calc(var(--i) * -9.09%))
      `)},t.delay*1e3))}),L(()=>{clearTimeout(n(e))}),()=>a(C,null,[a("div",{class:"scroll-num",style:{"--i":t.i,"--delay":t.delay}},[a("ul",{ref:"ulRef",style:{fontSize:"32px"}},[a("li",null,[f("0")]),a("li",null,[f("1")]),a("li",null,[f("2")]),a("li",null,[f("3")]),a("li",null,[f("4")]),a("li",null,[f("5")]),a("li",null,[f("6")]),a("li",null,[f("7")]),a("li",null,[f("8")]),a("li",null,[f("9")]),a("li",null,[f("0")])]),a("svg",{width:"0",height:"0"},[a("filter",{id:"blur"},[a("feGaussianBlur",{in:"SourceGraphic",stdDeviation:`0 ${t.blur}`},null)])])])])}}),de=$(ie);$(ce);function me(t){return T({name:"ReFlicker",render(){return B("div",{class:"point point-flicker",style:{"--point-width":t?.width??"12px","--point-height":t?.height??"12px","--point-background":t?.background??"var(--el-color-primary)","--point-border-radius":t?.borderRadius??"50%","--point-scale":t?.scale??"2"}},{default:()=>[]})}})}const fe={class:"flex justify-between"},pe={class:"text-md font-medium"},_e={class:"flex justify-between items-start mt-3"},ye={class:"w-1/2"},xe={class:"font-medium text-green-500"},be={class:"flex justify-between"},ge={class:"flex justify-between items-start mt-3"},Ve={class:"text-nowrap ml-2 text-text_color_regular text-sm"},ve={class:"text-text_color_regular text-sm"},he=T({name:"Welcome",__name:"index",setup(t){const{isDark:i}=O();let e=F(1);const _=[{label:"上周"},{label:"本周"}];return(h,u)=>{const w=v("IconifyIconOffline"),s=v("el-card"),V=v("el-progress"),b=v("el-timeline-item"),r=v("el-timeline"),p=v("el-scrollbar"),R=v("el-row"),y=P("motion");return d(),D("div",null,[a(R,{gutter:24,justify:"space-around"},{default:c(()=>[(d(!0),D(C,null,q(n(ne),(l,x)=>S((d(),g(n(k),{key:x,class:"mb-[18px]",value:6,md:12,sm:12,xs:24,initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:80*(x+1)}}},{default:c(()=>[a(s,{class:"line-card",shadow:"never"},{default:c(()=>[o("div",fe,[o("span",pe,N(l.name),1),o("div",{class:"w-8 h-8 flex justify-center items-center rounded-md",style:X({backgroundColor:n(i)?"transparent":l.bgColor})},[a(w,{icon:l.icon,color:l.color,width:"18",height:"18"},null,8,["icon","color"])],4)]),o("div",_e,[o("div",ye,[a(n(de),{duration:l.duration,fontSize:"1.6em",startVal:100,endVal:l.value},null,8,["duration","endVal"]),o("p",xe,N(l.percent),1)]),l.data.length>1?(d(),g(n(te),{key:0,class:"w-1/2!",color:l.color,data:l.data},null,8,["color","data"])):(d(),g(n(ae),{key:1,class:"w-1/2!"}))])]),_:2},1024)]),_:2},1032,["enter"])),[[y]])),128)),S((d(),g(n(k),{class:"mb-[18px]",value:18,xs:24,initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:400}}},{default:c(()=>[a(s,{class:"bar-card",shadow:"never"},{default:c(()=>[o("div",be,[u[1]||(u[1]=o("span",{class:"text-md font-medium"},"分析概览",-1)),a(n(le),{modelValue:n(e),"onUpdate:modelValue":u[0]||(u[0]=l=>Y(e)?e.value=l:e=l),options:_},null,8,["modelValue"])]),o("div",ge,[a(n(ee),{requireData:n(j)[n(e)].requireData,questionData:n(j)[n(e)].questionData},null,8,["requireData","questionData"])])]),_:1})]),_:1})),[[y]]),S((d(),g(n(k),{class:"mb-[18px]",value:6,xs:24,initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:480}}},{default:c(()=>[a(s,{shadow:"never"},{default:c(()=>[u[2]||(u[2]=o("div",{class:"flex justify-between"},[o("span",{class:"text-md font-medium"},"解决概率")],-1)),(d(!0),D(C,null,q(n(oe),(l,x)=>(d(),D("div",{key:x,class:H(["flex","justify-between","items-start",x===0?"mt-8":"mt-[2.15rem]"])},[a(V,{"text-inside":!0,percentage:l.percentage,"stroke-width":21,color:l.color,striped:"","striped-flow":"",duration:l.duration},null,8,["percentage","color","duration"]),o("span",Ve,N(l.week),1)],2))),128))]),_:1,__:[2]})]),_:1})),[[y]]),S((d(),g(n(k),{class:"mb-[18px]",value:18,xs:24,initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:560}}},{default:c(()=>[a(s,{shadow:"never",class:"h-[580px]"},{default:c(()=>[u[3]||(u[3]=o("div",{class:"flex justify-between"},[o("span",{class:"text-md font-medium"},"数据统计")],-1)),a(Z,{class:"mt-3"})]),_:1,__:[3]})]),_:1})),[[y]]),S((d(),g(n(k),{class:"mb-[18px]",value:6,xs:24,initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:640}}},{default:c(()=>[a(s,{shadow:"never"},{default:c(()=>[u[4]||(u[4]=o("div",{class:"flex justify-between"},[o("span",{class:"text-md font-medium"},"最新动态")],-1)),a(p,{"max-height":"504",class:"mt-3"},{default:c(()=>[a(r,null,{default:c(()=>[(d(!0),D(C,null,q(n(se),(l,x)=>(d(),g(b,{key:x,center:"",placement:"top",icon:J(n(me)({background:n(K)({randomizeHue:!0})})),timestamp:l.date},{default:c(()=>[o("p",ve,N(`新增 ${l.requiredNumber} 条问题，${l.resolveNumber} 条已解决`),1)]),_:2},1032,["icon","timestamp"]))),128))]),_:1})]),_:1})]),_:1,__:[4]})]),_:1})),[[y]])]),_:1})])}}});const Fe=Q(he,[["__scopeId","data-v-c4c74332"]]);export{Fe as default};
