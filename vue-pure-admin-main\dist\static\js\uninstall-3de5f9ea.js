import{a as M,r as O,A as Q,w as X}from"./index-0d74a956.js";import{u as Y}from"./dataService-42a3bdf6.js";import{d as Z,r as p,G as ee,D as le,p as u,q as te,c as N,e as ae,f as n,g as e,w as t,m as i,h as V,l as oe,n as ne,t as f,aH as ie}from"./index-329ed960.js";const de={class:"uninstall-container"},ue={class:"card-header"},re={class:"header-actions"},se={class:"filter-section"},pe={class:"program-name"},me={class:"pagination-container"},fe={style:{"margin-top":"10px"}},ge={class:"execute-warning"},_e={class:"warning-content"},Ve=Z({__name:"uninstall",setup(ve){Y();const q=p(),R=p(),B=p(!1),c=p(!1),x=p(!1),b=p(""),w=p(""),k=p(""),P=p(1),$=p(20),K=p(0),W=p([]),E=p([]),S=p(null),d=ee({id:"",name:"",description:"",priority:"medium",loadCapacity:0,executionTime:30,delayTime:0,autoExecute:!1,stages:[],status:"active"}),A={name:[{required:!0,message:"請輸入程序名稱",trigger:"blur"}],description:[{required:!0,message:"請輸入程序描述",trigger:"blur"}],priority:[{required:!0,message:"請選擇優先級",trigger:"change"}],loadCapacity:[{required:!0,message:"請輸入卸載容量",trigger:"blur"}],executionTime:[{required:!0,message:"請輸入執行時間",trigger:"blur"}]},I=le(()=>{let o=W.value;return b.value&&(o=o.filter(l=>l.name.toLowerCase().includes(b.value.toLowerCase())||l.description.toLowerCase().includes(b.value.toLowerCase()))),w.value&&(o=o.filter(l=>l.status===w.value)),k.value&&(o=o.filter(l=>l.priority===k.value)),o});return(o,l)=>{const s=u("el-button"),T=u("el-icon"),U=u("el-input"),g=u("el-col"),m=u("el-option"),h=u("el-select"),z=u("el-row"),r=u("el-table-column"),D=u("el-tag"),F=u("el-table"),j=u("el-pagination"),G=u("el-card"),_=u("el-form-item"),C=u("el-input-number"),H=u("el-switch"),L=u("el-dialog"),J=te("loading");return N(),ae("div",de,[l[37]||(l[37]=n("div",{class:"page-header"},[n("h2",null,"電力卸載"),n("p",null,"電力卸載程序管理，用於緊急情況下的電力負載控制")],-1)),e(G,{class:"uninstall-card"},{header:t(()=>[n("div",ue,[l[20]||(l[20]=n("span",null,"卸載程序管理",-1)),n("div",re,[e(s,{type:"primary",onClick:l[0]||(l[0]=a=>c.value=!0)},{default:t(()=>l[17]||(l[17]=[i(" 新增卸載程序 ",-1)])),_:1,__:[17]}),e(s,{type:"success",disabled:E.value.length===0,onClick:o.batchExecute},{default:t(()=>l[18]||(l[18]=[i(" 批次執行 ",-1)])),_:1,__:[18]},8,["disabled","onClick"]),e(s,{type:"danger",disabled:E.value.length===0,onClick:o.batchDelete},{default:t(()=>l[19]||(l[19]=[i(" 批次刪除 ",-1)])),_:1,__:[19]},8,["disabled","onClick"])])])]),default:t(()=>[n("div",se,[e(z,{gutter:20},{default:t(()=>[e(g,{span:8},{default:t(()=>[e(U,{modelValue:b.value,"onUpdate:modelValue":l[1]||(l[1]=a=>b.value=a),placeholder:"搜尋程序名稱...",clearable:"",onInput:o.handleSearch},{prefix:t(()=>[e(T,null,{default:t(()=>[e(V(M))]),_:1})]),_:1},8,["modelValue","onInput"])]),_:1}),e(g,{span:6},{default:t(()=>[e(h,{modelValue:w.value,"onUpdate:modelValue":l[2]||(l[2]=a=>w.value=a),placeholder:"狀態篩選",clearable:"",onChange:o.handleFilter},{default:t(()=>[e(m,{label:"全部",value:""}),e(m,{label:"啟用",value:"active"}),e(m,{label:"停用",value:"inactive"}),e(m,{label:"執行中",value:"running"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(g,{span:6},{default:t(()=>[e(h,{modelValue:k.value,"onUpdate:modelValue":l[3]||(l[3]=a=>k.value=a),placeholder:"優先級篩選",clearable:"",onChange:o.handleFilter},{default:t(()=>[e(m,{label:"全部",value:""}),e(m,{label:"高",value:"high"}),e(m,{label:"中",value:"medium"}),e(m,{label:"低",value:"low"})]),_:1},8,["modelValue","onChange"])]),_:1}),e(g,{span:4},{default:t(()=>[e(s,{type:"info",onClick:o.refreshList},{default:t(()=>[e(T,null,{default:t(()=>[e(V(O))]),_:1}),l[21]||(l[21]=i(" 刷新 ",-1))]),_:1,__:[21]},8,["onClick"])]),_:1})]),_:1})]),oe((N(),ne(F,{ref_key:"tableRef",ref:R,data:I.value,stripe:"",border:"",onSelectionChange:o.handleSelectionChange},{default:t(()=>[e(r,{type:"selection",width:"55"}),e(r,{prop:"name",label:"程序名稱",width:"200"},{default:t(({row:a})=>[n("div",pe,[e(T,{class:"program-icon"},{default:t(()=>[e(V(Q))]),_:1}),i(" "+f(a.name),1)])]),_:1}),e(r,{prop:"description",label:"描述","min-width":"200"}),e(r,{prop:"priority",label:"優先級",width:"100"},{default:t(({row:a})=>[e(D,{type:o.getPriorityType(a.priority)},{default:t(()=>[i(f(o.getPriorityText(a.priority)),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"loadCapacity",label:"卸載容量(kW)",width:"150",align:"right"},{default:t(({row:a})=>[i(f(o.formatNumber(a.loadCapacity)),1)]),_:1}),e(r,{prop:"executionTime",label:"執行時間(秒)",width:"120",align:"right"}),e(r,{prop:"status",label:"狀態",width:"100"},{default:t(({row:a})=>[e(D,{type:o.getStatusType(a.status)},{default:t(()=>[i(f(o.getStatusText(a.status)),1)]),_:2},1032,["type"])]),_:1}),e(r,{prop:"lastExecuted",label:"最後執行",width:"180"},{default:t(({row:a})=>[i(f(o.formatDateTime(a.lastExecuted)),1)]),_:1}),e(r,{prop:"createTime",label:"建立時間",width:"180"},{default:t(({row:a})=>[i(f(o.formatDateTime(a.createTime)),1)]),_:1}),e(r,{label:"操作",width:"250",fixed:"right"},{default:t(({row:a})=>[e(s,{type:"success",size:"small",disabled:a.status==="running",onClick:v=>o.executeProgram(a)},{default:t(()=>l[22]||(l[22]=[i(" 執行 ",-1)])),_:2,__:[22]},1032,["disabled","onClick"]),e(s,{type:"primary",size:"small",onClick:v=>o.editProgram(a)},{default:t(()=>l[23]||(l[23]=[i(" 編輯 ",-1)])),_:2,__:[23]},1032,["onClick"]),e(s,{type:"warning",size:"small",onClick:v=>o.toggleProgramStatus(a)},{default:t(()=>[i(f(a.status==="active"?"停用":"啟用"),1)]),_:2},1032,["onClick"]),e(s,{type:"danger",size:"small",onClick:v=>o.deleteProgram(a)},{default:t(()=>l[24]||(l[24]=[i(" 刪除 ",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[J,B.value]]),n("div",me,[e(j,{"current-page":P.value,"onUpdate:currentPage":l[4]||(l[4]=a=>P.value=a),"page-size":$.value,"onUpdate:pageSize":l[5]||(l[5]=a=>$.value=a),total:K.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(L,{modelValue:c.value,"onUpdate:modelValue":l[14]||(l[14]=a=>c.value=a),title:d.id?"編輯卸載程序":"新增卸載程序",width:"700px"},{footer:t(()=>[e(s,{onClick:l[13]||(l[13]=a=>c.value=!1)},{default:t(()=>l[31]||(l[31]=[i("取消",-1)])),_:1,__:[31]}),e(s,{type:"primary",onClick:o.saveProgram},{default:t(()=>l[32]||(l[32]=[i("確認",-1)])),_:1,__:[32]},8,["onClick"])]),default:t(()=>[e(V(ie),{ref_key:"programFormRef",ref:q,model:d,rules:A,"label-width":"120px"},{default:t(()=>[e(_,{label:"程序名稱",prop:"name"},{default:t(()=>[e(U,{modelValue:d.name,"onUpdate:modelValue":l[6]||(l[6]=a=>d.name=a),placeholder:"請輸入程序名稱"},null,8,["modelValue"])]),_:1}),e(_,{label:"描述",prop:"description"},{default:t(()=>[e(U,{modelValue:d.description,"onUpdate:modelValue":l[7]||(l[7]=a=>d.description=a),type:"textarea",rows:3,placeholder:"請輸入程序描述"},null,8,["modelValue"])]),_:1}),e(z,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(_,{label:"優先級",prop:"priority"},{default:t(()=>[e(h,{modelValue:d.priority,"onUpdate:modelValue":l[8]||(l[8]=a=>d.priority=a),style:{width:"100%"}},{default:t(()=>[e(m,{label:"高",value:"high"}),e(m,{label:"中",value:"medium"}),e(m,{label:"低",value:"low"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(_,{label:"卸載容量",prop:"loadCapacity"},{default:t(()=>[e(C,{modelValue:d.loadCapacity,"onUpdate:modelValue":l[9]||(l[9]=a=>d.loadCapacity=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),l[25]||(l[25]=n("span",{style:{"margin-left":"8px"}},"kW",-1))]),_:1,__:[25]})]),_:1})]),_:1}),e(z,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(_,{label:"執行時間",prop:"executionTime"},{default:t(()=>[e(C,{modelValue:d.executionTime,"onUpdate:modelValue":l[10]||(l[10]=a=>d.executionTime=a),min:1,max:3600,style:{width:"100%"}},null,8,["modelValue"]),l[26]||(l[26]=n("span",{style:{"margin-left":"8px"}},"秒",-1))]),_:1,__:[26]})]),_:1}),e(g,{span:12},{default:t(()=>[e(_,{label:"延遲時間",prop:"delayTime"},{default:t(()=>[e(C,{modelValue:d.delayTime,"onUpdate:modelValue":l[11]||(l[11]=a=>d.delayTime=a),min:0,max:300,style:{width:"100%"}},null,8,["modelValue"]),l[27]||(l[27]=n("span",{style:{"margin-left":"8px"}},"秒",-1))]),_:1,__:[27]})]),_:1})]),_:1}),e(_,{label:"卸載階段"},{default:t(()=>[e(F,{data:d.stages,border:"",style:{width:"100%"}},{default:t(()=>[e(r,{prop:"order",label:"順序",width:"80",align:"center"}),e(r,{prop:"deviceName",label:"設備名稱",width:"150"},{default:t(({row:a,$index:v})=>[e(U,{modelValue:a.deviceName,"onUpdate:modelValue":y=>a.deviceName=y,placeholder:"設備名稱"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{prop:"loadKw",label:"負載(kW)",width:"120"},{default:t(({row:a,$index:v})=>[e(C,{modelValue:a.loadKw,"onUpdate:modelValue":y=>a.loadKw=y,min:0,precision:2,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{prop:"delaySeconds",label:"延遲(秒)",width:"100"},{default:t(({row:a,$index:v})=>[e(C,{modelValue:a.delaySeconds,"onUpdate:modelValue":y=>a.delaySeconds=y,min:0,max:60,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{label:"操作",width:"80"},{default:t(({row:a,$index:v})=>[e(s,{type:"danger",size:"small",onClick:y=>o.removeStage(v)},{default:t(()=>l[28]||(l[28]=[i(" 刪除 ",-1)])),_:2,__:[28]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),n("div",fe,[e(s,{type:"primary",onClick:o.addStage},{default:t(()=>l[29]||(l[29]=[i("新增階段",-1)])),_:1,__:[29]},8,["onClick"])])]),_:1}),e(_,{label:"自動執行"},{default:t(()=>[e(H,{modelValue:d.autoExecute,"onUpdate:modelValue":l[12]||(l[12]=a=>d.autoExecute=a)},null,8,["modelValue"]),l[30]||(l[30]=n("span",{style:{"margin-left":"10px",color:"#909399","font-size":"12px"}}," 啟用後將在緊急情況下自動執行 ",-1))]),_:1,__:[30]})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(L,{modelValue:x.value,"onUpdate:modelValue":l[16]||(l[16]=a=>x.value=a),title:"執行確認",width:"500px"},{footer:t(()=>[e(s,{onClick:l[15]||(l[15]=a=>x.value=!1)},{default:t(()=>l[35]||(l[35]=[i("取消",-1)])),_:1,__:[35]}),e(s,{type:"danger",onClick:o.confirmExecute},{default:t(()=>l[36]||(l[36]=[i("確認執行",-1)])),_:1,__:[36]},8,["onClick"])]),default:t(()=>[n("div",ge,[e(T,{class:"warning-icon"},{default:t(()=>[e(V(X))]),_:1}),n("div",_e,[l[33]||(l[33]=n("h3",null,"警告：即將執行電力卸載程序",-1)),n("p",null,"程序名稱："+f(S.value?.name),1),n("p",null,"卸載容量："+f(S.value?.loadCapacity)+" kW",1),n("p",null,"執行時間："+f(S.value?.executionTime)+" 秒",1),l[34]||(l[34]=n("p",{style:{color:"#f56c6c","font-weight":"bold"}}," 此操作將影響電力供應，請確認是否繼續？ ",-1))])])]),_:1},8,["modelValue"])])}}});export{Ve as default};
