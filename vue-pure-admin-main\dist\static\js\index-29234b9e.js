import{w as ie,i as ce,b as ue,d as me,r as pe,c as _e,s as fe,a as ge}from"./index-0d74a956.js";import{a as b}from"./alarm-635d9769.js";import{p as S}from"./signalr-7c260a48.js";import{u as ve}from"./dataService-42a3bdf6.js";import{d as be,r as x,G as L,o as we,as as he,p as n,q as ye,c as R,e as Ae,f as o,g as e,w as l,h as p,t as i,m as d,l as Ie,n as M,j as Se,E as m,aF as xe,_ as ke}from"./index-329ed960.js";const Ce={class:"alarm-container"},Ve={class:"stats-cards"},ze={class:"stat-content"},Te={class:"stat-icon"},De={class:"stat-info"},Le={class:"stat-value"},Re={class:"stat-content"},Ue={class:"stat-icon"},Be={class:"stat-info"},Pe={class:"stat-value"},Ne={class:"stat-content"},$e={class:"stat-icon"},Me={class:"stat-info"},je={class:"stat-value"},Ee={class:"stat-content"},He={class:"stat-icon"},Oe={class:"stat-info"},qe={class:"stat-value"},Fe={class:"toolbar"},Ge={class:"toolbar-left"},Ke={class:"toolbar-right"},We={class:"pagination-container"},Je=be({__name:"index",setup(Qe){const k=ve(),C=x(!1),U=x([]),_=x([]),D=x(""),w=x(!1),h=L({totalAlarms:0,unacknowledgedAlarms:0,criticalAlarms:0,warningAlarms:0,infoAlarms:0}),c=L({pageIndex:1,pageSize:20,total:0}),r=L({enableSound:!0,enablePopup:!0,autoRefreshInterval:1e4,maxDisplayCount:100});let g=null;const y=async()=>{try{C.value=!0;const t={pageIndex:c.pageIndex,pageSize:c.pageSize,tagName:D.value||void 0,customerId:k.userInfo.customerId},a=await b.getAlarmList(t);U.value=a.Items||[],c.total=a.TotalCount||0}catch(t){console.error("載入警報列表失敗:",t),m.error(t.message||"載入警報列表失敗")}finally{C.value=!1}},B=async()=>{try{const t=await b.getAlarmStatistics(k.userInfo.customerId);Object.assign(h,t)}catch(t){console.error("載入警報統計失敗:",t)}},j=t=>new Date(t).toLocaleString("zh-TW"),E=t=>{switch(t){case 1:return"danger";case 2:return"warning";case 3:return"info";default:return"info"}},H=t=>{switch(t){case 1:return"嚴重";case 2:return"警告";case 3:return"資訊";default:return"未知"}},v=async()=>{await Promise.all([y(),B()])},O=()=>{c.pageIndex=1,y()},q=t=>{_.value=t},F=t=>{c.pageSize=t,c.pageIndex=1,y()},G=t=>{c.pageIndex=t,y()},K=async t=>{try{await b.acknowledgeAlarm({AlarmSummaryId:t.AlarmSummaryId}),m.success("警報確認成功"),await v()}catch(a){console.error("確認警報失敗:",a),m.error(a.message||"確認警報失敗")}},W=async()=>{if(_.value.length===0){m.warning("請選擇要確認的警報");return}try{await xe.confirm(`確定要確認選中的 ${_.value.length} 個警報嗎？`,"批量確認警報",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const t=_.value.map(a=>a.AlarmSummaryId);await b.batchAcknowledgeAlarms({alarmSummaryIds:t}),m.success("批量確認成功"),_.value=[],await v()}catch(t){t!=="cancel"&&(console.error("批量確認失敗:",t),m.error(t.message||"批量確認失敗"))}},J=t=>{m.info("警報詳情功能開發中...")},Q=async()=>{try{await b.updateAlarmSettings({customerId:k.userInfo.customerId,...r}),m.success("設定保存成功"),w.value=!1,P()}catch(t){console.error("保存設定失敗:",t),m.error(t.message||"保存設定失敗")}},P=()=>{g&&(clearInterval(g),g=null),r.autoRefreshInterval>0&&(g=setInterval(()=>{v()},r.autoRefreshInterval))},X=async()=>{try{const t=await b.getAlarmSettings(k.userInfo.customerId);Object.assign(r,t),P()}catch(t){console.error("載入警報設定失敗:",t)}},Y=()=>{S.on("alarm:data",t=>{v(),r.enableSound,r.enablePopup&&m({message:`新警報: ${t.alarm.TagName} - ${t.alarm.AlarmMessage}`,type:"warning",duration:5e3})}),S.on("alarm:acknowledged",t=>{v()})};return we(async()=>{await Promise.all([y(),B(),X()]),Y();try{await S.connectHub("alarm")}catch(t){console.error("連接警報 Hub 失敗:",t)}}),he(()=>{g&&clearInterval(g),S.off("alarm:data"),S.off("alarm:acknowledged")}),(t,a)=>{const V=n("el-icon"),A=n("el-card"),z=n("el-col"),Z=n("el-row"),f=n("el-button"),ee=n("el-input"),u=n("el-table-column"),N=n("el-tag"),ae=n("el-table"),te=n("el-pagination"),$=n("el-switch"),T=n("el-form-item"),I=n("el-option"),le=n("el-select"),se=n("el-input-number"),oe=n("el-form"),ne=n("el-dialog"),re=ye("loading");return R(),Ae("div",Ce,[a[20]||(a[20]=o("div",{class:"page-header"},[o("h2",null,"警報監控系統"),o("p",null,"即時監控系統警報狀態，快速響應異常情況")],-1)),o("div",Ve,[e(Z,{gutter:20},{default:l(()=>[e(z,{span:6},{default:l(()=>[e(A,{class:"stat-card critical"},{default:l(()=>[o("div",ze,[o("div",Te,[e(V,null,{default:l(()=>[e(p(ie))]),_:1})]),o("div",De,[o("div",Le,i(h.criticalAlarms),1),a[10]||(a[10]=o("div",{class:"stat-label"},"嚴重警報",-1))])])]),_:1})]),_:1}),e(z,{span:6},{default:l(()=>[e(A,{class:"stat-card warning"},{default:l(()=>[o("div",Re,[o("div",Ue,[e(V,null,{default:l(()=>[e(p(ce))]),_:1})]),o("div",Be,[o("div",Pe,i(h.warningAlarms),1),a[11]||(a[11]=o("div",{class:"stat-label"},"警告警報",-1))])])]),_:1})]),_:1}),e(z,{span:6},{default:l(()=>[e(A,{class:"stat-card unack"},{default:l(()=>[o("div",Ne,[o("div",$e,[e(V,null,{default:l(()=>[e(p(ue))]),_:1})]),o("div",Me,[o("div",je,i(h.unacknowledgedAlarms),1),a[12]||(a[12]=o("div",{class:"stat-label"},"未確認警報",-1))])])]),_:1})]),_:1}),e(z,{span:6},{default:l(()=>[e(A,{class:"stat-card total"},{default:l(()=>[o("div",Ee,[o("div",He,[e(V,null,{default:l(()=>[e(p(me))]),_:1})]),o("div",Oe,[o("div",qe,i(h.totalAlarms),1),a[13]||(a[13]=o("div",{class:"stat-label"},"總警報數",-1))])])]),_:1})]),_:1})]),_:1})]),o("div",Fe,[o("div",Ge,[e(f,{type:"primary",icon:p(pe),onClick:v,loading:C.value},{default:l(()=>a[14]||(a[14]=[d(" 刷新 ",-1)])),_:1,__:[14]},8,["icon","loading"]),e(f,{type:"success",icon:p(_e),onClick:W,disabled:_.value.length===0},{default:l(()=>[d(" 批量確認 ("+i(_.value.length)+") ",1)]),_:1},8,["icon","disabled"]),e(f,{type:"info",icon:p(fe),onClick:a[0]||(a[0]=s=>w.value=!0)},{default:l(()=>a[15]||(a[15]=[d(" 設定 ",-1)])),_:1,__:[15]},8,["icon"])]),o("div",Ke,[e(ee,{modelValue:D.value,"onUpdate:modelValue":a[1]||(a[1]=s=>D.value=s),placeholder:"搜尋標籤名稱...","prefix-icon":p(ge),clearable:"",style:{width:"250px"},onInput:O},null,8,["modelValue","prefix-icon"])])]),e(A,{class:"alarm-table-card"},{default:l(()=>[Ie((R(),M(ae,{ref:"alarmTableRef",data:U.value,onSelectionChange:q,height:"500",stripe:"",border:""},{default:l(()=>[e(u,{type:"selection",width:"55"}),e(u,{prop:"AlarmTime",label:"警報時間",width:"180"},{default:l(({row:s})=>[d(i(j(s.AlarmTime)),1)]),_:1}),e(u,{prop:"TagName",label:"標籤名稱",width:"150"}),e(u,{prop:"AlarmMessage",label:"警報訊息","min-width":"200"}),e(u,{prop:"AlarmLevel",label:"警報等級",width:"100"},{default:l(({row:s})=>[e(N,{type:E(s.AlarmLevel)},{default:l(()=>[d(i(H(s.AlarmLevel)),1)]),_:2},1032,["type"])]),_:1}),e(u,{prop:"CurrentValue",label:"當前值",width:"100"},{default:l(({row:s})=>[d(i(s.CurrentValue??"-")+" "+i(s.Unit||""),1)]),_:1}),e(u,{prop:"IsAcknowledged",label:"確認狀態",width:"100"},{default:l(({row:s})=>[e(N,{type:s.IsAcknowledged?"success":"danger"},{default:l(()=>[d(i(s.IsAcknowledged?"已確認":"未確認"),1)]),_:2},1032,["type"])]),_:1}),e(u,{prop:"AcknowledgedBy",label:"確認人員",width:"120"},{default:l(({row:s})=>[d(i(s.AcknowledgedBy||"-"),1)]),_:1}),e(u,{prop:"ValueAddress",label:"數值位址",width:"120"},{default:l(({row:s})=>[d(i(s.ValueAddress||"-"),1)]),_:1}),e(u,{prop:"DeviceId",label:"設備",width:"100"},{default:l(({row:s})=>[d(i(s.DeviceId||"-"),1)]),_:1}),e(u,{prop:"RegionId",label:"區域",width:"100"},{default:l(({row:s})=>[d(i(s.RegionId||"-"),1)]),_:1}),e(u,{label:"操作",width:"120",fixed:"right"},{default:l(({row:s})=>[s.IsAcknowledged?Se("",!0):(R(),M(f,{key:0,type:"primary",size:"small",onClick:de=>K(s)},{default:l(()=>a[16]||(a[16]=[d(" 確認 ",-1)])),_:2,__:[16]},1032,["onClick"])),e(f,{type:"info",size:"small",onClick:de=>J(s)},{default:l(()=>a[17]||(a[17]=[d(" 詳情 ",-1)])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[re,C.value]]),o("div",We,[e(te,{"current-page":c.pageIndex,"onUpdate:currentPage":a[2]||(a[2]=s=>c.pageIndex=s),"page-size":c.pageSize,"onUpdate:pageSize":a[3]||(a[3]=s=>c.pageSize=s),total:c.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:G},null,8,["current-page","page-size","total"])])]),_:1}),e(ne,{modelValue:w.value,"onUpdate:modelValue":a[9]||(a[9]=s=>w.value=s),title:"警報設定",width:"500px"},{footer:l(()=>[e(f,{onClick:a[8]||(a[8]=s=>w.value=!1)},{default:l(()=>a[18]||(a[18]=[d("取消",-1)])),_:1,__:[18]}),e(f,{type:"primary",onClick:Q},{default:l(()=>a[19]||(a[19]=[d("保存",-1)])),_:1,__:[19]})]),default:l(()=>[e(oe,{model:r,"label-width":"120px"},{default:l(()=>[e(T,{label:"啟用聲音提醒"},{default:l(()=>[e($,{modelValue:r.enableSound,"onUpdate:modelValue":a[4]||(a[4]=s=>r.enableSound=s)},null,8,["modelValue"])]),_:1}),e(T,{label:"啟用彈窗提醒"},{default:l(()=>[e($,{modelValue:r.enablePopup,"onUpdate:modelValue":a[5]||(a[5]=s=>r.enablePopup=s)},null,8,["modelValue"])]),_:1}),e(T,{label:"自動刷新間隔"},{default:l(()=>[e(le,{modelValue:r.autoRefreshInterval,"onUpdate:modelValue":a[6]||(a[6]=s=>r.autoRefreshInterval=s)},{default:l(()=>[e(I,{label:"5秒",value:5e3}),e(I,{label:"10秒",value:1e4}),e(I,{label:"30秒",value:3e4}),e(I,{label:"1分鐘",value:6e4}),e(I,{label:"關閉",value:0})]),_:1},8,["modelValue"])]),_:1}),e(T,{label:"最大顯示數量"},{default:l(()=>[e(se,{modelValue:r.maxDisplayCount,"onUpdate:modelValue":a[7]||(a[7]=s=>r.maxDisplayCount=s),min:10,max:1e3,step:10},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const ta=ke(Je,[["__scopeId","data-v-1a46b67d"]]);export{ta as default};
