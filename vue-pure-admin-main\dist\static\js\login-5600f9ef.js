import{d as x,a as S,r as w,G as E,o as k,aK as T,p as f,c as L,e as P,f as n,g as s,w as c,h as g,m as V,t as R,aL as D,aH as N,E as h,_ as M}from"./index-329ed960.js";import{u as U,k as F}from"./index-0d74a956.js";import{u as $}from"./dataService-42a3bdf6.js";const z={class:"login-container"},A={class:"login-box"},B=x({__name:"login",setup(K){const d=S();$();const m=w(),i=w(!1),t=E({account:"<EMAIL>",password:"111111"}),v={account:[{required:!0,message:"請輸入帳號",trigger:"blur"},{min:3,max:50,message:"帳號長度應為 3-50 個字符",trigger:"blur"}],password:[{required:!0,message:"請輸入密碼",trigger:"blur"},{min:1,max:100,message:"密碼長度應為 1-100 個字符",trigger:"blur"}]},C=()=>{const o=localStorage.getItem("PLC_CUSTOMER_ID");if(o)return o;const e={}.VITE_CUSTOMER_ID;return e||"fdff1878-a54a-44ee-b82c-a62bdc5cdb55"},_=async()=>{if(m.value)try{await m.value.validate(),i.value=!0;const o=C(),e=new URLSearchParams;e.append("Account",t.account),e.append("Password",t.password),e.append("IdName",o);const a=await fetch("http://192.168.1.152:8345/api/Staff/StaffLogin",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:e});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const r=await a.json();if(!r.Detail){const u=r.Message||"登入失敗，請檢查登入資料";throw console.error("登入失敗:",u),new Error(u)}const{AccessToken:p,RefreshToken:l,StaffName:b,PermissionCode:O,RoleId:q,UniformNumber:H,CustomerID:I,CustomerName:j,EnableState:G}=r.Detail;localStorage.setItem("access_token",p),localStorage.setItem("refresh_token",l),localStorage.setItem("brand_id",o),localStorage.setItem("customer_id",I),h.success(`登入成功！歡迎 ${b}`);try{await d.push("/plc-tags/tag")}catch(u){console.error("❌ 路由跳轉失敗:",u);try{await d.push("/")}catch(y){console.error("❌ 跳轉到首頁也失敗:",y),window.location.href="/plc-tags/tag"}}}catch(o){console.error("❌ 登入失敗:",o),h.error(`登入失敗: ${o.message}`)}finally{i.value=!1}};return k(()=>{T()&&d.push("/")}),(o,e)=>{const a=f("el-input"),r=f("el-form-item"),p=f("el-button");return L(),P("div",z,[n("div",A,[e[2]||(e[2]=n("div",{class:"login-header"},[n("h2",null,"PLC 系統登入"),n("p",null,"請輸入您的帳號和密碼")],-1)),s(g(N),{ref_key:"loginFormRef",ref:m,model:t,rules:v,class:"login-form",onKeyup:D(_,["enter"])},{default:c(()=>[s(r,{prop:"account"},{default:c(()=>[s(a,{modelValue:t.account,"onUpdate:modelValue":e[0]||(e[0]=l=>t.account=l),placeholder:"請輸入帳號",size:"large",clearable:"","prefix-icon":g(U)},null,8,["modelValue","prefix-icon"])]),_:1}),s(r,{prop:"password"},{default:c(()=>[s(a,{modelValue:t.password,"onUpdate:modelValue":e[1]||(e[1]=l=>t.password=l),type:"password",placeholder:"請輸入密碼",size:"large",clearable:"","show-password":"","prefix-icon":g(F)},null,8,["modelValue","prefix-icon"])]),_:1}),s(r,null,{default:c(()=>[s(p,{type:"primary",size:"large",loading:i.value,class:"login-button",onClick:_},{default:c(()=>[V(R(i.value?"登入中...":"登入"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),e[3]||(e[3]=n("div",{class:"login-footer"},[n("p",null,"PLC 控制系統 v1.0")],-1))])])}}});const X=M(B,[["__scopeId","data-v-10055e60"]]);export{X as default};
