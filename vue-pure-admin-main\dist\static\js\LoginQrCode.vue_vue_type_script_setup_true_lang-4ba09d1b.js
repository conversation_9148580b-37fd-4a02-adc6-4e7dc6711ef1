import{M as ot}from"./motion-7f60b73b.js";import{p as G}from"./propTypes-656ac4a0.js";import{d as Mt,r as Bt,D as bt,R as Gt,g as M,l as qt,q as Wt,h as N,p as ct,X as Rt,a3 as Xt,ac as Zt,bc as Tt,aw as xt,a9 as te,c as ee,e as ne,w as K,f as oe,t as At,b as re,m as ie}from"./index-329ed960.js";import{R as se}from"./refresh-right-c5d36303.js";var j={},ae=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},Pt={},P={};let ht;const le=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];P.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return e*4+17};P.getSymbolTotalCodewords=function(e){return le[e]};P.getBCHDigit=function(t){let e=0;for(;t!==0;)e++,t>>>=1;return e};P.setToSJISFunction=function(e){if(typeof e!="function")throw new Error('"toSJISFunc" is not a valid function.');ht=e};P.isKanjiModeEnabled=function(){return typeof ht<"u"};P.toSJIS=function(e){return ht(e)};var x={};(function(t){t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2};function e(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw new Error("Unknown EC Level: "+i)}}t.isValid=function(o){return o&&typeof o.bit<"u"&&o.bit>=0&&o.bit<4},t.from=function(o,n){if(t.isValid(o))return o;try{return e(o)}catch{return n}}})(x);function Lt(){this.buffer=[],this.length=0}Lt.prototype={get:function(t){const e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)===1},put:function(t,e){for(let i=0;i<e;i++)this.putBit((t>>>e-i-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var ce=Lt;function $(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}$.prototype.set=function(t,e,i,o){const n=t*this.size+e;this.data[n]=i,o&&(this.reservedBit[n]=!0)};$.prototype.get=function(t,e){return this.data[t*this.size+e]};$.prototype.xor=function(t,e,i){this.data[t*this.size+e]^=i};$.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var ue=$,_t={};(function(t){const e=P.getSymbolSize;t.getRowColCoords=function(o){if(o===1)return[];const n=Math.floor(o/7)+2,r=e(o),s=r===145?26:Math.ceil((r-13)/(2*n-2))*2,l=[r-7];for(let a=1;a<n-1;a++)l[a]=l[a-1]-s;return l.push(6),l.reverse()},t.getPositions=function(o){const n=[],r=t.getRowColCoords(o),s=r.length;for(let l=0;l<s;l++)for(let a=0;a<s;a++)l===0&&a===0||l===0&&a===s-1||l===s-1&&a===0||n.push([r[l],r[a]]);return n}})(_t);var vt={};const fe=P.getSymbolSize,It=7;vt.getPositions=function(e){const i=fe(e);return[[0,0],[i-It,0],[0,i-It]]};var Dt={};(function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e={N1:3,N2:3,N3:40,N4:10};t.isValid=function(n){return n!=null&&n!==""&&!isNaN(n)&&n>=0&&n<=7},t.from=function(n){return t.isValid(n)?parseInt(n,10):void 0},t.getPenaltyN1=function(n){const r=n.size;let s=0,l=0,a=0,f=null,d=null;for(let B=0;B<r;B++){l=a=0,f=d=null;for(let C=0;C<r;C++){let m=n.get(B,C);m===f?l++:(l>=5&&(s+=e.N1+(l-5)),f=m,l=1),m=n.get(C,B),m===d?a++:(a>=5&&(s+=e.N1+(a-5)),d=m,a=1)}l>=5&&(s+=e.N1+(l-5)),a>=5&&(s+=e.N1+(a-5))}return s},t.getPenaltyN2=function(n){const r=n.size;let s=0;for(let l=0;l<r-1;l++)for(let a=0;a<r-1;a++){const f=n.get(l,a)+n.get(l,a+1)+n.get(l+1,a)+n.get(l+1,a+1);(f===4||f===0)&&s++}return s*e.N2},t.getPenaltyN3=function(n){const r=n.size;let s=0,l=0,a=0;for(let f=0;f<r;f++){l=a=0;for(let d=0;d<r;d++)l=l<<1&2047|n.get(f,d),d>=10&&(l===1488||l===93)&&s++,a=a<<1&2047|n.get(d,f),d>=10&&(a===1488||a===93)&&s++}return s*e.N3},t.getPenaltyN4=function(n){let r=0;const s=n.data.length;for(let a=0;a<s;a++)r+=n.data[a];return Math.abs(Math.ceil(r*100/s/5)-10)*e.N4};function i(o,n,r){switch(o){case t.Patterns.PATTERN000:return(n+r)%2===0;case t.Patterns.PATTERN001:return n%2===0;case t.Patterns.PATTERN010:return r%3===0;case t.Patterns.PATTERN011:return(n+r)%3===0;case t.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2===0;case t.Patterns.PATTERN101:return n*r%2+n*r%3===0;case t.Patterns.PATTERN110:return(n*r%2+n*r%3)%2===0;case t.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2===0;default:throw new Error("bad maskPattern:"+o)}}t.applyMask=function(n,r){const s=r.size;for(let l=0;l<s;l++)for(let a=0;a<s;a++)r.isReserved(a,l)||r.xor(a,l,i(n,a,l))},t.getBestMask=function(n,r){const s=Object.keys(t.Patterns).length;let l=0,a=1/0;for(let f=0;f<s;f++){r(f),t.applyMask(f,n);const d=t.getPenaltyN1(n)+t.getPenaltyN2(n)+t.getPenaltyN3(n)+t.getPenaltyN4(n);t.applyMask(f,n),d<a&&(a=d,l=f)}return l}})(Dt);var tt={};const v=x,q=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],W=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];tt.getBlocksCount=function(e,i){switch(i){case v.L:return q[(e-1)*4+0];case v.M:return q[(e-1)*4+1];case v.Q:return q[(e-1)*4+2];case v.H:return q[(e-1)*4+3];default:return}};tt.getTotalCodewordsCount=function(e,i){switch(i){case v.L:return W[(e-1)*4+0];case v.M:return W[(e-1)*4+1];case v.Q:return W[(e-1)*4+2];case v.H:return W[(e-1)*4+3];default:return}};var Ut={},et={};const J=new Uint8Array(512),X=new Uint8Array(256);(function(){let e=1;for(let i=0;i<255;i++)J[i]=e,X[e]=i,e<<=1,e&256&&(e^=285);for(let i=255;i<512;i++)J[i]=J[i-255]})();et.log=function(e){if(e<1)throw new Error("log("+e+")");return X[e]};et.exp=function(e){return J[e]};et.mul=function(e,i){return e===0||i===0?0:J[X[e]+X[i]]};(function(t){const e=et;t.mul=function(o,n){const r=new Uint8Array(o.length+n.length-1);for(let s=0;s<o.length;s++)for(let l=0;l<n.length;l++)r[s+l]^=e.mul(o[s],n[l]);return r},t.mod=function(o,n){let r=new Uint8Array(o);for(;r.length-n.length>=0;){const s=r[0];for(let a=0;a<n.length;a++)r[a]^=e.mul(n[a],s);let l=0;for(;l<r.length&&r[l]===0;)l++;r=r.slice(l)}return r},t.generateECPolynomial=function(o){let n=new Uint8Array([1]);for(let r=0;r<o;r++)n=t.mul(n,new Uint8Array([1,e.exp(r)]));return n}})(Ut);const kt=Ut;function mt(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}mt.prototype.initialize=function(e){this.degree=e,this.genPoly=kt.generateECPolynomial(this.degree)};mt.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const i=new Uint8Array(e.length+this.degree);i.set(e);const o=kt.mod(i,this.genPoly),n=this.degree-o.length;if(n>0){const r=new Uint8Array(this.degree);return r.set(o,n),r}return o};var de=mt,Ft={},D={},wt={};wt.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40};var L={};const zt="[0-9]+",ge="[A-Z $%*+\\-./:]+";let Y="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Y=Y.replace(/u/g,"\\u");const he="(?:(?![A-Z0-9 $%*+\\-./:]|"+Y+`)(?:.|[\r
]))+`;L.KANJI=new RegExp(Y,"g");L.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");L.BYTE=new RegExp(he,"g");L.NUMERIC=new RegExp(zt,"g");L.ALPHANUMERIC=new RegExp(ge,"g");const me=new RegExp("^"+Y+"$"),we=new RegExp("^"+zt+"$"),Ce=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");L.testKanji=function(e){return me.test(e)};L.testNumeric=function(e){return we.test(e)};L.testAlphanumeric=function(e){return Ce.test(e)};(function(t){const e=wt,i=L;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(r,s){if(!r.ccBits)throw new Error("Invalid mode: "+r);if(!e.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?r.ccBits[0]:s<27?r.ccBits[1]:r.ccBits[2]},t.getBestModeForData=function(r){return i.testNumeric(r)?t.NUMERIC:i.testAlphanumeric(r)?t.ALPHANUMERIC:i.testKanji(r)?t.KANJI:t.BYTE},t.toString=function(r){if(r&&r.id)return r.id;throw new Error("Invalid mode")},t.isValid=function(r){return r&&r.bit&&r.ccBits};function o(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+n)}}t.from=function(r,s){if(t.isValid(r))return r;try{return o(r)}catch{return s}}})(D);(function(t){const e=P,i=tt,o=x,n=D,r=wt,s=7973,l=e.getBCHDigit(s);function a(C,m,y){for(let c=1;c<=40;c++)if(m<=t.getCapacity(c,y,C))return c}function f(C,m){return n.getCharCountIndicator(C,m)+4}function d(C,m){let y=0;return C.forEach(function(c){const E=f(c.mode,m);y+=E+c.getBitsLength()}),y}function B(C,m){for(let y=1;y<=40;y++)if(d(C,y)<=t.getCapacity(y,m,n.MIXED))return y}t.from=function(m,y){return r.isValid(m)?parseInt(m,10):y},t.getCapacity=function(m,y,c){if(!r.isValid(m))throw new Error("Invalid QR Code version");typeof c>"u"&&(c=n.BYTE);const E=e.getSymbolTotalCodewords(m),u=i.getTotalCodewordsCount(m,y),w=(E-u)*8;if(c===n.MIXED)return w;const h=w-f(c,m);switch(c){case n.NUMERIC:return Math.floor(h/10*3);case n.ALPHANUMERIC:return Math.floor(h/11*2);case n.KANJI:return Math.floor(h/13);case n.BYTE:default:return Math.floor(h/8)}},t.getBestVersionForData=function(m,y){let c;const E=o.from(y,o.M);if(Array.isArray(m)){if(m.length>1)return B(m,E);if(m.length===0)return 1;c=m[0]}else c=m;return a(c.mode,c.getLength(),E)},t.getEncodedBits=function(m){if(!r.isValid(m)||m<7)throw new Error("Invalid QR Code version");let y=m<<12;for(;e.getBCHDigit(y)-l>=0;)y^=s<<e.getBCHDigit(y)-l;return m<<12|y}})(Ft);var Vt={};const ut=P,Ht=1335,ye=21522,St=ut.getBCHDigit(Ht);Vt.getEncodedBits=function(e,i){const o=e.bit<<3|i;let n=o<<10;for(;ut.getBCHDigit(n)-St>=0;)n^=Ht<<ut.getBCHDigit(n)-St;return(o<<10|n)^ye};var Ot={};const Ee=D;function k(t){this.mode=Ee.NUMERIC,this.data=t.toString()}k.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)};k.prototype.getLength=function(){return this.data.length};k.prototype.getBitsLength=function(){return k.getBitsLength(this.data.length)};k.prototype.write=function(e){let i,o,n;for(i=0;i+3<=this.data.length;i+=3)o=this.data.substr(i,3),n=parseInt(o,10),e.put(n,10);const r=this.data.length-i;r>0&&(o=this.data.substr(i),n=parseInt(o,10),e.put(n,r*3+1))};var pe=k;const Be=D,rt=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function F(t){this.mode=Be.ALPHANUMERIC,this.data=t}F.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)};F.prototype.getLength=function(){return this.data.length};F.prototype.getBitsLength=function(){return F.getBitsLength(this.data.length)};F.prototype.write=function(e){let i;for(i=0;i+2<=this.data.length;i+=2){let o=rt.indexOf(this.data[i])*45;o+=rt.indexOf(this.data[i+1]),e.put(o,11)}this.data.length%2&&e.put(rt.indexOf(this.data[i]),6)};var be=F;const Te=D;function z(t){this.mode=Te.BYTE,typeof t=="string"?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}z.getBitsLength=function(e){return e*8};z.prototype.getLength=function(){return this.data.length};z.prototype.getBitsLength=function(){return z.getBitsLength(this.data.length)};z.prototype.write=function(t){for(let e=0,i=this.data.length;e<i;e++)t.put(this.data[e],8)};var Ae=z;const Ie=D,Se=P;function V(t){this.mode=Ie.KANJI,this.data=t}V.getBitsLength=function(e){return e*13};V.prototype.getLength=function(){return this.data.length};V.prototype.getBitsLength=function(){return V.getBitsLength(this.data.length)};V.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let i=Se.toSJIS(this.data[e]);if(i>=33088&&i<=40956)i-=33088;else if(i>=57408&&i<=60351)i-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);i=(i>>>8&255)*192+(i&255),t.put(i,13)}};var Ne=V,Kt={exports:{}};(function(t){var e={single_source_shortest_paths:function(i,o,n){var r={},s={};s[o]=0;var l=e.PriorityQueue.make();l.push(o,0);for(var a,f,d,B,C,m,y,c,E;!l.empty();){a=l.pop(),f=a.value,B=a.cost,C=i[f]||{};for(d in C)C.hasOwnProperty(d)&&(m=C[d],y=B+m,c=s[d],E=typeof s[d]>"u",(E||c>y)&&(s[d]=y,l.push(d,y),r[d]=f))}if(typeof n<"u"&&typeof s[n]>"u"){var u=["Could not find a path from ",o," to ",n,"."].join("");throw new Error(u)}return r},extract_shortest_path_from_predecessor_list:function(i,o){for(var n=[],r=o;r;)n.push(r),i[r],r=i[r];return n.reverse(),n},find_path:function(i,o,n){var r=e.single_source_shortest_paths(i,o,n);return e.extract_shortest_path_from_predecessor_list(r,n)},PriorityQueue:{make:function(i){var o=e.PriorityQueue,n={},r;i=i||{};for(r in o)o.hasOwnProperty(r)&&(n[r]=o[r]);return n.queue=[],n.sorter=i.sorter||o.default_sorter,n},default_sorter:function(i,o){return i.cost-o.cost},push:function(i,o){var n={value:i,cost:o};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};t.exports=e})(Kt);var Me=Kt.exports;(function(t){const e=D,i=pe,o=be,n=Ae,r=Ne,s=L,l=P,a=Me;function f(u){return unescape(encodeURIComponent(u)).length}function d(u,w,h){const g=[];let p;for(;(p=u.exec(h))!==null;)g.push({data:p[0],index:p.index,mode:w,length:p[0].length});return g}function B(u){const w=d(s.NUMERIC,e.NUMERIC,u),h=d(s.ALPHANUMERIC,e.ALPHANUMERIC,u);let g,p;return l.isKanjiModeEnabled()?(g=d(s.BYTE,e.BYTE,u),p=d(s.KANJI,e.KANJI,u)):(g=d(s.BYTE_KANJI,e.BYTE,u),p=[]),w.concat(h,g,p).sort(function(T,R){return T.index-R.index}).map(function(T){return{data:T.data,mode:T.mode,length:T.length}})}function C(u,w){switch(w){case e.NUMERIC:return i.getBitsLength(u);case e.ALPHANUMERIC:return o.getBitsLength(u);case e.KANJI:return r.getBitsLength(u);case e.BYTE:return n.getBitsLength(u)}}function m(u){return u.reduce(function(w,h){const g=w.length-1>=0?w[w.length-1]:null;return g&&g.mode===h.mode?(w[w.length-1].data+=h.data,w):(w.push(h),w)},[])}function y(u){const w=[];for(let h=0;h<u.length;h++){const g=u[h];switch(g.mode){case e.NUMERIC:w.push([g,{data:g.data,mode:e.ALPHANUMERIC,length:g.length},{data:g.data,mode:e.BYTE,length:g.length}]);break;case e.ALPHANUMERIC:w.push([g,{data:g.data,mode:e.BYTE,length:g.length}]);break;case e.KANJI:w.push([g,{data:g.data,mode:e.BYTE,length:f(g.data)}]);break;case e.BYTE:w.push([{data:g.data,mode:e.BYTE,length:f(g.data)}])}}return w}function c(u,w){const h={},g={start:{}};let p=["start"];for(let b=0;b<u.length;b++){const T=u[b],R=[];for(let S=0;S<T.length;S++){const I=T[S],_=""+b+S;R.push(_),h[_]={node:I,lastCount:0},g[_]={};for(let U=0;U<p.length;U++){const A=p[U];h[A]&&h[A].node.mode===I.mode?(g[A][_]=C(h[A].lastCount+I.length,I.mode)-C(h[A].lastCount,I.mode),h[A].lastCount+=I.length):(h[A]&&(h[A].lastCount=I.length),g[A][_]=C(I.length,I.mode)+4+e.getCharCountIndicator(I.mode,w))}}p=R}for(let b=0;b<p.length;b++)g[p[b]].end=0;return{map:g,table:h}}function E(u,w){let h;const g=e.getBestModeForData(u);if(h=e.from(w,g),h!==e.BYTE&&h.bit<g.bit)throw new Error('"'+u+'" cannot be encoded with mode '+e.toString(h)+`.
 Suggested mode is: `+e.toString(g));switch(h===e.KANJI&&!l.isKanjiModeEnabled()&&(h=e.BYTE),h){case e.NUMERIC:return new i(u);case e.ALPHANUMERIC:return new o(u);case e.KANJI:return new r(u);case e.BYTE:return new n(u)}}t.fromArray=function(w){return w.reduce(function(h,g){return typeof g=="string"?h.push(E(g,null)):g.data&&h.push(E(g.data,g.mode)),h},[])},t.fromString=function(w,h){const g=B(w,l.isKanjiModeEnabled()),p=y(g),b=c(p,h),T=a.find_path(b.map,"start","end"),R=[];for(let S=1;S<T.length-1;S++)R.push(b.table[T[S]].node);return t.fromArray(m(R))},t.rawSplit=function(w){return t.fromArray(B(w,l.isKanjiModeEnabled()))}})(Ot);const nt=P,it=x,Re=ce,Pe=ue,Le=_t,_e=vt,ft=Dt,dt=tt,ve=de,Z=Ft,De=Vt,Ue=D,st=Ot;function ke(t,e){const i=t.size,o=_e.getPositions(e);for(let n=0;n<o.length;n++){const r=o[n][0],s=o[n][1];for(let l=-1;l<=7;l++)if(!(r+l<=-1||i<=r+l))for(let a=-1;a<=7;a++)s+a<=-1||i<=s+a||(l>=0&&l<=6&&(a===0||a===6)||a>=0&&a<=6&&(l===0||l===6)||l>=2&&l<=4&&a>=2&&a<=4?t.set(r+l,s+a,!0,!0):t.set(r+l,s+a,!1,!0))}}function Fe(t){const e=t.size;for(let i=8;i<e-8;i++){const o=i%2===0;t.set(i,6,o,!0),t.set(6,i,o,!0)}}function ze(t,e){const i=Le.getPositions(e);for(let o=0;o<i.length;o++){const n=i[o][0],r=i[o][1];for(let s=-2;s<=2;s++)for(let l=-2;l<=2;l++)s===-2||s===2||l===-2||l===2||s===0&&l===0?t.set(n+s,r+l,!0,!0):t.set(n+s,r+l,!1,!0)}}function Ve(t,e){const i=t.size,o=Z.getEncodedBits(e);let n,r,s;for(let l=0;l<18;l++)n=Math.floor(l/3),r=l%3+i-8-3,s=(o>>l&1)===1,t.set(n,r,s,!0),t.set(r,n,s,!0)}function at(t,e,i){const o=t.size,n=De.getEncodedBits(e,i);let r,s;for(r=0;r<15;r++)s=(n>>r&1)===1,r<6?t.set(r,8,s,!0):r<8?t.set(r+1,8,s,!0):t.set(o-15+r,8,s,!0),r<8?t.set(8,o-r-1,s,!0):r<9?t.set(8,15-r-1+1,s,!0):t.set(8,15-r-1,s,!0);t.set(o-8,8,1,!0)}function He(t,e){const i=t.size;let o=-1,n=i-1,r=7,s=0;for(let l=i-1;l>0;l-=2)for(l===6&&l--;;){for(let a=0;a<2;a++)if(!t.isReserved(n,l-a)){let f=!1;s<e.length&&(f=(e[s]>>>r&1)===1),t.set(n,l-a,f),r--,r===-1&&(s++,r=7)}if(n+=o,n<0||i<=n){n-=o,o=-o;break}}}function Oe(t,e,i){const o=new Re;i.forEach(function(a){o.put(a.mode.bit,4),o.put(a.getLength(),Ue.getCharCountIndicator(a.mode,t)),a.write(o)});const n=nt.getSymbolTotalCodewords(t),r=dt.getTotalCodewordsCount(t,e),s=(n-r)*8;for(o.getLengthInBits()+4<=s&&o.put(0,4);o.getLengthInBits()%8!==0;)o.putBit(0);const l=(s-o.getLengthInBits())/8;for(let a=0;a<l;a++)o.put(a%2?17:236,8);return Ke(o,t,e)}function Ke(t,e,i){const o=nt.getSymbolTotalCodewords(e),n=dt.getTotalCodewordsCount(e,i),r=o-n,s=dt.getBlocksCount(e,i),l=o%s,a=s-l,f=Math.floor(o/s),d=Math.floor(r/s),B=d+1,C=f-d,m=new ve(C);let y=0;const c=new Array(s),E=new Array(s);let u=0;const w=new Uint8Array(t.buffer);for(let T=0;T<s;T++){const R=T<a?d:B;c[T]=w.slice(y,y+R),E[T]=m.encode(c[T]),y+=R,u=Math.max(u,R)}const h=new Uint8Array(o);let g=0,p,b;for(p=0;p<u;p++)for(b=0;b<s;b++)p<c[b].length&&(h[g++]=c[b][p]);for(p=0;p<C;p++)for(b=0;b<s;b++)h[g++]=E[b][p];return h}function Je(t,e,i,o){let n;if(Array.isArray(t))n=st.fromArray(t);else if(typeof t=="string"){let f=e;if(!f){const d=st.rawSplit(t);f=Z.getBestVersionForData(d,i)}n=st.fromString(t,f||40)}else throw new Error("Invalid data");const r=Z.getBestVersionForData(n,i);if(!r)throw new Error("The amount of data is too big to be stored in a QR Code");if(!e)e=r;else if(e<r)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+r+`.
`);const s=Oe(e,i,n),l=nt.getSymbolSize(e),a=new Pe(l);return ke(a,e),Fe(a),ze(a,e),at(a,i,0),e>=7&&Ve(a,e),He(a,s),isNaN(o)&&(o=ft.getBestMask(a,at.bind(null,a,i))),ft.applyMask(o,a),at(a,i,o),{modules:a,version:e,errorCorrectionLevel:i,maskPattern:o,segments:n}}Pt.create=function(e,i){if(typeof e>"u"||e==="")throw new Error("No input text");let o=it.M,n,r;return typeof i<"u"&&(o=it.from(i.errorCorrectionLevel,it.M),n=Z.from(i.version),r=ft.from(i.maskPattern),i.toSJISFunc&&nt.setToSJISFunction(i.toSJISFunc)),Je(e,n,o,r)};var Jt={},Ct={};(function(t){function e(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let o=i.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+i);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(r){return[r,r]}))),o.length===6&&o.push("F","F");const n=parseInt(o.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:n&255,hex:"#"+o.slice(0,6).join("")}}t.getOptions=function(o){o||(o={}),o.color||(o.color={});const n=typeof o.margin>"u"||o.margin===null||o.margin<0?4:o.margin,r=o.width&&o.width>=21?o.width:void 0,s=o.scale||4;return{width:r,scale:r?4:s,margin:n,color:{dark:e(o.color.dark||"#000000ff"),light:e(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},t.getScale=function(o,n){return n.width&&n.width>=o+n.margin*2?n.width/(o+n.margin*2):n.scale},t.getImageWidth=function(o,n){const r=t.getScale(o,n);return Math.floor((o+n.margin*2)*r)},t.qrToImageData=function(o,n,r){const s=n.modules.size,l=n.modules.data,a=t.getScale(s,r),f=Math.floor((s+r.margin*2)*a),d=r.margin*a,B=[r.color.light,r.color.dark];for(let C=0;C<f;C++)for(let m=0;m<f;m++){let y=(C*f+m)*4,c=r.color.light;if(C>=d&&m>=d&&C<f-d&&m<f-d){const E=Math.floor((C-d)/a),u=Math.floor((m-d)/a);c=B[l[E*s+u]?1:0]}o[y++]=c.r,o[y++]=c.g,o[y++]=c.b,o[y]=c.a}}})(Ct);(function(t){const e=Ct;function i(n,r,s){n.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=s,r.width=s,r.style.height=s+"px",r.style.width=s+"px"}function o(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}t.render=function(r,s,l){let a=l,f=s;typeof a>"u"&&(!s||!s.getContext)&&(a=s,s=void 0),s||(f=o()),a=e.getOptions(a);const d=e.getImageWidth(r.modules.size,a),B=f.getContext("2d"),C=B.createImageData(d,d);return e.qrToImageData(C.data,r,a),i(B,f,d),B.putImageData(C,0,0),f},t.renderToDataURL=function(r,s,l){let a=l;typeof a>"u"&&(!s||!s.getContext)&&(a=s,s=void 0),a||(a={});const f=t.render(r,s,a),d=a.type||"image/png",B=a.rendererOpts||{};return f.toDataURL(d,B.quality)}})(Jt);var Yt={};const Ye=Ct;function Nt(t,e){const i=t.a/255,o=e+'="'+t.hex+'"';return i<1?o+" "+e+'-opacity="'+i.toFixed(2).slice(1)+'"':o}function lt(t,e,i){let o=t+e;return typeof i<"u"&&(o+=" "+i),o}function je(t,e,i){let o="",n=0,r=!1,s=0;for(let l=0;l<t.length;l++){const a=Math.floor(l%e),f=Math.floor(l/e);!a&&!r&&(r=!0),t[l]?(s++,l>0&&a>0&&t[l-1]||(o+=r?lt("M",a+i,.5+f+i):lt("m",n,0),n=0,r=!1),a+1<e&&t[l+1]||(o+=lt("h",s),s=0)):n++}return o}Yt.render=function(e,i,o){const n=Ye.getOptions(i),r=e.modules.size,s=e.modules.data,l=r+n.margin*2,a=n.color.light.a?"<path "+Nt(n.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",f="<path "+Nt(n.color.dark,"stroke")+' d="'+je(s,r,n.margin)+'"/>',d='viewBox="0 0 '+l+" "+l+'"',C='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+d+' shape-rendering="crispEdges">'+a+f+`</svg>
`;return typeof o=="function"&&o(null,C),C};const $e=ae,gt=Pt,jt=Jt,Qe=Yt;function yt(t,e,i,o,n){const r=[].slice.call(arguments,1),s=r.length,l=typeof r[s-1]=="function";if(!l&&!$e())throw new Error("Callback required as last argument");if(l){if(s<2)throw new Error("Too few arguments provided");s===2?(n=i,i=e,e=o=void 0):s===3&&(e.getContext&&typeof n>"u"?(n=o,o=void 0):(n=o,o=i,i=e,e=void 0))}else{if(s<1)throw new Error("Too few arguments provided");return s===1?(i=e,e=o=void 0):s===2&&!e.getContext&&(o=i,i=e,e=void 0),new Promise(function(a,f){try{const d=gt.create(i,o);a(t(d,e,o))}catch(d){f(d)}})}try{const a=gt.create(i,o);n(null,t(a,e,o))}catch(a){n(a)}}j.create=gt.create;j.toCanvas=yt.bind(null,jt.render);j.toDataURL=yt.bind(null,jt.renderToDataURL);j.toString=yt.bind(null,function(t,e,i){return Qe.render(t,i)});const Ge={tag:G.string.validate(t=>["canvas","img"].includes(t)).def("canvas"),text:{type:[String,Array],default:null},options:{type:Object,default:()=>({})},width:G.number.def(200),logo:{type:[String,Object],default:()=>""},disabled:G.bool.def(!1),disabledText:G.string.def("")},qe=Mt({name:"ReQrcode",props:Ge,emits:["done","click","disabled-click"],setup(t,{emit:e}){const{toCanvas:i,toDataURL:o}=j,n=Bt(!0),r=Bt(null),s=bt(()=>String(t.text)),l=bt(()=>({width:t.width+"px",height:t.width+"px"})),a=async()=>{await Xt();const c=Zt(t.options||{});if(t.tag==="canvas"){c.errorCorrectionLevel=c.errorCorrectionLevel||B(N(s));const E=await d(N(s),c);c.scale=t.width===0?void 0:t.width/E*4;const u=await i(N(r),N(s),c);if(t.logo){const w=await f(u);e("done",w),n.value=!1}else e("done",u.toDataURL()),n.value=!1}else{const E=await o(s.value,{errorCorrectionLevel:"H",width:t.width,...c});N(r).src=E,e("done",E),n.value=!1}};Gt(()=>s.value,c=>{c&&a()},{deep:!0,immediate:!0});const f=c=>{const E=c.width,u=Object.assign({logoSize:.15,bgColor:"#ffffff",borderSize:.05,crossOrigin:"anonymous",borderRadius:8,logoRadius:0},Tt(t.logo)?{}:t.logo),{logoSize:w=.15,bgColor:h="#ffffff",borderSize:g=.05,crossOrigin:p="anonymous",borderRadius:b=8,logoRadius:T=0}=u,R=Tt(t.logo)?t.logo:t.logo.src,S=E*w,I=E*(1-w)/2,_=E*(w+g),U=E*(1-w-g)/2,A=c.getContext("2d");if(!A)return;C(A)(U,U,_,_,b),A.fillStyle=h,A.fill();const H=new Image;(p||T)&&H.setAttribute("crossOrigin",p),H.src=R;const $t=O=>{A.drawImage(O,I,I,S,S)},Qt=O=>{const Q=document.createElement("canvas");Q.width=I+S,Q.height=I+S;const Et=Q.getContext("2d");if(!Et||!A||(Et.drawImage(O,I,I,S,S),C(A)(I,I,S,S,T),!A))return;const pt=A.createPattern(Q,"no-repeat");pt&&(A.fillStyle=pt,A.fill())};return new Promise(O=>{H.onload=()=>{T?Qt(H):$t(H),O(c.toDataURL())}})},d=async(c,E)=>{const u=document.createElement("canvas");return await i(u,c,E),u.width},B=c=>c.length>36?"M":c.length>16?"Q":"H",C=c=>(E,u,w,h,g)=>{const p=Math.min(w,h);return g>p/2&&(g=p/2),c.beginPath(),c.moveTo(E+g,u),c.arcTo(E+w,u,E+w,u+h,g),c.arcTo(E+w,u+h,E,u+h,g),c.arcTo(E,u+h,E,u,g),c.arcTo(E,u,E+w,u,g),c.closePath(),c},m=()=>{e("click")},y=()=>{e("disabled-click")};return()=>M(Rt,null,[qt(M("div",{class:"qrcode relative inline-block",style:N(l)},[t.tag==="canvas"?M("canvas",{ref:r,onClick:m},null):M("img",{ref:r,onClick:m},null),t.disabled&&M("div",{class:"qrcode--disabled absolute top-0 left-0 flex w-full h-full items-center justify-center",onClick:y},[M("div",{class:"absolute top-[50%] left-[50%] font-bold"},[M(ct("iconify-icon-offline"),{class:"cursor-pointer",icon:se,width:"30",color:"var(--el-color-primary)"},null),M("div",null,[t.disabledText])])])]),[[Wt("loading"),N(n)]])])}}),We=xt(qe),Xe={class:"text-gray-500 text-xs"},nn=Mt({__name:"LoginQrCode",setup(t){const{t:e}=te();return(i,o)=>{const n=ct("el-divider"),r=ct("el-button");return ee(),ne(Rt,null,[M(N(ot),{class:"-mt-2 -mb-2"},{default:K(()=>[M(N(We),{text:N(e)("login.pureTest")},null,8,["text"])]),_:1}),M(N(ot),{delay:100},{default:K(()=>[M(n,null,{default:K(()=>[oe("p",Xe,At(N(e)("login.pureTip")),1)]),_:1})]),_:1}),M(N(ot),{delay:150},{default:K(()=>[M(r,{class:"w-full mt-4!",onClick:o[0]||(o[0]=s=>N(re)().SET_CURRENTPAGE(0))},{default:K(()=>[ie(At(N(e)("login.pureBack")),1)]),_:1})]),_:1})],64)}}});export{nn as _};
