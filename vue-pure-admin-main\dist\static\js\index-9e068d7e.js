import{d as Ce,a as we,r as b,G as I,o as Te,p as m,c,e as D,f as a,g as e,w as t,h as y,t as n,m as d,X as U,Y as M,n as L,j as Q,E as A,_ as De}from"./index-329ed960.js";import{g as ee,m as le,B as te,C as ae,D as xe,v as Ie,x as Ae,e as ke,E as Ue}from"./index-0d74a956.js";import{u as Le}from"./dataService-42a3bdf6.js";const Ge={class:"tags-container"},Se={class:"stats-content"},Ne={class:"stats-icon channel-icon"},he={class:"stats-info"},Re={class:"stats-number"},Ee={class:"stats-content"},$e={class:"stats-icon device-icon"},Be={class:"stats-info"},Me={class:"stats-number"},Pe={class:"stats-content"},Oe={class:"stats-icon tag-icon"},qe={class:"stats-info"},Fe={class:"stats-number"},He={class:"stats-content"},We={class:"stats-icon group-icon"},Qe={class:"stats-info"},Xe={class:"stats-number"},je={class:"modules-section"},Ye={class:"module-content"},ze={class:"module-icon channel-bg"},Je={class:"module-info"},Ke={class:"module-stats"},Ze={class:"module-content"},el={class:"module-icon device-bg"},ll={class:"module-info"},tl={class:"module-stats"},al={class:"module-content"},sl={class:"module-icon tag-bg"},ol={class:"module-info"},nl={class:"module-stats"},ul={class:"module-content"},dl={class:"module-icon group-bg"},il={class:"module-info"},rl={class:"module-stats"},pl={class:"module-content"},ml={class:"module-icon region-bg"},fl={class:"module-info"},vl={class:"module-stats"},_l={class:"quick-actions"},gl={class:"action-content"},cl={class:"action-content"},Vl={class:"action-content"},bl={class:"system-status"},yl={class:"status-list"},Cl={class:"status-item"},wl={class:"status-item"},Tl={class:"status-item"},Dl={class:"status-list"},xl={class:"status-item"},Il={class:"status-item"},Al={class:"status-item"},kl={key:0},Ul={class:"dialog-footer"},Ll={key:0,class:"real-time-value"},Gl={class:"value-display"},Sl={class:"tag-name"},Nl={class:"current-value"},hl={class:"value"},Rl={class:"unit"},El={class:"quality"},$l={class:"timestamp"},Bl={key:0,class:"write-value"},Ml={key:1,class:"loading-container"},Pl={class:"dialog-footer"},Ol=Ce({__name:"index",setup(ql){const P=we();Le();const G=b(!1),O=b(!1),se=b([]),X=I({overwriteExisting:!1}),S=b(!1),j=b("create"),q=b(null),o=I({TagId:"",TagName:"",TagType:"",DataType:"",Address:"",DeviceId:"",GroupId:"",TagDescription:"",Unit:"",ScanRate:1e3,DefaultValue:0,MinValue:0,MaxValue:100,IsActive:!0,IsReadOnly:!1,AlarmEnabled:!1,HighAlarmLimit:0,LowAlarmLimit:0,HighWarningLimit:0,LowWarningLimit:0}),oe=I({TagName:[{required:!0,message:"請輸入標籤名稱",trigger:"blur"}],TagType:[{required:!0,message:"請選擇標籤類型",trigger:"change"}],DataType:[{required:!0,message:"請選擇資料類型",trigger:"change"}],Address:[{required:!0,message:"請輸入位址",trigger:"blur"}],DeviceId:[{required:!0,message:"請選擇設備",trigger:"change"}],GroupId:[{required:!0,message:"請選擇群組",trigger:"change"}]}),ne=I([{label:"數位",value:"Digital"},{label:"類比",value:"Analog"},{label:"字串",value:"String"},{label:"布林",value:"Boolean"}]),ue=I([{label:"Int16",value:"Int16"},{label:"Int32",value:"Int32"},{label:"Float",value:"Float"},{label:"Double",value:"Double"},{label:"String",value:"String"},{label:"Boolean",value:"Boolean"}]),de=I([{DeviceId:"Device1",DeviceName:"PLC 1"},{DeviceId:"Device2",DeviceName:"PLC 2"},{DeviceId:"Device3",DeviceName:"PLC 3"}]),ie=I([{GroupId:"Group1",GroupName:"Group A"},{GroupId:"Group2",GroupName:"Group B"},{GroupId:"Group3",GroupName:"Group C"}]),F=b(!1),Y=b(!1),p=b(null),z=b(!1),x=b(null),N=b(null),H=b(!1),f=I({channelCount:0,activeChannelCount:0,deviceCount:0,activeDeviceCount:0,tagCount:0,alarmTagCount:0,groupCount:0,groupTagCount:0,regionCount:0,highTempAlarms:0,lowTempAlarms:0,offlineDevices:0}),k=r=>{P.push(`/plc-tags/${r}`)},re=()=>{P.push("/plc/tags/tag")},pe=()=>{P.push("/plc/database/realtime")},me=()=>{A.info("匯出功能開發中...")},J=async()=>{try{Object.assign(f,{channelCount:8,activeChannelCount:6,deviceCount:25,activeDeviceCount:22,tagCount:156,alarmTagCount:12,groupCount:18,groupTagCount:145,regionCount:12,highTempAlarms:2,lowTempAlarms:1,offlineDevices:3})}catch(r){console.error("載入系統統計失敗:",r),A.error(r.message||"載入系統統計失敗")}},fe=r=>{},ve=async()=>{try{O.value=!0,await new Promise(r=>setTimeout(r,2e3)),A.success("匯入成功"),G.value=!1}catch(r){A.error(r.message||"匯入失敗")}finally{O.value=!1}},_e=async()=>{q.value&&await q.value.validate(async r=>{if(r){F.value=!0;try{await new Promise(l=>setTimeout(l,1e3)),A.success("標籤保存成功"),S.value=!1,await J()}catch(l){A.error(l.message||"標籤保存失敗")}finally{F.value=!1}}})},ge=async()=>{if(!(!p.value||!N.value)){H.value=!0;try{await new Promise(r=>setTimeout(r,1e3)),A.success("寫入成功"),x.value.Value=N.value}catch(r){A.error(r.message||"寫入失敗")}finally{H.value=!1}}},K=r=>{if(!r)return"";const l=new Date(r),_=l.getFullYear(),v=String(l.getMonth()+1).padStart(2,"0"),u=String(l.getDate()).padStart(2,"0"),g=String(l.getHours()).padStart(2,"0"),C=String(l.getMinutes()).padStart(2,"0");return`${_}-${v}-${u} ${g}:${C}`},ce=r=>{switch(r){case"Good":return"success";case"Bad":return"danger";case"Uncertain":return"warning";default:return"info"}};return Te(async()=>{await J()}),(r,l)=>{const _=m("el-icon"),v=m("el-card"),u=m("el-col"),g=m("el-row"),C=m("el-button"),w=m("el-tag"),h=m("el-input"),i=m("el-form-item"),R=m("el-option"),E=m("el-select"),T=m("el-input-number"),$=m("el-switch"),Z=m("el-divider"),W=m("el-form"),B=m("el-dialog"),V=m("el-descriptions-item"),Ve=m("el-descriptions"),be=m("el-skeleton"),ye=m("el-upload");return c(),D(U,null,[a("div",Ge,[l[70]||(l[70]=a("div",{class:"page-header"},[a("h1",null,"測點管理系統"),a("p",null,"管理 PLC 系統的測點、裝置、通道和群組，提供完整的測點組織架構")],-1)),e(g,{gutter:20,class:"stats-row"},{default:t(()=>[e(u,{span:6},{default:t(()=>[e(v,{class:"stats-card"},{default:t(()=>[a("div",Se,[a("div",Ne,[e(_,null,{default:t(()=>[e(y(ee))]),_:1})]),a("div",he,[a("div",Re,n(f.channelCount),1),l[32]||(l[32]=a("div",{class:"stats-label"},"通道數量",-1))])])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(v,{class:"stats-card"},{default:t(()=>[a("div",Ee,[a("div",$e,[e(_,null,{default:t(()=>[e(y(le))]),_:1})]),a("div",Be,[a("div",Me,n(f.deviceCount),1),l[33]||(l[33]=a("div",{class:"stats-label"},"裝置數量",-1))])])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(v,{class:"stats-card"},{default:t(()=>[a("div",Pe,[a("div",Oe,[e(_,null,{default:t(()=>[e(y(te))]),_:1})]),a("div",qe,[a("div",Fe,n(f.tagCount),1),l[34]||(l[34]=a("div",{class:"stats-label"},"測點數量",-1))])])]),_:1})]),_:1}),e(u,{span:6},{default:t(()=>[e(v,{class:"stats-card"},{default:t(()=>[a("div",He,[a("div",We,[e(_,null,{default:t(()=>[e(y(ae))]),_:1})]),a("div",Qe,[a("div",Xe,n(f.groupCount),1),l[35]||(l[35]=a("div",{class:"stats-label"},"群組數量",-1))])])]),_:1})]),_:1})]),_:1}),a("div",je,[l[47]||(l[47]=a("h2",null,"功能模組",-1)),e(g,{gutter:20},{default:t(()=>[e(u,{span:12,lg:8},{default:t(()=>[e(v,{class:"module-card",shadow:"hover",onClick:l[0]||(l[0]=s=>k("channel"))},{default:t(()=>[a("div",Ye,[a("div",ze,[e(_,null,{default:t(()=>[e(y(ee))]),_:1})]),a("div",Je,[l[36]||(l[36]=a("h3",null,"通道管理",-1)),l[37]||(l[37]=a("p",null,"管理通訊通道，支援 TCP、OBIX、Desigo CC 等多種驅動程式",-1)),a("div",Ke,[a("span",null,n(f.channelCount)+" 個通道",1),a("span",null,n(f.activeChannelCount)+" 個啟用",1)])])])]),_:1})]),_:1}),e(u,{span:12,lg:8},{default:t(()=>[e(v,{class:"module-card",shadow:"hover",onClick:l[1]||(l[1]=s=>k("device"))},{default:t(()=>[a("div",Ze,[a("div",el,[e(_,null,{default:t(()=>[e(y(le))]),_:1})]),a("div",ll,[l[38]||(l[38]=a("h3",null,"裝置管理",-1)),l[39]||(l[39]=a("p",null,"管理 PLC 裝置、感測器、執行器等設備，支援裝置分類",-1)),a("div",tl,[a("span",null,n(f.deviceCount)+" 個裝置",1),a("span",null,n(f.activeDeviceCount)+" 個線上",1)])])])]),_:1})]),_:1}),e(u,{span:12,lg:8},{default:t(()=>[e(v,{class:"module-card",shadow:"hover",onClick:l[2]||(l[2]=s=>k("tag"))},{default:t(()=>[a("div",al,[a("div",sl,[e(_,null,{default:t(()=>[e(y(te))]),_:1})]),a("div",ol,[l[40]||(l[40]=a("h3",null,"測點管理",-1)),l[41]||(l[41]=a("p",null,"管理測點資料，包含類比、數位測點，支援警報設定",-1)),a("div",nl,[a("span",null,n(f.tagCount)+" 個測點",1),a("span",null,n(f.alarmTagCount)+" 個警報",1)])])])]),_:1})]),_:1}),e(u,{span:12,lg:8},{default:t(()=>[e(v,{class:"module-card",shadow:"hover",onClick:l[3]||(l[3]=s=>k("group"))},{default:t(()=>[a("div",ul,[a("div",dl,[e(_,null,{default:t(()=>[e(y(ae))]),_:1})]),a("div",il,[l[42]||(l[42]=a("h3",null,"群組管理",-1)),l[43]||(l[43]=a("p",null,"管理測點群組，支援群組分類和地區組織",-1)),a("div",rl,[a("span",null,n(f.groupCount)+" 個群組",1),a("span",null,n(f.groupTagCount)+" 個測點",1)])])])]),_:1})]),_:1}),e(u,{span:12,lg:8},{default:t(()=>[e(v,{class:"module-card",shadow:"hover",onClick:l[4]||(l[4]=s=>k("region"))},{default:t(()=>[a("div",pl,[a("div",ml,[e(_,null,{default:t(()=>[e(y(xe))]),_:1})]),a("div",fl,[l[45]||(l[45]=a("h3",null,"地區管理",-1)),l[46]||(l[46]=a("p",null,"管理系統地區結構，支援階層式地區組織",-1)),a("div",vl,[a("span",null,n(f.regionCount)+" 個地區",1),l[44]||(l[44]=a("span",null,"階層式管理",-1))])])])]),_:1})]),_:1})]),_:1})]),a("div",_l,[l[57]||(l[57]=a("h2",null,"快速操作",-1)),e(g,{gutter:20},{default:t(()=>[e(u,{span:8},{default:t(()=>[e(v,{class:"action-card"},{default:t(()=>[a("div",gl,[e(_,{class:"action-icon"},{default:t(()=>[e(y(Ie))]),_:1}),l[49]||(l[49]=a("h4",null,"新增測點",-1)),l[50]||(l[50]=a("p",null,"快速新增新的測點",-1)),e(C,{type:"primary",onClick:re},{default:t(()=>l[48]||(l[48]=[d("立即新增",-1)])),_:1,__:[48]})])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(v,{class:"action-card"},{default:t(()=>[a("div",cl,[e(_,{class:"action-icon"},{default:t(()=>[e(y(Ae))]),_:1}),l[52]||(l[52]=a("h4",null,"即時監控",-1)),l[53]||(l[53]=a("p",null,"查看測點即時數據",-1)),e(C,{type:"success",onClick:pe},{default:t(()=>l[51]||(l[51]=[d("開始監控",-1)])),_:1,__:[51]})])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(v,{class:"action-card"},{default:t(()=>[a("div",Vl,[e(_,{class:"action-icon"},{default:t(()=>[e(y(ke))]),_:1}),l[55]||(l[55]=a("h4",null,"匯出資料",-1)),l[56]||(l[56]=a("p",null,"匯出測點配置資料",-1)),e(C,{type:"warning",onClick:me},{default:t(()=>l[54]||(l[54]=[d("開始匯出",-1)])),_:1,__:[54]})])]),_:1})]),_:1})]),_:1})]),a("div",bl,[l[69]||(l[69]=a("h2",null,"系統狀態",-1)),e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(v,null,{header:t(()=>l[58]||(l[58]=[a("span",null,"通訊狀態",-1)])),default:t(()=>[a("div",yl,[a("div",Cl,[l[60]||(l[60]=a("span",{class:"status-label"},"TCP 通道",-1)),e(w,{type:"success"},{default:t(()=>l[59]||(l[59]=[d("正常",-1)])),_:1,__:[59]})]),a("div",wl,[l[62]||(l[62]=a("span",{class:"status-label"},"OBIX 通道",-1)),e(w,{type:"success"},{default:t(()=>l[61]||(l[61]=[d("正常",-1)])),_:1,__:[61]})]),a("div",Tl,[l[64]||(l[64]=a("span",{class:"status-label"},"Desigo CC",-1)),e(w,{type:"warning"},{default:t(()=>l[63]||(l[63]=[d("警告",-1)])),_:1,__:[63]})])])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(v,null,{header:t(()=>l[65]||(l[65]=[a("span",null,"警報狀態",-1)])),default:t(()=>[a("div",Dl,[a("div",xl,[l[66]||(l[66]=a("span",{class:"status-label"},"高溫警報",-1)),e(w,{type:"danger"},{default:t(()=>[d(n(f.highTempAlarms)+" 個",1)]),_:1})]),a("div",Il,[l[67]||(l[67]=a("span",{class:"status-label"},"低溫警報",-1)),e(w,{type:"warning"},{default:t(()=>[d(n(f.lowTempAlarms)+" 個",1)]),_:1})]),a("div",Al,[l[68]||(l[68]=a("span",{class:"status-label"},"設備離線",-1)),e(w,{type:"info"},{default:t(()=>[d(n(f.offlineDevices)+" 個",1)]),_:1})])])]),_:1})]),_:1})]),_:1})])]),e(B,{modelValue:S.value,"onUpdate:modelValue":l[25]||(l[25]=s=>S.value=s),title:j.value==="create"?"新增標籤":"編輯標籤",width:"800px","close-on-click-modal":!1},{footer:t(()=>[a("div",Ul,[e(C,{onClick:l[24]||(l[24]=s=>S.value=!1)},{default:t(()=>l[72]||(l[72]=[d("取消",-1)])),_:1,__:[72]}),e(C,{type:"primary",onClick:_e,loading:F.value},{default:t(()=>[d(n(j.value==="create"?"新增":"更新"),1)]),_:1},8,["loading"])])]),default:t(()=>[e(W,{ref_key:"editFormRef",ref:q,model:o,rules:oe,"label-width":"120px"},{default:t(()=>[e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(i,{label:"標籤名稱",prop:"TagName"},{default:t(()=>[e(h,{modelValue:o.TagName,"onUpdate:modelValue":l[5]||(l[5]=s=>o.TagName=s),placeholder:"請輸入標籤名稱"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(i,{label:"標籤類型",prop:"TagType"},{default:t(()=>[e(E,{modelValue:o.TagType,"onUpdate:modelValue":l[6]||(l[6]=s=>o.TagType=s),placeholder:"請選擇標籤類型",style:{width:"100%"}},{default:t(()=>[(c(!0),D(U,null,M(ne,s=>(c(),L(R,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(i,{label:"資料類型",prop:"DataType"},{default:t(()=>[e(E,{modelValue:o.DataType,"onUpdate:modelValue":l[7]||(l[7]=s=>o.DataType=s),placeholder:"請選擇資料類型",style:{width:"100%"}},{default:t(()=>[(c(!0),D(U,null,M(ue,s=>(c(),L(R,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(i,{label:"位址",prop:"Address"},{default:t(()=>[e(h,{modelValue:o.Address,"onUpdate:modelValue":l[8]||(l[8]=s=>o.Address=s),placeholder:"請輸入位址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(i,{label:"設備"},{default:t(()=>[e(E,{modelValue:o.DeviceId,"onUpdate:modelValue":l[9]||(l[9]=s=>o.DeviceId=s),placeholder:"請選擇設備",clearable:"",style:{width:"100%"}},{default:t(()=>[(c(!0),D(U,null,M(de,s=>(c(),L(R,{key:s.DeviceId,label:s.DeviceName,value:s.DeviceId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(i,{label:"群組"},{default:t(()=>[e(E,{modelValue:o.GroupId,"onUpdate:modelValue":l[10]||(l[10]=s=>o.GroupId=s),placeholder:"請選擇群組",clearable:"",style:{width:"100%"}},{default:t(()=>[(c(!0),D(U,null,M(ie,s=>(c(),L(R,{key:s.GroupId,label:s.GroupName,value:s.GroupId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(i,{label:"描述"},{default:t(()=>[e(h,{modelValue:o.TagDescription,"onUpdate:modelValue":l[11]||(l[11]=s=>o.TagDescription=s),type:"textarea",rows:3,placeholder:"請輸入標籤描述"},null,8,["modelValue"])]),_:1}),e(g,{gutter:20},{default:t(()=>[e(u,{span:8},{default:t(()=>[e(i,{label:"單位"},{default:t(()=>[e(h,{modelValue:o.Unit,"onUpdate:modelValue":l[12]||(l[12]=s=>o.Unit=s),placeholder:"請輸入單位"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(i,{label:"掃描頻率"},{default:t(()=>[e(T,{modelValue:o.ScanRate,"onUpdate:modelValue":l[13]||(l[13]=s=>o.ScanRate=s),min:100,max:6e4,step:100,placeholder:"毫秒",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(i,{label:"預設值"},{default:t(()=>[e(T,{modelValue:o.DefaultValue,"onUpdate:modelValue":l[14]||(l[14]=s=>o.DefaultValue=s),placeholder:"預設值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(i,{label:"最小值"},{default:t(()=>[e(T,{modelValue:o.MinValue,"onUpdate:modelValue":l[15]||(l[15]=s=>o.MinValue=s),placeholder:"最小值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(i,{label:"最大值"},{default:t(()=>[e(T,{modelValue:o.MaxValue,"onUpdate:modelValue":l[16]||(l[16]=s=>o.MaxValue=s),placeholder:"最大值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:t(()=>[e(u,{span:8},{default:t(()=>[e(i,{label:"狀態"},{default:t(()=>[e($,{modelValue:o.IsActive,"onUpdate:modelValue":l[17]||(l[17]=s=>o.IsActive=s),"active-text":"啟用","inactive-text":"停用"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(i,{label:"唯讀"},{default:t(()=>[e($,{modelValue:o.IsReadOnly,"onUpdate:modelValue":l[18]||(l[18]=s=>o.IsReadOnly=s),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:8},{default:t(()=>[e(i,{label:"啟用警報"},{default:t(()=>[e($,{modelValue:o.AlarmEnabled,"onUpdate:modelValue":l[19]||(l[19]=s=>o.AlarmEnabled=s),"active-text":"啟用","inactive-text":"停用"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o.AlarmEnabled?(c(),D("div",kl,[e(Z,{"content-position":"left"},{default:t(()=>l[71]||(l[71]=[d("警報設定",-1)])),_:1,__:[71]}),e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(i,{label:"高警報限值"},{default:t(()=>[e(T,{modelValue:o.HighAlarmLimit,"onUpdate:modelValue":l[20]||(l[20]=s=>o.HighAlarmLimit=s),placeholder:"高警報限值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(i,{label:"低警報限值"},{default:t(()=>[e(T,{modelValue:o.LowAlarmLimit,"onUpdate:modelValue":l[21]||(l[21]=s=>o.LowAlarmLimit=s),placeholder:"低警報限值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(g,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(i,{label:"高警告限值"},{default:t(()=>[e(T,{modelValue:o.HighWarningLimit,"onUpdate:modelValue":l[22]||(l[22]=s=>o.HighWarningLimit=s),placeholder:"高警告限值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(i,{label:"低警告限值"},{default:t(()=>[e(T,{modelValue:o.LowWarningLimit,"onUpdate:modelValue":l[23]||(l[23]=s=>o.LowWarningLimit=s),placeholder:"低警告限值",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])):Q("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),e(B,{modelValue:Y.value,"onUpdate:modelValue":l[26]||(l[26]=s=>Y.value=s),title:"標籤詳情",width:"600px"},{default:t(()=>[p.value?(c(),L(Ve,{key:0,column:2,border:""},{default:t(()=>[e(V,{label:"標籤名稱"},{default:t(()=>[d(n(p.value.TagName),1)]),_:1}),e(V,{label:"標籤類型"},{default:t(()=>[d(n(p.value.TagType),1)]),_:1}),e(V,{label:"資料類型"},{default:t(()=>[d(n(p.value.DataType),1)]),_:1}),e(V,{label:"位址"},{default:t(()=>[d(n(p.value.Address),1)]),_:1}),e(V,{label:"設備"},{default:t(()=>[d(n(p.value.DeviceName||"-"),1)]),_:1}),e(V,{label:"群組"},{default:t(()=>[d(n(p.value.GroupName||"-"),1)]),_:1}),e(V,{label:"單位"},{default:t(()=>[d(n(p.value.Unit||"-"),1)]),_:1}),e(V,{label:"掃描頻率"},{default:t(()=>[d(n(p.value.ScanRate||"-")+" ms",1)]),_:1}),e(V,{label:"狀態"},{default:t(()=>[e(w,{type:p.value.IsActive?"success":"danger"},{default:t(()=>[d(n(p.value.IsActive?"啟用":"停用"),1)]),_:1},8,["type"])]),_:1}),e(V,{label:"唯讀"},{default:t(()=>[e(w,{type:p.value.IsReadOnly?"warning":"info"},{default:t(()=>[d(n(p.value.IsReadOnly?"是":"否"),1)]),_:1},8,["type"])]),_:1}),e(V,{label:"警報啟用"},{default:t(()=>[e(w,{type:p.value.AlarmEnabled?"success":"info"},{default:t(()=>[d(n(p.value.AlarmEnabled?"啟用":"停用"),1)]),_:1},8,["type"])]),_:1}),e(V,{label:"創建時間",span:2},{default:t(()=>[d(n(K(p.value.CreatedTime)),1)]),_:1}),e(V,{label:"描述",span:2},{default:t(()=>[d(n(p.value.TagDescription||"-"),1)]),_:1})]),_:1})):Q("",!0)]),_:1},8,["modelValue"]),e(B,{modelValue:z.value,"onUpdate:modelValue":l[28]||(l[28]=s=>z.value=s),title:"標籤即時值",width:"500px"},{default:t(()=>[x.value?(c(),D("div",Ll,[e(v,null,{default:t(()=>[a("div",Gl,[a("div",Sl,n(x.value.TagName),1),a("div",Nl,[a("span",hl,n(x.value.Value),1),a("span",Rl,n(x.value.Unit||""),1)]),a("div",El,[e(w,{type:ce(x.value.Quality)},{default:t(()=>[d(n(x.value.Quality),1)]),_:1},8,["type"])]),a("div",$l," 更新時間: "+n(K(x.value.Timestamp)),1)])]),_:1}),p.value?.IsReadOnly?Q("",!0):(c(),D("div",Bl,[e(Z,{"content-position":"left"},{default:t(()=>l[73]||(l[73]=[d("寫入值",-1)])),_:1,__:[73]}),e(W,{inline:!0},{default:t(()=>[e(i,null,{default:t(()=>[e(T,{modelValue:N.value,"onUpdate:modelValue":l[27]||(l[27]=s=>N.value=s),placeholder:"請輸入要寫入的值",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(C,{type:"primary",onClick:ge,loading:H.value},{default:t(()=>l[74]||(l[74]=[d(" 寫入 ",-1)])),_:1,__:[74]},8,["loading"])]),_:1})]),_:1})]))])):(c(),D("div",Ml,[e(be,{rows:3,animated:""})]))]),_:1},8,["modelValue"]),e(B,{modelValue:G.value,"onUpdate:modelValue":l[31]||(l[31]=s=>G.value=s),title:"匯入標籤",width:"500px"},{footer:t(()=>[a("div",Pl,[e(C,{onClick:l[30]||(l[30]=s=>G.value=!1)},{default:t(()=>l[77]||(l[77]=[d("取消",-1)])),_:1,__:[77]}),e(C,{type:"primary",onClick:ve,loading:O.value},{default:t(()=>l[78]||(l[78]=[d(" 匯入 ",-1)])),_:1,__:[78]},8,["loading"])])]),default:t(()=>[e(W,{"label-width":"120px"},{default:t(()=>[e(i,{label:"選擇檔案"},{default:t(()=>[e(ye,{ref:"uploadRef","auto-upload":!1,limit:1,accept:".xlsx,.xls,.csv","on-change":fe,"file-list":se.value},{tip:t(()=>l[76]||(l[76]=[a("div",{class:"el-upload__tip"}," 支援 Excel (.xlsx, .xls) 和 CSV (.csv) 格式 ",-1)])),default:t(()=>[e(C,{icon:y(Ue)},{default:t(()=>l[75]||(l[75]=[d("選擇檔案",-1)])),_:1,__:[75]},8,["icon"])]),_:1},8,["file-list"])]),_:1}),e(i,{label:"覆蓋現有"},{default:t(()=>[e($,{modelValue:X.overwriteExisting,"onUpdate:modelValue":l[29]||(l[29]=s=>X.overwriteExisting=s),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])],64)}}});const Ql=De(Ol,[["__scopeId","data-v-dd53d063"]]);export{Ql as default};
