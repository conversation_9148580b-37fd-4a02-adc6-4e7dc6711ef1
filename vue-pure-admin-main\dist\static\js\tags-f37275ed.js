import{p as a}from"./dataService-42a3bdf6.js";const I=[{value:"AI",label:"類比輸入"},{value:"AO",label:"類比輸出"},{value:"DI",label:"數位輸入"},{value:"DO",label:"數位輸出"},{value:"CALC",label:"計算標籤"},{value:"VIRTUAL",label:"虛擬標籤"}],D=[{value:"BOOL",label:"布林值"},{value:"INT",label:"整數"},{value:"REAL",label:"實數"},{value:"STRING",label:"字串"},{value:"DWORD",label:"雙字"},{value:"WORD",label:"字"}],p=[{DeviceId:"DEV001",DeviceName:"高雄火車站商業大樓電力管理系統通道.ECP-A電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV002",DeviceName:"高雄火車站商業大樓電力管理系統通道.ECP-C電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV003",DeviceName:"高雄火車站商業大樓電力管理系統通道.MB-B21電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV004",DeviceName:"高雄火車站商業大樓電力管理系統通道.LV-B01-1電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV005",DeviceName:"高雄火車站商業大樓電力管理系統通道.CB-B05電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV006",DeviceName:"高雄火車站商業大樓電力管理系統通道.MB-B11電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV007",DeviceName:"高雄火車站商業大樓電力管理系統通道.LV-B01-2電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV008",DeviceName:"高雄火車站商業大樓電力管理系統通道.LV-B04-2電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV009",DeviceName:"高雄火車站商業大樓電力管理系統通道.EECP-D電表",DeviceType:"PowerMeter",IsOnline:!0},{DeviceId:"DEV010",DeviceName:"高雄火車站商業大樓電力管理系統通道.CB-B01電表",DeviceType:"PowerMeter",IsOnline:!0}],l=[{GroupId:"GRP001",GroupName:"生產線A",GroupDescription:"主要生產線監控點",IsActive:!0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z"},{GroupId:"GRP002",GroupName:"生產線B",GroupDescription:"次要生產線監控點",IsActive:!0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z"},{GroupId:"GRP003",GroupName:"公用設施",GroupDescription:"水電氣監控點",IsActive:!0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z"},{GroupId:"GRP004",GroupName:"安全系統",GroupDescription:"安全監控點",IsActive:!0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z"},{GroupId:"GRP005",GroupName:"環境監控",GroupDescription:"溫濕度監控點",IsActive:!0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z"}],c=[{TagId:"TAG001",TagName:"ECP-A電表第1相電流",TagDescription:"ECP-A電表第1相電流",TagType:"AI",DataType:"REAL",DeviceId:"DEV001",DeviceName:"高雄火車站商業大樓電力管理系統通道.ECP-A電表",GroupId:"GRP001",GroupName:"預設地區",Address:"1A",Unit:"A",MinValue:0,MaxValue:1e3,IsReadOnly:!1,IsActive:!0,AlarmEnabled:!0,HighAlarmLimit:800,LowAlarmLimit:0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z",UpdatedTime:new Date().toISOString()},{TagId:"TAG002",TagName:"ECP-C電表第1相電流",TagDescription:"ECP-C電表第1相電流",TagType:"AI",DataType:"REAL",DeviceId:"DEV002",DeviceName:"高雄火車站商業大樓電力管理系統通道.ECP-C電表",GroupId:"GRP001",GroupName:"預設地區",Address:"1A",Unit:"A",MinValue:0,MaxValue:1e3,IsReadOnly:!1,IsActive:!0,AlarmEnabled:!0,HighAlarmLimit:800,LowAlarmLimit:0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z",UpdatedTime:new Date().toISOString()},{TagId:"TAG003",TagName:"MB-B21電表第1相電流",TagDescription:"MB-B21電表第1相電流",TagType:"AI",DataType:"REAL",DeviceId:"DEV003",DeviceName:"高雄火車站商業大樓電力管理系統通道.MB-B21電表",GroupId:"GRP001",GroupName:"預設地區",Address:"1A",Unit:"A",MinValue:0,MaxValue:1e3,IsReadOnly:!1,IsActive:!0,AlarmEnabled:!0,HighAlarmLimit:800,LowAlarmLimit:0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z",UpdatedTime:new Date().toISOString()},{TagId:"TAG004",TagName:"LV-B01-1電表第1相電流",TagDescription:"LV-B01-1電表第1相電流",TagType:"AI",DataType:"REAL",DeviceId:"DEV004",DeviceName:"高雄火車站商業大樓電力管理系統通道.LV-B01-1電表",GroupId:"GRP001",GroupName:"預設地區",Address:"1A",Unit:"A",MinValue:0,MaxValue:1e3,IsReadOnly:!1,IsActive:!0,AlarmEnabled:!0,HighAlarmLimit:800,LowAlarmLimit:0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z",UpdatedTime:new Date().toISOString()},{TagId:"TAG005",TagName:"CB-B05電表第1相電流",TagDescription:"CB-B05電表第1相電流",TagType:"AI",DataType:"REAL",DeviceId:"DEV005",DeviceName:"高雄火車站商業大樓電力管理系統通道.CB-B05電表",GroupId:"GRP001",GroupName:"預設地區",Address:"1A",Unit:"A",MinValue:0,MaxValue:1e3,IsReadOnly:!1,IsActive:!0,AlarmEnabled:!0,HighAlarmLimit:800,LowAlarmLimit:0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z",UpdatedTime:new Date().toISOString()},{TagId:"TAG006",TagName:"MB-B11電表第1相電流",TagDescription:"MB-B11電表第1相電流",TagType:"AI",DataType:"REAL",DeviceId:"DEV006",DeviceName:"高雄火車站商業大樓電力管理系統通道.MB-B11電表",GroupId:"GRP001",GroupName:"預設地區",Address:"1A",Unit:"A",MinValue:0,MaxValue:1e3,IsReadOnly:!1,IsActive:!0,AlarmEnabled:!0,HighAlarmLimit:800,LowAlarmLimit:0,CustomerId:"fdff1878-a54a-44ee-b82c-a62bdc5cdb55",CreatedTime:"2024-01-01T00:00:00Z",UpdatedTime:new Date().toISOString()},{TagId:"TAG003",TagName:"FLOW_001",TagDescription:"生產線A流量",TagType:"AI",DataType:"REAL",DeviceId:"DEV004",GroupId:"GRP001",Address:"40003",Unit:"L/min",MinValue:0,MaxValue:1e3,CurrentValue:150.8,IsActive:!0,LastUpdateTime:new Date().toISOString(),CreateTime:"2024-01-01T00:00:00Z",UpdateTime:new Date().toISOString()},{TagId:"TAG004",TagName:"MOTOR_001_RUN",TagDescription:"馬達1運行狀態",TagType:"DI",DataType:"BOOL",DeviceId:"DEV001",GroupId:"GRP001",Address:"10001",Unit:"",MinValue:0,MaxValue:1,CurrentValue:1,IsActive:!0,LastUpdateTime:new Date().toISOString(),CreateTime:"2024-01-01T00:00:00Z",UpdateTime:new Date().toISOString()},{TagId:"TAG005",TagName:"VALVE_001_CMD",TagDescription:"閥門1控制命令",TagType:"DO",DataType:"BOOL",DeviceId:"DEV001",GroupId:"GRP001",Address:"00001",Unit:"",MinValue:0,MaxValue:1,CurrentValue:0,IsActive:!0,LastUpdateTime:new Date().toISOString(),CreateTime:"2024-01-01T00:00:00Z",UpdateTime:new Date().toISOString()}],v={totalTags:c.length,activeTags:c.filter(e=>e.IsActive).length,inactiveTags:c.filter(e=>!e.IsActive).length,onlineDevices:p.filter(e=>e.IsOnline).length,offlineDevices:p.filter(e=>!e.IsOnline).length,totalDevices:p.length,totalGroups:l.length};class T{static delay(t=500){return new Promise(r=>setTimeout(r,t))}static async getTags(t){await this.delay();let r=[...c];t.tagName&&(r=r.filter(o=>o.TagName.toLowerCase().includes(t.tagName.toLowerCase())||o.TagDescription.toLowerCase().includes(t.tagName.toLowerCase()))),t.tagType&&(r=r.filter(o=>o.TagType===t.tagType)),t.dataType&&(r=r.filter(o=>o.DataType===t.dataType)),t.deviceId&&(r=r.filter(o=>o.DeviceId===t.deviceId)),t.groupId&&(r=r.filter(o=>o.GroupId===t.groupId)),t.isActive!==void 0&&(r=r.filter(o=>o.IsActive===t.isActive));const s=(t.pageIndex-1)*t.pageSize,g=s+t.pageSize;return{data:r.slice(s,g),total:r.length,pageIndex:t.pageIndex,pageSize:t.pageSize,totalPages:Math.ceil(r.length/t.pageSize)}}static async getTagTypes(){return await this.delay(200),I}static async getDataTypes(){return await this.delay(200),D}static async getDevices(){return await this.delay(200),p}static async getGroups(){return await this.delay(200),l}static async getTagStatistics(){return await this.delay(200),v}static async createTag(t){await this.delay(800);const r={TagId:`TAG${String(c.length+1).padStart(3,"0")}`,TagName:t.TagName||"",TagDescription:t.TagDescription||"",TagType:t.TagType||"AI",DataType:t.DataType||"REAL",DeviceId:t.DeviceId||"",GroupId:t.GroupId||"",Address:t.Address||"",Unit:t.Unit||"",MinValue:t.MinValue||0,MaxValue:t.MaxValue||100,CurrentValue:t.CurrentValue||0,IsActive:t.IsActive!==void 0?t.IsActive:!0,LastUpdateTime:new Date().toISOString(),CreateTime:new Date().toISOString(),UpdateTime:new Date().toISOString()};return c.push(r),r}static async updateTag(t,r){await this.delay(800);const s=c.findIndex(g=>g.TagId===t);if(s===-1)throw new Error("標籤不存在");return c[s]={...c[s],...r,TagId:t,UpdateTime:new Date().toISOString()},c[s]}static async deleteTag(t){await this.delay(500);const r=c.findIndex(s=>s.TagId===t);if(r===-1)throw new Error("標籤不存在");c.splice(r,1)}}const i=()=>({}).VITE_USE_MOCK==="true",n=()=>{const e=localStorage.getItem("PLC_CUSTOMER_ID");if(e)return e;const t={}.VITE_CUSTOMER_ID;return t||"fdff1878-a54a-44ee-b82c-a62bdc5cdb55"},A={getTags:e=>i()?T.getTags(e||{}).then(t=>({Message:"Success",ReturnCode:0,Detail:{CheckTagRalatedItemsUpdateTimeOK:!1,TagTypeList:[],SaveTypeList:[],ExpressionModeList:[],DataTypeList:[],LogInterValType:[],UnitList:[],AlarmStatusList:[],AlarmExceptionUntilList:[],AlarmExceptionActionList:[],EnumDigitalAlarmValueList:[],EnumExpressionModeList:[],RegionHierarchyList:[],TagCategoryHierarchyList:[],CCTVMapList:[],TagList:t.data||[],TagRalatedItemsUpdateTime:new Date().toISOString()}})):a.get("/api/Tag/GetTagList",e),getTagsLegacy:e=>A.getTags(e).then(t=>({data:t.Detail.TagList||[],total:t.Detail.TagList?.length||0,page:1,pageSize:t.Detail.TagList?.length||0})),getTag:e=>{const t=n();return a.get(`/api/Tag/GetTag/${e}`,{customerId:t})},getTagProperties:async e=>{if(i()){const s={};return e.forEach((g,d)=>{const o=["Normal","Alarm","Status"],u=["NO","NC"];s[g]={Usage:o[d%o.length],ContactType:u[d%u.length]}}),Promise.resolve(s)}const t=50,r={};for(let s=0;s<e.length;s+=t){const d=e.slice(s,s+t).join(","),o=Math.floor(s/t)+1;Math.ceil(e.length/t);try{const m=(await a.get("/api/Tag/GetTagProperties",{},{tags:d})).Detail?.Properties||{};Object.assign(r,m)}catch(u){console.error(`❌ 第 ${o} 批次 GetTagProperties API 調用失敗:`,u),console.error("❌ 錯誤詳情:",{message:u.message,status:u.response?.status,statusText:u.response?.statusText,data:u.response?.data})}}return r},createTag:e=>{const t=n(),r={...e,CustomerId:t};return a.post("/Tag/CreateTag",r)},updateTag:e=>{const t=n(),r={...e,CustomerId:t};return a.put("/Tag/UpdateTag",r)},deleteTag:e=>{const t=n();return a.delete(`/api/Tag/DeleteTag/${e}?customerId=${t}`)},batchOperation:e=>{const t=n(),r={...e,CustomerId:t};return a.post("/api/Tag/BatchOperation",r)},importTags:e=>{const t=n(),r=new FormData;return r.append("file",e.file),r.append("overwriteExisting",e.overwriteExisting.toString()),r.append("customerId",t),a.postForm("/api/Tag/ImportTags",r)},exportTags:e=>{const t=n(),r={...e,customerId:t};return a.get("/api/Tag/ExportTags",r)},getTagStatistics:e=>{if(i())return T.getTagStatistics();const t=e||n();return a.get("/api/Tag/GetTagStatistics",{customerId:t})},getDevices:e=>{if(i())return T.getDevices();const t=e||n();return a.get("/api/Tag/GetDeviceList",{customerId:t})},getDeviceCategoryHierarchy:e=>{if(i())return Promise.resolve([]);const t=e||n();return a.get("/api/Tag/GetDeviceCategoryHierarchyList",{customerId:t})},createDeviceCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/CreateNewDeviceCategory",e),updateDeviceCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/UpdateDeviceCategory",e),deleteDeviceCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/DeleteDeviceCategory",e),deleteDevice:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/DeleteDevice",e),deleteTagCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/DeleteTagCategory",e),deleteRegion:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/DeleteRegion",e),deleteTagChannel:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/DeleteTagChannel",e),deleteGroup:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/DeleteGroup",e),createTagCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/CreateNewTagCategory",e),updateTagCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/UpdateTagCategory",e),createRegion:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/CreateNewRegion",e),updateRegion:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/UpdateRegion",e),createTagChannel:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/CreateNewTagChannel",e),updateTagChannel:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/UpdateTagChannel",e),createGroup:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/CreateNewGroup",e),updateGroup:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/UpdateGroup",e),createGroupCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/CreateNewGroupCategory",e),updateGroupCategory:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/UpdateGroupCategory",e),createDevice:e=>i()?Promise.resolve({success:!0}):a.post("/Tag/CreateNewDevice",e),updateDevice:e=>i()?Promise.resolve({success:!0}):a.post("/Tag/UpdateDevice",e),getGroups:e=>{if(i())return T.getGroups();const t=e||n();return a.get("/api/Tag/GetGroups",{customerId:t})},getTagTypes:()=>{if(i())return T.getTagTypes();const e=n();return a.get("/api/Tag/GetTagTypes",{customerId:e})},getDataTypes:()=>{if(i())return T.getDataTypes();const e=n();return a.get("/api/Tag/GetDataTypes",{customerId:e})},validateTagName:(e,t)=>{const r=n();return a.get("/api/Tag/ValidateTagName",{tagName:e,excludeTagId:t,customerId:r})},getTagValueHistory:e=>{const t=n(),r={...e,customerId:t};return a.get("/api/Tag/GetTagValueHistory",r)},getTagRealTimeValue:e=>{const t=n();return a.get(`/api/Tag/GetTagRealTimeValue/${e}`,{customerId:t})},writeTagValue:e=>{const t=n(),r={...e,CustomerId:t};return a.post("/api/Tag/WriteTagValue",r)},setChannelStatus:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/SetTagChannelAbility",e),setDeviceStatus:e=>i()?Promise.resolve({success:!0}):a.post("/api/Tag/SetTagDeviceAbility",e)},C={getRegionList:e=>a.get("/api/Tag/GetRegionHierarchyList",{params:e}),createRegion:e=>a.post("/api/regions",e),updateRegion:(e,t)=>a.put(`/api/regions/${e}`,t),deleteRegion:e=>a.delete(`/api/regions/${e}`),getRegionTree:()=>a.get("/api/Tag/GetRegionHierarchyList")},f={getUnitList:e=>a.get("/api/Tag/GetTagList",e),createUnit:e=>a.post("/api/units",e),updateUnit:(e,t)=>a.put(`/api/units/${e}`,t),deleteUnit:e=>a.delete(`/api/units/${e}`)};export{C as r,A as t,f as u};
