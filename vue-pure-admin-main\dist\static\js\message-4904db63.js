import{t as ee,r as B,a as Ve,x as Ce}from"./index-0d74a956.js";import{d as Se,r as w,G as U,o as Te,p as n,q as xe,c as v,e as z,f as s,g as e,w as t,h as V,m as d,X as te,Y as le,n as E,j as q,t as r,l as Me,E as T,aF as Ee,_ as ke}from"./index-329ed960.js";const Ie={class:"message-container"},Ne={class:"page-header"},Ue={class:"header-right"},ze={class:"search-section"},Le={class:"pagination-container"},De={class:"stats-overview"},Pe={class:"stat-item"},He={class:"stat-number"},Re={class:"stat-item"},Ye={class:"stat-number success"},Be={class:"stat-item"},qe={class:"stat-number danger"},Fe={class:"stat-item"},je={class:"stat-number warning"},Ge={class:"chart-section"},Oe={key:0,class:"message-detail"},$e={class:"message-content"},Ae={class:"dialog-footer"},Xe=Se({__name:"message",setup(Je){const L=w("send"),D=w(!1),P=w(!1),k=w(!1),p=w(null),H=w(),i=U({groupId:"",method:"",title:"",content:"",sendType:"now",sendTime:"",priority:"medium"}),I=w([]),c=U({groupId:"",method:"",status:"",dateRange:[]}),g=U({currentPage:1,pageSize:10,total:0}),F=w([]),x=U({totalSent:0,successCount:0,failedCount:0,pendingCount:0}),ae={groupId:[{required:!0,message:"請選擇通知群組",trigger:"change"}],method:[{required:!0,message:"請選擇通知方式",trigger:"change"}],title:[{required:!0,message:"請輸入訊息標題",trigger:"blur"}],content:[{required:!0,message:"請輸入訊息內容",trigger:"blur"}],priority:[{required:!0,message:"請選擇優先級",trigger:"change"}]},j=o=>({SMS:"warning",LINE:"success",Email:"info"})[o]||"info",G=o=>({success:"success",failed:"danger",pending:"warning"})[o]||"info",O=o=>({success:"成功",failed:"失敗",pending:"等待中"})[o]||o,$=o=>({low:"info",medium:"",high:"warning",urgent:"danger"})[o]||"",A=o=>({low:"低",medium:"中",high:"高",urgent:"緊急"})[o]||o,oe=o=>{const l=I.value.find(f=>f.id===o);l&&(i.method=l.method)},se=async()=>{try{await H.value?.validate(),D.value=!0,await new Promise(o=>setTimeout(o,2e3)),T.success("訊息發送成功"),X()}catch{T.error("訊息發送失敗")}finally{D.value=!1}},X=()=>{Object.assign(i,{groupId:"",method:"",title:"",content:"",sendType:"now",sendTime:"",priority:"medium"}),H.value?.clearValidate()},J=()=>{g.currentPage=1,M()},ne=()=>{Object.assign(c,{groupId:"",method:"",status:"",dateRange:[]}),J()},de=o=>{g.pageSize=o,M()},ie=o=>{g.currentPage=o,M()},ue=o=>{p.value=o,k.value=!0},re=async o=>{try{await Ee.confirm("確定要重新發送此訊息嗎？","確認重發",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),T.success("重發成功"),M()}catch{}},pe=()=>{L.value="send"},me=async()=>{try{I.value=[{id:"1",name:"緊急通知群組",method:"SMS"},{id:"2",name:"LINE 通知群組",method:"LINE"},{id:"3",name:"Email 通知群組",method:"Email"}]}catch{T.error("載入群組列表失敗")}},M=async()=>{P.value=!0;try{await new Promise(o=>setTimeout(o,500)),F.value=[{id:"1",title:"系統維護通知",groupName:"緊急通知群組",method:"SMS",recipientCount:5,sendTime:"2024-01-20 10:30:00",status:"success",priority:"high",content:"系統將於今晚 22:00 進行維護，預計維護時間 2 小時。"},{id:"2",title:"設備異常警報",groupName:"LINE 通知群組",method:"LINE",recipientCount:8,sendTime:"2024-01-20 09:15:00",status:"success",priority:"urgent",content:"設備 A01 溫度異常，請立即檢查。"},{id:"3",title:"月報發送",groupName:"Email 通知群組",method:"Email",recipientCount:12,sendTime:"2024-01-20 08:00:00",status:"failed",priority:"low",content:"本月系統運行報告已生成，請查收。"}],g.total=3}catch{T.error("載入歷史數據失敗")}finally{P.value=!1}},ce=async()=>{try{Object.assign(x,{totalSent:156,successCount:142,failedCount:8,pendingCount:6})}catch{T.error("載入統計數據失敗")}};return Te(async()=>{await Promise.all([me(),M(),ce()])}),(o,l)=>{const f=n("el-icon"),y=n("el-button"),u=n("el-option"),C=n("el-select"),m=n("el-form-item"),_=n("el-col"),N=n("el-row"),K=n("el-input"),Q=n("el-radio"),_e=n("el-radio-group"),W=n("el-date-picker"),Z=n("el-form"),R=n("el-card"),Y=n("el-tab-pane"),b=n("el-table-column"),S=n("el-tag"),ge=n("el-table"),fe=n("el-pagination"),ve=n("el-tabs"),h=n("el-descriptions-item"),ye=n("el-descriptions"),be=n("el-dialog"),he=xe("loading");return v(),z("div",Ie,[s("div",Ne,[l[17]||(l[17]=s("div",{class:"header-left"},[s("h1",null,"訊息管理"),s("p",null,"發送通知訊息、查看歷史記錄和訊息統計")],-1)),s("div",Ue,[e(y,{type:"primary",onClick:pe},{default:t(()=>[e(f,null,{default:t(()=>[e(V(ee))]),_:1}),l[16]||(l[16]=d(" 發送訊息 ",-1))]),_:1,__:[16]})])]),e(ve,{modelValue:L.value,"onUpdate:modelValue":l[13]||(l[13]=a=>L.value=a),class:"message-tabs"},{default:t(()=>[e(Y,{label:"發送訊息",name:"send"},{default:t(()=>[e(R,{class:"send-card",shadow:"never"},{default:t(()=>[e(Z,{ref_key:"sendFormRef",ref:H,model:i,rules:ae,"label-width":"100px"},{default:t(()=>[e(N,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(m,{label:"通知群組",prop:"groupId"},{default:t(()=>[e(C,{modelValue:i.groupId,"onUpdate:modelValue":l[0]||(l[0]=a=>i.groupId=a),placeholder:"請選擇通知群組",style:{width:"100%"},onChange:oe},{default:t(()=>[(v(!0),z(te,null,le(I.value,a=>(v(),E(u,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(m,{label:"通知方式",prop:"method"},{default:t(()=>[e(C,{modelValue:i.method,"onUpdate:modelValue":l[1]||(l[1]=a=>i.method=a),placeholder:"請選擇通知方式",style:{width:"100%"}},{default:t(()=>[e(u,{label:"SMS",value:"SMS"}),e(u,{label:"LINE",value:"LINE"}),e(u,{label:"Email",value:"Email"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,{label:"訊息標題",prop:"title"},{default:t(()=>[e(K,{modelValue:i.title,"onUpdate:modelValue":l[2]||(l[2]=a=>i.title=a),placeholder:"請輸入訊息標題",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(m,{label:"訊息內容",prop:"content"},{default:t(()=>[e(K,{modelValue:i.content,"onUpdate:modelValue":l[3]||(l[3]=a=>i.content=a),type:"textarea",rows:6,placeholder:"請輸入訊息內容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(N,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(m,{label:"發送時間",prop:"sendTime"},{default:t(()=>[e(_e,{modelValue:i.sendType,"onUpdate:modelValue":l[4]||(l[4]=a=>i.sendType=a)},{default:t(()=>[e(Q,{label:"now"},{default:t(()=>l[18]||(l[18]=[d("立即發送",-1)])),_:1,__:[18]}),e(Q,{label:"scheduled"},{default:t(()=>l[19]||(l[19]=[d("定時發送",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"]),i.sendType==="scheduled"?(v(),E(W,{key:0,modelValue:i.sendTime,"onUpdate:modelValue":l[5]||(l[5]=a=>i.sendTime=a),type:"datetime",placeholder:"請選擇發送時間",style:{width:"100%","margin-top":"8px"},format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])):q("",!0)]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(m,{label:"優先級",prop:"priority"},{default:t(()=>[e(C,{modelValue:i.priority,"onUpdate:modelValue":l[6]||(l[6]=a=>i.priority=a),placeholder:"請選擇優先級",style:{width:"100%"}},{default:t(()=>[e(u,{label:"低",value:"low"}),e(u,{label:"中",value:"medium"}),e(u,{label:"高",value:"high"}),e(u,{label:"緊急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,null,{default:t(()=>[e(y,{type:"primary",onClick:se,loading:D.value},{default:t(()=>[e(f,null,{default:t(()=>[e(V(ee))]),_:1}),d(" "+r(i.sendType==="now"?"立即發送":"定時發送"),1)]),_:1},8,["loading"]),e(y,{onClick:X},{default:t(()=>[e(f,null,{default:t(()=>[e(V(B))]),_:1}),l[20]||(l[20]=d(" 重置 ",-1))]),_:1,__:[20]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1}),e(Y,{label:"訊息歷史",name:"history"},{default:t(()=>[e(R,{class:"history-card",shadow:"never"},{default:t(()=>[s("div",ze,[e(Z,{model:c,inline:""},{default:t(()=>[e(m,{label:"群組"},{default:t(()=>[e(C,{modelValue:c.groupId,"onUpdate:modelValue":l[7]||(l[7]=a=>c.groupId=a),placeholder:"請選擇群組",clearable:"",style:{width:"150px"}},{default:t(()=>[e(u,{label:"全部",value:""}),(v(!0),z(te,null,le(I.value,a=>(v(),E(u,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"方式"},{default:t(()=>[e(C,{modelValue:c.method,"onUpdate:modelValue":l[8]||(l[8]=a=>c.method=a),placeholder:"請選擇方式",clearable:"",style:{width:"120px"}},{default:t(()=>[e(u,{label:"全部",value:""}),e(u,{label:"SMS",value:"SMS"}),e(u,{label:"LINE",value:"LINE"}),e(u,{label:"Email",value:"Email"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"狀態"},{default:t(()=>[e(C,{modelValue:c.status,"onUpdate:modelValue":l[9]||(l[9]=a=>c.status=a),placeholder:"請選擇狀態",clearable:"",style:{width:"120px"}},{default:t(()=>[e(u,{label:"全部",value:""}),e(u,{label:"成功",value:"success"}),e(u,{label:"失敗",value:"failed"}),e(u,{label:"等待中",value:"pending"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"時間"},{default:t(()=>[e(W,{modelValue:c.dateRange,"onUpdate:modelValue":l[10]||(l[10]=a=>c.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"開始日期","end-placeholder":"結束日期",style:{width:"240px"}},null,8,["modelValue"])]),_:1}),e(m,null,{default:t(()=>[e(y,{type:"primary",onClick:J},{default:t(()=>[e(f,null,{default:t(()=>[e(V(Ve))]),_:1}),l[21]||(l[21]=d(" 搜尋 ",-1))]),_:1,__:[21]}),e(y,{onClick:ne},{default:t(()=>[e(f,null,{default:t(()=>[e(V(B))]),_:1}),l[22]||(l[22]=d(" 重置 ",-1))]),_:1,__:[22]})]),_:1})]),_:1},8,["model"])]),Me((v(),E(ge,{data:F.value,stripe:"",style:{width:"100%"}},{default:t(()=>[e(b,{prop:"title",label:"標題","min-width":"150"}),e(b,{prop:"groupName",label:"群組",width:"120"}),e(b,{prop:"method",label:"方式",width:"80"},{default:t(({row:a})=>[e(S,{type:j(a.method)},{default:t(()=>[d(r(a.method),1)]),_:2},1032,["type"])]),_:1}),e(b,{prop:"recipientCount",label:"接收人數",width:"100",align:"center"}),e(b,{prop:"sendTime",label:"發送時間",width:"180"}),e(b,{prop:"status",label:"狀態",width:"100",align:"center"},{default:t(({row:a})=>[e(S,{type:G(a.status)},{default:t(()=>[d(r(O(a.status)),1)]),_:2},1032,["type"])]),_:1}),e(b,{prop:"priority",label:"優先級",width:"80",align:"center"},{default:t(({row:a})=>[e(S,{type:$(a.priority),size:"small"},{default:t(()=>[d(r(A(a.priority)),1)]),_:2},1032,["type"])]),_:1}),e(b,{label:"操作",width:"150",fixed:"right"},{default:t(({row:a})=>[e(y,{size:"small",type:"primary",onClick:we=>ue(a)},{default:t(()=>[e(f,null,{default:t(()=>[e(V(Ce))]),_:1}),l[23]||(l[23]=d(" 詳情 ",-1))]),_:2,__:[23]},1032,["onClick"]),a.status==="failed"?(v(),E(y,{key:0,size:"small",type:"warning",onClick:we=>re(a)},{default:t(()=>[e(f,null,{default:t(()=>[e(V(B))]),_:1}),l[24]||(l[24]=d(" 重發 ",-1))]),_:2,__:[24]},1032,["onClick"])):q("",!0)]),_:1})]),_:1},8,["data"])),[[he,P.value]]),s("div",Le,[e(fe,{"current-page":g.currentPage,"onUpdate:currentPage":l[11]||(l[11]=a=>g.currentPage=a),"page-size":g.pageSize,"onUpdate:pageSize":l[12]||(l[12]=a=>g.pageSize=a),total:g.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:de,onCurrentChange:ie},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(Y,{label:"統計報表",name:"statistics"},{default:t(()=>[e(R,{class:"statistics-card",shadow:"never"},{default:t(()=>[s("div",De,[e(N,{gutter:20},{default:t(()=>[e(_,{span:6},{default:t(()=>[s("div",Pe,[s("div",He,r(x.totalSent),1),l[25]||(l[25]=s("div",{class:"stat-label"},"總發送數",-1))])]),_:1}),e(_,{span:6},{default:t(()=>[s("div",Re,[s("div",Ye,r(x.successCount),1),l[26]||(l[26]=s("div",{class:"stat-label"},"成功數",-1))])]),_:1}),e(_,{span:6},{default:t(()=>[s("div",Be,[s("div",qe,r(x.failedCount),1),l[27]||(l[27]=s("div",{class:"stat-label"},"失敗數",-1))])]),_:1}),e(_,{span:6},{default:t(()=>[s("div",Fe,[s("div",je,r(x.pendingCount),1),l[28]||(l[28]=s("div",{class:"stat-label"},"等待中",-1))])]),_:1})]),_:1})]),s("div",Ge,[e(N,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>l[29]||(l[29]=[s("div",{class:"chart-container"},[s("h3",null,"發送方式統計"),s("div",{class:"chart-placeholder"}," 圖表區域 - 發送方式分布 ")],-1)])),_:1,__:[29]}),e(_,{span:12},{default:t(()=>l[30]||(l[30]=[s("div",{class:"chart-container"},[s("h3",null,"發送趨勢"),s("div",{class:"chart-placeholder"}," 圖表區域 - 時間趨勢 ")],-1)])),_:1,__:[30]})]),_:1})])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(be,{modelValue:k.value,"onUpdate:modelValue":l[15]||(l[15]=a=>k.value=a),title:"訊息詳情",width:"600px"},{footer:t(()=>[s("div",Ae,[e(y,{onClick:l[14]||(l[14]=a=>k.value=!1)},{default:t(()=>l[31]||(l[31]=[d("關閉",-1)])),_:1,__:[31]})])]),default:t(()=>[p.value?(v(),z("div",Oe,[e(ye,{column:2,border:""},{default:t(()=>[e(h,{label:"標題"},{default:t(()=>[d(r(p.value.title),1)]),_:1}),e(h,{label:"群組"},{default:t(()=>[d(r(p.value.groupName),1)]),_:1}),e(h,{label:"通知方式"},{default:t(()=>[e(S,{type:j(p.value.method)},{default:t(()=>[d(r(p.value.method),1)]),_:1},8,["type"])]),_:1}),e(h,{label:"優先級"},{default:t(()=>[e(S,{type:$(p.value.priority)},{default:t(()=>[d(r(A(p.value.priority)),1)]),_:1},8,["type"])]),_:1}),e(h,{label:"發送時間"},{default:t(()=>[d(r(p.value.sendTime),1)]),_:1}),e(h,{label:"狀態"},{default:t(()=>[e(S,{type:G(p.value.status)},{default:t(()=>[d(r(O(p.value.status)),1)]),_:1},8,["type"])]),_:1}),e(h,{label:"接收人數",span:2},{default:t(()=>[d(r(p.value.recipientCount),1)]),_:1}),e(h,{label:"內容",span:2},{default:t(()=>[s("div",$e,r(p.value.content),1)]),_:1})]),_:1})])):q("",!0)]),_:1},8,["modelValue"])])}}});const We=ke(Xe,[["__scopeId","data-v-541130a5"]]);export{We as default};
