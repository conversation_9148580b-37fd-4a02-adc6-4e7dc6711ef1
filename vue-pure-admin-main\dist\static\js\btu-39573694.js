import{aG as U,d as me,r as b,G as H,o as pe,p as d,q as ce,c as V,e as I,f as o,g as e,w as l,m as n,l as fe,n as J,t as m,h as P,aH as W,X as _e,Y as ge,j as ve,E as T,aF as be,_ as Te}from"./index-329ed960.js";import{u as ye}from"./dataService-42a3bdf6.js";const j={getMeters:p=>U.request("get","/api/plc/system/btu/meters",{params:p}),createMeter:p=>U.request("post","/api/plc/system/btu/meters",{data:p}),updateMeter:(p,y)=>U.request("put",`/api/plc/system/btu/meters/${p}`,{data:y}),deleteMeter:p=>U.request("delete",`/api/plc/system/btu/meters/${p}`),calculateBTU:p=>U.request("post","/api/plc/system/btu/calculate",{data:p}),getMeterData:(p,y)=>U.request("get",`/api/plc/system/btu/meters/${p}/data`,{params:y}),exportResult:p=>U.request("post","/api/plc/system/btu/export",{data:p})},Ue={class:"btu-container"},Be={class:"tab-content"},we={class:"card-header"},Ve={class:"tab-content"},Me={key:0,class:"btu-result"},he={class:"result-item"},xe={class:"result-value"},De={class:"result-item"},ke={class:"result-value"},Ce={class:"result-item"},qe={class:"result-value"},Ne={class:"result-item"},Re={class:"result-value"},Fe={class:"chart-container"},Ie=me({__name:"btu",setup(p){ye();const y=b(),k=b(),$=b("meter"),C=b(!1),M=b(!1),B=b(!1),q=b(!1),N=b([]),r=H({id:"",name:"",meterNo:"",location:"",meterType:"",unit:"BTU",multiplier:1,installDate:"",description:"",status:"active"}),g=H({meterId:[],dateRange:[],statisticType:"daily",calculationType:"cumulative"}),v=b(null),A={name:[{required:!0,message:"請輸入BTU表名稱",trigger:"blur"}],meterNo:[{required:!0,message:"請輸入表號",trigger:"blur"}],location:[{required:!0,message:"請輸入安裝位置",trigger:"blur"}],meterType:[{required:!0,message:"請選擇表類型",trigger:"change"}],unit:[{required:!0,message:"請選擇單位",trigger:"change"}],multiplier:[{required:!0,message:"請輸入倍率",trigger:"blur"}],installDate:[{required:!0,message:"請選擇安裝日期",trigger:"change"}]},G={meterId:[{required:!0,message:"請選擇BTU表",trigger:"change"}],dateRange:[{required:!0,message:"請選擇查詢期間",trigger:"change"}]},O=s=>({heat_meter:"熱量表",cooling_meter:"冷量表",steam_meter:"蒸汽表",combined_meter:"綜合表"})[s]||s,X=s=>s?new Date(s).toLocaleDateString("zh-TW"):"-",K=s=>s?new Date(s).toLocaleString("zh-TW"):"-",Q=s=>s==null?"-":s.toLocaleString("zh-TW",{maximumFractionDigits:2}),Z=s=>({normal:"success",warning:"warning",error:"danger"})[s]||"info",ee=s=>({normal:"正常",warning:"警告",error:"異常"})[s]||"未知",te=s=>{Object.assign(r,s),B.value=!0},le=s=>{T.info("BTU表數據查看功能開發中...")},ae=async s=>{try{await be.confirm(`確定要刪除BTU表 "${s.name}" 嗎？`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"}),T.success("BTU表刪除成功"),await R()}catch(t){t!=="cancel"&&(console.error("刪除BTU表失敗:",t),T.error("刪除BTU表失敗"))}},se=async()=>{if(y.value)try{await y.value.validate();const s={Name:r.name,RegionId:r.regionId||void 0,MeterType:r.meterType||void 0,Properties:{meterNo:r.meterNo,location:r.location,multiplier:r.multiplier,installDate:r.installDate,description:r.description},Tags:r.tags||[]};let t;if(r.id?t=await j.updateMeter(r.id,s):t=await j.createMeter(s),t&&(t.ReturnCode===1||t.success))T.success(r.id?"BTU表更新成功":"BTU表新增成功"),B.value=!1,await R();else{const f=t?.Message||t?.message||"保存失敗";throw new Error(f)}}catch(s){console.error("保存BTU表失敗:",s);let t="保存BTU表失敗";s.message?t=s.message:s.response?.data?.Message?t=s.response.data.Message:s.response?.data?.message?t=s.response.data.message:typeof s=="string"&&(t=s),T.error({message:t,duration:5e3,showClose:!0})}},re=async()=>{if(k.value)try{await k.value.validate(),M.value=!0,v.value={totalBTU:"1,234,567",avgBTU:"12,345",maxBTU:"23,456",minBTU:"8,901",details:[{meterName:"BTU表1",timestamp:new Date().toISOString(),btuValue:12345,temperature:25.5,flow:100.2,pressure:1.5,efficiency:85.2,status:"normal"}]},T.success("BTU計算完成")}catch(s){console.error("BTU計算失敗:",s),T.error(s.message||"BTU計算失敗")}finally{M.value=!1}},oe=()=>{T.info("BTU結果匯出功能開發中...")},R=async()=>{try{C.value=!0,N.value=[{id:"1",name:"BTU表1",meterNo:"BTU001",location:"冷卻塔1",meterType:"heat_meter",unit:"BTU",multiplier:1,installDate:"2023-01-01",description:"主要冷卻系統",status:"active"}]}catch(s){console.error("載入BTU表列表失敗:",s),T.error(s.message||"載入BTU表列表失敗")}finally{C.value=!1}};return pe(async()=>{await R()}),(s,t)=>{const f=d("el-button"),i=d("el-table-column"),F=d("el-tag"),S=d("el-table"),h=d("el-card"),Y=d("el-tab-pane"),u=d("el-option"),w=d("el-select"),c=d("el-form-item"),_=d("el-col"),L=d("el-date-picker"),x=d("el-row"),ue=d("el-tabs"),D=d("el-input"),ne=d("el-input-number"),z=d("el-dialog"),ie=d("el-empty"),de=ce("loading");return V(),I("div",Ue,[t[33]||(t[33]=o("div",{class:"page-header"},[o("h2",null,"BTU 計算"),o("p",null,"BTU (British Thermal Unit) 熱量計算與管理系統")],-1)),e(h,{class:"btu-card"},{default:l(()=>[e(ue,{modelValue:$.value,"onUpdate:modelValue":t[6]||(t[6]=a=>$.value=a),type:"border-card"},{default:l(()=>[e(Y,{label:"BTU表管理",name:"meter"},{default:l(()=>[o("div",Be,[e(h,{shadow:"never"},{header:l(()=>[o("div",we,[t[19]||(t[19]=o("span",null,"BTU表設定",-1)),e(f,{type:"primary",onClick:t[0]||(t[0]=a=>B.value=!0)},{default:l(()=>t[18]||(t[18]=[n(" 新增BTU表 ",-1)])),_:1,__:[18]})])]),default:l(()=>[fe((V(),J(S,{data:N.value,stripe:"",border:""},{default:l(()=>[e(i,{prop:"name",label:"BTU表名稱",width:"200"}),e(i,{prop:"meterNo",label:"表號",width:"150"}),e(i,{prop:"location",label:"安裝位置",width:"200"}),e(i,{prop:"meterType",label:"表類型",width:"120"},{default:l(({row:a})=>[e(F,null,{default:l(()=>[n(m(O(a.meterType)),1)]),_:2},1024)]),_:1}),e(i,{prop:"unit",label:"單位",width:"100"}),e(i,{prop:"multiplier",label:"倍率",width:"100",align:"right"}),e(i,{prop:"installDate",label:"安裝日期",width:"120"},{default:l(({row:a})=>[n(m(X(a.installDate)),1)]),_:1}),e(i,{prop:"status",label:"狀態",width:"100"},{default:l(({row:a})=>[e(F,{type:a.status==="active"?"success":"danger"},{default:l(()=>[n(m(a.status==="active"?"正常":"異常"),1)]),_:2},1032,["type"])]),_:1}),e(i,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[e(f,{type:"primary",size:"small",onClick:E=>te(a)},{default:l(()=>t[20]||(t[20]=[n(" 編輯 ",-1)])),_:2,__:[20]},1032,["onClick"]),e(f,{type:"info",size:"small",onClick:E=>le(a)},{default:l(()=>t[21]||(t[21]=[n(" 數據 ",-1)])),_:2,__:[21]},1032,["onClick"]),e(f,{type:"danger",size:"small",onClick:E=>ae(a)},{default:l(()=>t[22]||(t[22]=[n(" 刪除 ",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,C.value]])]),_:1})])]),_:1}),e(Y,{label:"BTU查詢",name:"calculate"},{default:l(()=>[o("div",Ve,[e(h,{shadow:"never"},{header:l(()=>t[23]||(t[23]=[o("div",{class:"card-header"},[o("span",null,"BTU計算查詢")],-1)])),default:l(()=>[e(P(W),{ref_key:"calculateFormRef",ref:k,model:g,rules:G,"label-width":"120px",class:"calculate-form"},{default:l(()=>[e(x,{gutter:20},{default:l(()=>[e(_,{span:12},{default:l(()=>[e(c,{label:"選擇BTU表",prop:"meterId"},{default:l(()=>[e(w,{modelValue:g.meterId,"onUpdate:modelValue":t[1]||(t[1]=a=>g.meterId=a),placeholder:"請選擇BTU表",style:{width:"100%"},multiple:""},{default:l(()=>[(V(!0),I(_e,null,ge(N.value,a=>(V(),J(u,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:l(()=>[e(c,{label:"查詢期間",prop:"dateRange"},{default:l(()=>[e(L,{modelValue:g.dateRange,"onUpdate:modelValue":t[2]||(t[2]=a=>g.dateRange=a),type:"datetimerange","range-separator":"至","start-placeholder":"開始時間","end-placeholder":"結束時間",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20},{default:l(()=>[e(_,{span:12},{default:l(()=>[e(c,{label:"統計方式"},{default:l(()=>[e(w,{modelValue:g.statisticType,"onUpdate:modelValue":t[3]||(t[3]=a=>g.statisticType=a),style:{width:"100%"}},{default:l(()=>[e(u,{label:"小時統計",value:"hourly"}),e(u,{label:"日統計",value:"daily"}),e(u,{label:"月統計",value:"monthly"}),e(u,{label:"年統計",value:"yearly"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:l(()=>[e(c,{label:"計算方式"},{default:l(()=>[e(w,{modelValue:g.calculationType,"onUpdate:modelValue":t[4]||(t[4]=a=>g.calculationType=a),style:{width:"100%"}},{default:l(()=>[e(u,{label:"累計值",value:"cumulative"}),e(u,{label:"平均值",value:"average"}),e(u,{label:"最大值",value:"maximum"}),e(u,{label:"最小值",value:"minimum"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(c,null,{default:l(()=>[e(f,{type:"primary",loading:M.value,onClick:re},{default:l(()=>[n(m(M.value?"計算中...":"開始計算"),1)]),_:1},8,["loading"]),e(f,{type:"success",disabled:!v.value,onClick:oe},{default:l(()=>t[24]||(t[24]=[n(" 匯出結果 ",-1)])),_:1,__:[24]},8,["disabled"]),e(f,{type:"info",disabled:!v.value,onClick:t[5]||(t[5]=a=>q.value=!0)},{default:l(()=>t[25]||(t[25]=[n(" 圖表顯示 ",-1)])),_:1,__:[25]},8,["disabled"])]),_:1})]),_:1},8,["model"]),v.value?(V(),I("div",Me,[e(h,{shadow:"never"},{header:l(()=>t[26]||(t[26]=[o("span",null,"BTU計算結果",-1)])),default:l(()=>[e(x,{gutter:20,class:"summary-row"},{default:l(()=>[e(_,{span:6},{default:l(()=>[o("div",he,[o("div",xe,m(v.value.totalBTU),1),t[27]||(t[27]=o("div",{class:"result-label"},"總BTU值",-1))])]),_:1}),e(_,{span:6},{default:l(()=>[o("div",De,[o("div",ke,m(v.value.avgBTU),1),t[28]||(t[28]=o("div",{class:"result-label"},"平均BTU值",-1))])]),_:1}),e(_,{span:6},{default:l(()=>[o("div",Ce,[o("div",qe,m(v.value.maxBTU),1),t[29]||(t[29]=o("div",{class:"result-label"},"最大BTU值",-1))])]),_:1}),e(_,{span:6},{default:l(()=>[o("div",Ne,[o("div",Re,m(v.value.minBTU),1),t[30]||(t[30]=o("div",{class:"result-label"},"最小BTU值",-1))])]),_:1})]),_:1}),e(S,{data:v.value.details,stripe:"",border:"",style:{"margin-top":"20px"},"max-height":"400"},{default:l(()=>[e(i,{prop:"meterName",label:"BTU表名稱",width:"150",fixed:"left"}),e(i,{prop:"timestamp",label:"時間",width:"180"},{default:l(({row:a})=>[n(m(K(a.timestamp)),1)]),_:1}),e(i,{prop:"btuValue",label:"BTU值",width:"120",align:"right"},{default:l(({row:a})=>[n(m(Q(a.btuValue)),1)]),_:1}),e(i,{prop:"temperature",label:"溫度(°C)",width:"100",align:"right"},{default:l(({row:a})=>[n(m(a.temperature?.toFixed(2)||"-"),1)]),_:1}),e(i,{prop:"flow",label:"流量",width:"100",align:"right"},{default:l(({row:a})=>[n(m(a.flow?.toFixed(2)||"-"),1)]),_:1}),e(i,{prop:"pressure",label:"壓力",width:"100",align:"right"},{default:l(({row:a})=>[n(m(a.pressure?.toFixed(2)||"-"),1)]),_:1}),e(i,{prop:"efficiency",label:"效率(%)",width:"100",align:"right"},{default:l(({row:a})=>[n(m(a.efficiency?.toFixed(1)||"-"),1)]),_:1}),e(i,{prop:"status",label:"狀態",width:"100"},{default:l(({row:a})=>[e(F,{type:Z(a.status)},{default:l(()=>[n(m(ee(a.status)),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),_:1})])):ve("",!0)]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(z,{modelValue:B.value,"onUpdate:modelValue":t[16]||(t[16]=a=>B.value=a),title:r.id?"編輯BTU表":"新增BTU表",width:"600px"},{footer:l(()=>[e(f,{onClick:t[15]||(t[15]=a=>B.value=!1)},{default:l(()=>t[31]||(t[31]=[n("取消",-1)])),_:1,__:[31]}),e(f,{type:"primary",onClick:se},{default:l(()=>t[32]||(t[32]=[n("確認",-1)])),_:1,__:[32]})]),default:l(()=>[e(P(W),{ref_key:"meterFormRef",ref:y,model:r,rules:A,"label-width":"120px"},{default:l(()=>[e(c,{label:"BTU表名稱",prop:"name"},{default:l(()=>[e(D,{modelValue:r.name,"onUpdate:modelValue":t[7]||(t[7]=a=>r.name=a),placeholder:"請輸入BTU表名稱"},null,8,["modelValue"])]),_:1}),e(c,{label:"表號",prop:"meterNo"},{default:l(()=>[e(D,{modelValue:r.meterNo,"onUpdate:modelValue":t[8]||(t[8]=a=>r.meterNo=a),placeholder:"請輸入表號"},null,8,["modelValue"])]),_:1}),e(c,{label:"安裝位置",prop:"location"},{default:l(()=>[e(D,{modelValue:r.location,"onUpdate:modelValue":t[9]||(t[9]=a=>r.location=a),placeholder:"請輸入安裝位置"},null,8,["modelValue"])]),_:1}),e(c,{label:"表類型",prop:"meterType"},{default:l(()=>[e(w,{modelValue:r.meterType,"onUpdate:modelValue":t[10]||(t[10]=a=>r.meterType=a),style:{width:"100%"}},{default:l(()=>[e(u,{label:"熱量表",value:"heat_meter"}),e(u,{label:"冷量表",value:"cooling_meter"}),e(u,{label:"蒸汽表",value:"steam_meter"}),e(u,{label:"綜合表",value:"combined_meter"})]),_:1},8,["modelValue"])]),_:1}),e(x,{gutter:20},{default:l(()=>[e(_,{span:12},{default:l(()=>[e(c,{label:"單位",prop:"unit"},{default:l(()=>[e(w,{modelValue:r.unit,"onUpdate:modelValue":t[11]||(t[11]=a=>r.unit=a),style:{width:"100%"}},{default:l(()=>[e(u,{label:"BTU",value:"BTU"}),e(u,{label:"kBTU",value:"kBTU"}),e(u,{label:"MBTU",value:"MBTU"}),e(u,{label:"kcal",value:"kcal"}),e(u,{label:"kJ",value:"kJ"}),e(u,{label:"MJ",value:"MJ"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:l(()=>[e(c,{label:"倍率",prop:"multiplier"},{default:l(()=>[e(ne,{modelValue:r.multiplier,"onUpdate:modelValue":t[12]||(t[12]=a=>r.multiplier=a),min:.1,max:1e4,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(c,{label:"安裝日期",prop:"installDate"},{default:l(()=>[e(L,{modelValue:r.installDate,"onUpdate:modelValue":t[13]||(t[13]=a=>r.installDate=a),type:"date",placeholder:"選擇安裝日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(c,{label:"備註"},{default:l(()=>[e(D,{modelValue:r.description,"onUpdate:modelValue":t[14]||(t[14]=a=>r.description=a),type:"textarea",rows:3,placeholder:"請輸入備註"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(z,{modelValue:q.value,"onUpdate:modelValue":t[17]||(t[17]=a=>q.value=a),title:"BTU趨勢圖表",width:"80%"},{default:l(()=>[o("div",Fe,[e(ie,{description:"圖表功能開發中..."})])]),_:1},8,["modelValue"])])}}});const Ye=Te(Ie,[["__scopeId","data-v-c410f6fc"]]);export{Ye as default};
