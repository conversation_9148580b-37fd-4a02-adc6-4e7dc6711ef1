import{a as Qe,r as We,v as Ze,z as le,c as be}from"./index-0d74a956.js";import{u as ea,p as w}from"./dataService-42a3bdf6.js";import{d as aa,r as c,G as <PERSON>,D as he,o as ta,p as v,q as la,c as g,e as C,f as m,g as t,w as s,m as f,h,X as M,Y as R,n as T,l as se,t as b,aH as oe,j as ne,aF as Te,E as _,_ as sa}from"./index-329ed960.js";const oa={class:"group-container"},na={class:"tab-content"},ia={class:"card-header"},ra={class:"filter-section"},da={key:1},ua={class:"pagination-container"},pa={class:"tab-content"},ca={class:"card-header"},ma={class:"tree-node"},fa={class:"node-label"},ga={class:"node-actions"},va={class:"content-setting"},_a={class:"tag-selection-section"},ya={class:"action-buttons"},Ia={key:0,class:"selection-summary"},ba={key:1,class:"selected-tags-list"},Ca={class:"list-header"},ha={class:"list-count"},Ta={key:2,class:"empty-state"},Da={key:0,style:{"margin-top":"15px",padding:"15px","background-color":"#f0f9ff",border:"1px solid #b3d8ff","border-radius":"6px"}},La={style:{"font-weight":"bold","margin-bottom":"12px",color:"#1890ff",display:"flex","align-items":"center",gap:"8px"}},Na={style:{display:"flex","align-items":"center",gap:"8px"}},Va={style:{color:"#606266","font-size":"13px"}},wa=aa({__name:"group",setup(xa){ea();const P=c(),K=c(),De=c(),ie=c("list"),X=c("setting"),Y=c(!1),Le=c(!1),k=c(!1),x=c(!1),z=c(!1),E=c(""),U=c(""),j=c(""),A=c(1),J=c(20),re=c(0),Q=c(!1),F=c([]),G=c(""),H=c(""),B=c("");c("1");const q=c([]),W=c([]),S=c([]),de=c([]),Z=c([]),ue={children:"children",label:"name",value:"id"},n=Ce({id:"",name:"",classId:"",className:"",regionId:"",regionName:"",description:"",tags:[]}),y=Ce({id:"",name:"",parentId:""}),Ne={name:[{required:!0,message:"請輸入群組名稱",trigger:"blur"}],description:[{required:!0,message:"請輸入說明",trigger:"blur"}],classId:[{required:!0,message:"請選擇群組分類",trigger:"change"}],regionId:[{required:!0,message:"請選擇地區",trigger:"change"}]},Ve={name:[{required:!0,message:"請輸入分類名稱",trigger:"blur"}]},we=he(()=>{let l=q.value;return E.value&&(l=l.filter(e=>e.name.toLowerCase().includes(E.value.toLowerCase())||e.description?.toLowerCase().includes(E.value.toLowerCase()))),U.value&&(l=l.filter(e=>e.status===U.value)),j.value&&(l=l.filter(e=>e.classId===j.value)),l}),xe=l=>{const e=[],o=(r,i=0)=>{r.forEach(d=>{const u=d.Id||d.id,I=d.Name||d.name;u&&I&&e.push({id:u,name:I,level:i,displayName:"　".repeat(i)+I}),d.Children&&d.Children.length>0&&o(d.Children,i+1)})};return o(l),e},ke=()=>{},pe=()=>{},Ge=l=>{J.value=l,A.value=1,N()},$e=l=>{A.value=l,N()},Ee=()=>{N()},Se=()=>{n.id=null,n.name="",n.classId="",n.className="",n.regionId="",n.regionName="",n.description="",n.tags=[],k.value=!0,X.value="setting"},Me=l=>{const e=l.classIdList&&l.classIdList.length>0?l.classIdList[l.classIdList.length-1]:l.classId;Object.assign(n,{id:l.id,name:l.name,classId:e,className:l.className,regionId:l.regionId,regionName:l.regionName,description:l.description,tags:l.tags||[]}),k.value=!0},Re=async l=>{try{await Te.confirm(`確定要刪除群組 "${l.name}" 嗎？`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const e={GroupId:l.id},o=await w.post("/Tag/DeleteGroup",e);if(o&&(o.ReturnCode===1||o.success))_.success("群組刪除成功"),await N();else{const r=o?.Message||o?.message||"刪除失敗";throw new Error(r)}}catch(e){if(e!=="cancel"){console.error("刪除群組失敗:",e);let o="刪除群組失敗";e.message?o+=`: ${e.message}`:e.response?.data?.Message?o+=`: ${e.response.data.Message}`:e.response?.data?.message&&(o+=`: ${e.response.data.message}`),_.error(o)}}},ce=async()=>{z.value=!0,await fe()},me=()=>{z.value=!1,G.value="",H.value="",B.value=""},fe=async()=>{try{Q.value=!0;const l=await w.get("/Tag/GetTagList");l&&l.Detail&&l.Detail.TagList?F.value=l.Detail.TagList.map(e=>{const o=e.Id||e.id,r=e.Name||e.name;if(!o||!r)return null;let i="Float";e.DataType&&(typeof e.DataType=="string"?i=e.DataType:typeof e.DataType=="object"&&e.DataType.Name?i=e.DataType.Name:typeof e.DataType=="object"&&e.DataType.value&&(i=e.DataType.value));let d="";e.Address&&(typeof e.Address=="string"?d=e.Address:typeof e.Address=="object"&&e.Address.Name?d=e.Address.Name:typeof e.Address=="object"&&e.Address.value&&(d=e.Address.value));let u="";return e.DeviceId&&(typeof e.DeviceId=="string"?u=e.DeviceId:typeof e.DeviceId=="object"&&e.DeviceId.Name?u=e.DeviceId.Name:typeof e.DeviceId=="object"&&e.DeviceId.value&&(u=e.DeviceId.value)),{id:o,name:r,description:e.Description||e.description||"",tagType:e.TagType||"Analog",dataType:i,address:d,deviceId:u,status:e.Status||"active"}}).filter(e=>e!==null):F.value=[]}catch(l){console.error("載入測點列表失敗:",l),_.error("載入測點列表失敗"),F.value=[]}finally{Q.value=!1}},ge=l=>n.tags.some(e=>{const o=e.Id||e.id,r=l.Id||l.id;return o===r}),ze=he(()=>{let l=F.value;return G.value&&(l=l.filter(e=>e.name.toLowerCase().includes(G.value.toLowerCase())||e.description.toLowerCase().includes(G.value.toLowerCase()))),H.value,B.value,l}),Ue=l=>{n.tags.find(o=>{const r=o.Id||o.id,i=l.Id||l.id;return r===i})||n.tags.push(l)},ve=l=>{const e=n.tags.findIndex(o=>{const r=o.Id||o.id,i=l.Id||l.id;return r===i});e>-1&&n.tags.splice(e,1)},je=()=>{n.tags=[]},Ae=()=>{me(),_.success(`已選擇 ${n.tags.length} 個測點`)},Fe=async()=>{if(P.value)try{if(await P.value.validate(),!n.classId){_.error("請選擇群組分類");return}if(!n.regionId){_.error("請選擇地區");return}if(!S.value.some(i=>i.Id===n.classId||i.id===n.classId)){_.error("選擇的群組分類不存在，請重新選擇");return}if(!Z.value.some(i=>i.Id===n.regionId||i.id===n.regionId)){_.error("選擇的地區不存在，請重新選擇");return}if(n.tags&&n.tags.length>0){const i=n.tags.map(d=>d.id)}const o={GroupName:n.name,GroupCategoryIdList:n.classId?[n.classId]:[],Description:n.description||"",RegionId:n.regionId||"",TagIdList:n.tags?.map(i=>(i.Id||i.id)?.toUpperCase())||[]};let r;if(n.id){const i={GroupId:n.id,...o};r=await w.post("/Tag/UpdateGroup",i)}else r=await w.post("/Tag/CreateNewGroup",o);if(r&&(r.ReturnCode===1||r.success))_.success(n.id?"群組更新成功":"群組新增成功"),k.value=!1,Object.assign(n,{id:null,name:"",classId:"",className:"",regionId:"",regionName:"",description:"",tags:[]}),await N();else{let i=r?.Message||r?.message||"保存失敗";if(r?.Detail){const d=r.Detail,u=[];d.NonExistGroupCategoryIdList&&d.NonExistGroupCategoryIdList.length>0&&u.push(`群組分類ID不存在: ${d.NonExistGroupCategoryIdList.join(", ")}`),d.NonExistTagIdList&&d.NonExistTagIdList.length>0&&u.push(`測點ID不存在: ${d.NonExistTagIdList.join(", ")}`),d.NonExistGroupIdList&&d.NonExistGroupIdList.length>0&&u.push(`群組ID不存在: ${d.NonExistGroupIdList.join(", ")}`),u.length>0&&(i=u.join(`
`))}throw new Error(i)}}catch(l){console.error("保存群組失敗:",l);let e="保存群組失敗";l.message?e+=`: ${l.message}`:l.response?.data?.Message?e+=`: ${l.response.data.Message}`:l.response?.data?.message&&(e+=`: ${l.response.data.message}`),_.error(e)}},N=async()=>{try{Y.value=!0;const l=await w.get("/Tag/GetGroupHierarchyList");l&&l.Detail?(l.Detail.GroupHierarchyList&&(q.value=l.Detail.GroupHierarchyList.map(e=>{let o="",r="",i=[];if(e.GroupSetting?.GroupCategoryDirectLineElderList){const p=e.GroupSetting.GroupCategoryDirectLineElderList;if(p.length>0){const $=p[p.length-1];o=$.Id,r=$.Name,i=p.map(D=>D[D.length-1].Id)}}else o=e.GroupCategoryId||e.CategoryId,r=e.GroupCategoryName||e.CategoryName,i=o?[o]:[];let d="",u="";if(e.GroupSetting?.RegionListDirectLineElderList){const p=e.GroupSetting.RegionListDirectLineElderList;p.length>0&&(d=p[p.length-1].Id,u=p.map(D=>D.Name).join(" > "))}else d=e.RegionId,u=e.RegionName;let I=[];return e.ContentSetting?.TagList?I=e.ContentSetting.TagList:e.Tags&&(I=e.Tags),{id:e.GroupId||e.Id,name:e.GroupName||e.Name,classId:o,className:r,classIdList:i,regionId:d,regionName:u,description:e.Description,tags:I}}),re.value=q.value.length,_.success(`成功載入 ${q.value.length} 個群組`)),l.Detail.RegionHierarchyList&&(de.value=l.Detail.RegionHierarchyList,Z.value=l.Detail.RegionHierarchyList),l.Detail.GroupCategoryHierarchyList&&(W.value=l.Detail.GroupCategoryHierarchyList,S.value=xe(l.Detail.GroupCategoryHierarchyList))):(console.error("API 回應格式不正確:",l),_.error("載入群組資料失敗"))}catch(l){console.error("載入群組列表失敗:",l),_.error("載入群組列表失敗")}finally{Y.value=!1}},He=l=>{Object.assign(y,l),x.value=!0},Be=l=>{y.parentId=l.id,x.value=!0},qe=async l=>{try{await Te.confirm(`確定要刪除分類 "${l.name}" 嗎？`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const e={GroupCategoryId:l.id},o=await w.post("/Tag/DeleteGroupCategory",e);if(o&&(o.ReturnCode===1||o.success))_.success("分類刪除成功"),await N();else{const r=o?.Message||o?.message||"刪除失敗";throw new Error(r)}}catch(e){if(e!=="cancel"){console.error("刪除分類失敗:",e);let o="刪除分類失敗";e.message?o+=`: ${e.message}`:e.response?.data?.Message?o+=`: ${e.response.data.Message}`:e.response?.data?.message&&(o+=`: ${e.response.data.message}`),_.error(o)}}},Oe=async()=>{if(K.value)try{await K.value.validate();const l={GroupCategoryName:y.name,ParentId:y.parentId||"",GroupCategoryId:y.id||""};let e;if(y.id){const o={GroupCategoryId:y.id,...l};e=await w.post("/Tag/UpdateGroupCategory",o)}else e=await w.post("/Tag/CreateNewGroupCategory",l);if(e&&(e.ReturnCode===1||e.success))_.success(y.id?"分類更新成功":"分類新增成功"),x.value=!1,Object.assign(y,{id:"",name:"",parentId:""}),await N();else{const o=e?.Message||e?.message||"保存失敗";throw new Error(o)}}catch(l){console.error("保存分類失敗:",l);let e="保存分類失敗";l.message?e+=`: ${l.message}`:l.response?.data?.Message?e+=`: ${l.response.data.Message}`:l.response?.data?.message&&(e+=`: ${l.response.data.message}`),_.error(e)}};return ta(async()=>{await N(),await fe()}),(l,e)=>{const o=v("el-button"),r=v("el-icon"),i=v("el-input"),d=v("el-col"),u=v("el-option"),I=v("el-select"),_e=v("el-row"),p=v("el-table-column"),$=v("el-tag"),D=v("el-table"),Pe=v("el-pagination"),ee=v("el-card"),O=v("el-tab-pane"),Ke=v("el-tree"),ye=v("el-tabs"),L=v("el-form-item"),Xe=v("el-alert"),Ye=v("el-empty"),ae=v("el-dialog"),Je=v("el-tree-select"),te=la("loading");return g(),C("div",oa,[e[45]||(e[45]=m("div",{class:"page-header"},[m("h2",null,"群組管理"),m("p",null,"管理測點群組，包含群組列表和群組分類")],-1)),t(ee,{class:"group-card"},{default:s(()=>[t(ye,{modelValue:ie.value,"onUpdate:modelValue":e[6]||(e[6]=a=>ie.value=a),type:"border-card"},{default:s(()=>[t(O,{label:"群組列表",name:"list"},{default:s(()=>[m("div",na,[t(ee,{shadow:"never"},{header:s(()=>[m("div",ia,[e[23]||(e[23]=m("span",null,"群組管理",-1)),t(o,{type:"primary",onClick:Se},{default:s(()=>e[22]||(e[22]=[f(" 新增群組 ",-1)])),_:1,__:[22]})])]),default:s(()=>[m("div",ra,[t(_e,{gutter:20},{default:s(()=>[t(d,{span:8},{default:s(()=>[t(i,{modelValue:E.value,"onUpdate:modelValue":e[0]||(e[0]=a=>E.value=a),placeholder:"搜尋群組名稱...",clearable:"",onInput:ke},{prefix:s(()=>[t(r,null,{default:s(()=>[t(h(Qe))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(d,{span:6},{default:s(()=>[t(I,{modelValue:U.value,"onUpdate:modelValue":e[1]||(e[1]=a=>U.value=a),placeholder:"狀態篩選",clearable:"",onChange:pe},{default:s(()=>[t(u,{label:"全部",value:""}),t(u,{label:"啟用",value:"active"}),t(u,{label:"停用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),t(d,{span:6},{default:s(()=>[t(I,{modelValue:j.value,"onUpdate:modelValue":e[2]||(e[2]=a=>j.value=a),placeholder:"分類篩選",clearable:"",onChange:pe},{default:s(()=>[t(u,{label:"全部",value:""}),(g(!0),C(M,null,R(S.value,a=>(g(),T(u,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{span:4},{default:s(()=>[t(o,{type:"info",onClick:Ee},{default:s(()=>[t(r,null,{default:s(()=>[t(h(We))]),_:1}),e[24]||(e[24]=f(" 刷新 ",-1))]),_:1,__:[24]})]),_:1})]),_:1})]),se((g(),T(D,{data:we.value,stripe:"",border:""},{default:s(()=>[t(p,{prop:"name",label:"名稱","min-width":"150"}),t(p,{prop:"regionName",label:"地區",width:"150"},{default:s(({row:a})=>[a.regionName?(g(),T($,{key:0,type:"info"},{default:s(()=>[f(b(a.regionName),1)]),_:2},1024)):(g(),C("span",da,"-"))]),_:1}),t(p,{prop:"description",label:"說明","min-width":"200"}),t(p,{label:"操作",width:"200",fixed:"right"},{default:s(({row:a})=>[t(o,{type:"primary",size:"small",onClick:V=>Me(a)},{default:s(()=>e[25]||(e[25]=[f(" 編輯 ",-1)])),_:2,__:[25]},1032,["onClick"]),t(o,{type:"danger",size:"small",onClick:V=>Re(a)},{default:s(()=>e[26]||(e[26]=[f(" 刪除 ",-1)])),_:2,__:[26]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[te,Y.value]]),m("div",ua,[t(Pe,{"current-page":A.value,"onUpdate:currentPage":e[3]||(e[3]=a=>A.value=a),"page-size":J.value,"onUpdate:pageSize":e[4]||(e[4]=a=>J.value=a),total:re.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ge,onCurrentChange:$e},null,8,["current-page","page-size","total"])])]),_:1})])]),_:1}),t(O,{label:"群組分類",name:"class"},{default:s(()=>[m("div",pa,[t(ee,{shadow:"never"},{header:s(()=>[m("div",ca,[e[28]||(e[28]=m("span",null,"群組分類",-1)),t(o,{type:"primary",onClick:e[5]||(e[5]=a=>x.value=!0)},{default:s(()=>e[27]||(e[27]=[f(" 新增分類 ",-1)])),_:1,__:[27]})])]),default:s(()=>[se((g(),T(Ke,{ref_key:"classTreeRef",ref:De,data:W.value,props:ue,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},{default:s(({node:a,data:V})=>[m("div",ma,[m("span",fa,b(a.label),1),m("div",ga,[t(o,{type:"primary",size:"small",onClick:Ie=>He(V)},{default:s(()=>e[29]||(e[29]=[f(" 編輯 ",-1)])),_:2,__:[29]},1032,["onClick"]),t(o,{type:"success",size:"small",onClick:Ie=>Be(V)},{default:s(()=>e[30]||(e[30]=[f(" 新增子分類 ",-1)])),_:2,__:[30]},1032,["onClick"]),t(o,{type:"danger",size:"small",onClick:Ie=>qe(V)},{default:s(()=>e[31]||(e[31]=[f(" 刪除 ",-1)])),_:2,__:[31]},1032,["onClick"])])])]),_:1},8,["data"])),[[te,Le.value]])]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(ae,{modelValue:k.value,"onUpdate:modelValue":e[13]||(e[13]=a=>k.value=a),title:n.id?"編輯群組":"新增群組",width:"1200px"},{footer:s(()=>[t(o,{onClick:e[12]||(e[12]=a=>k.value=!1)},{default:s(()=>e[38]||(e[38]=[f("取消",-1)])),_:1,__:[38]}),t(o,{type:"primary",onClick:Fe},{default:s(()=>e[39]||(e[39]=[f("確認",-1)])),_:1,__:[39]})]),default:s(()=>[t(ye,{modelValue:X.value,"onUpdate:modelValue":e[11]||(e[11]=a=>X.value=a),type:"border-card"},{default:s(()=>[t(O,{label:"群組設定",name:"setting"},{default:s(()=>[t(h(oe),{ref_key:"groupFormRef",ref:P,model:n,rules:Ne,"label-width":"120px"},{default:s(()=>[t(L,{label:"群組名稱",prop:"name"},{default:s(()=>[t(i,{modelValue:n.name,"onUpdate:modelValue":e[7]||(e[7]=a=>n.name=a),placeholder:"請輸入群組名稱"},null,8,["modelValue"])]),_:1}),t(L,{label:"群組分類",prop:"classId"},{default:s(()=>[t(I,{modelValue:n.classId,"onUpdate:modelValue":e[8]||(e[8]=a=>n.classId=a),placeholder:"請選擇群組分類",clearable:"",style:{width:"100%"}},{default:s(()=>[(g(!0),C(M,null,R(S.value,a=>(g(),T(u,{key:a.id,label:a.displayName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(L,{label:"地區",prop:"regionId"},{default:s(()=>[t(I,{modelValue:n.regionId,"onUpdate:modelValue":e[9]||(e[9]=a=>n.regionId=a),placeholder:"請選擇地區",clearable:"",style:{width:"100%"}},{default:s(()=>[(g(!0),C(M,null,R(Z.value,a=>(g(),T(u,{key:a.Id||a.id,label:a.Name||a.name,value:a.Id||a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(L,{label:"說明"},{default:s(()=>[t(i,{modelValue:n.description,"onUpdate:modelValue":e[10]||(e[10]=a=>n.description=a),type:"textarea",rows:3,placeholder:"請輸入群組說明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),t(O,{label:"內容設定",name:"content"},{default:s(()=>[m("div",va,[m("div",_a,[e[37]||(e[37]=m("div",{class:"section-header"},[m("h4",null,"📊 測點選擇"),m("p",{class:"section-description"},"為此群組選擇相關的測點")],-1)),m("div",ya,[t(o,{type:"primary",size:"large",onClick:ce,icon:h(Ze)},{default:s(()=>e[32]||(e[32]=[f(" 選擇測點 ",-1)])),_:1,__:[32]},8,["icon"]),n.tags.length>0?(g(),T(o,{key:0,type:"danger",size:"large",onClick:je,icon:h(le)},{default:s(()=>e[33]||(e[33]=[f(" 清除全部測點 ",-1)])),_:1,__:[33]},8,["icon"])):ne("",!0)]),n.tags.length>0?(g(),C("div",Ia,[t(Xe,{title:`已選擇 ${n.tags.length} 個測點`,type:"success",closable:!1,"show-icon":""},null,8,["title"])])):ne("",!0),n.tags.length>0?(g(),C("div",ba,[m("div",Ca,[e[34]||(e[34]=m("h5",null,"📋 已選測點列表",-1)),m("span",ha,"共 "+b(n.tags.length)+" 個測點",1)]),t(D,{data:n.tags,height:"350",stripe:"",border:"",style:{width:"100%"}},{default:s(()=>[t(p,{label:"測點名稱","min-width":"200"},{default:s(({row:a})=>[f(b(a.Name||a.name||a.SimpleName||a.simpleName||"未知"),1)]),_:1}),t(p,{label:"描述","min-width":"250"},{default:s(({row:a})=>[f(b(a.Description||a.description||a.SimpleName||a.simpleName||""),1)]),_:1}),t(p,{label:"類型",width:"100"},{default:s(({row:a})=>[f(b(a.TagType||a.tagType||a.Type||a.type||""),1)]),_:1}),t(p,{label:"資料類型",width:"120"},{default:s(({row:a})=>[f(b(a.DataType||a.dataType||a.ValueType||a.valueType||""),1)]),_:1}),t(p,{label:"操作",width:"100",fixed:"right"},{default:s(({row:a})=>[t(o,{type:"danger",size:"small",onClick:V=>ve(a),icon:h(le)},{default:s(()=>e[35]||(e[35]=[f(" 移除 ",-1)])),_:2,__:[35]},1032,["onClick","icon"])]),_:1})]),_:1},8,["data"])])):(g(),C("div",Ta,[t(Ye,{description:"尚未選擇任何測點","image-size":100},{default:s(()=>[t(o,{type:"primary",onClick:ce},{default:s(()=>e[36]||(e[36]=[f(" 開始選擇測點 ",-1)])),_:1,__:[36]})]),_:1})]))])])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"]),t(ae,{modelValue:x.value,"onUpdate:modelValue":e[17]||(e[17]=a=>x.value=a),title:y.id?"編輯分類":"新增分類",width:"500px"},{footer:s(()=>[t(o,{onClick:e[16]||(e[16]=a=>x.value=!1)},{default:s(()=>e[40]||(e[40]=[f("取消",-1)])),_:1,__:[40]}),t(o,{type:"primary",onClick:Oe},{default:s(()=>e[41]||(e[41]=[f("確認",-1)])),_:1,__:[41]})]),default:s(()=>[t(h(oe),{ref_key:"classFormRef",ref:K,model:y,rules:Ve,"label-width":"120px"},{default:s(()=>[t(L,{label:"分類名稱",prop:"name"},{default:s(()=>[t(i,{modelValue:y.name,"onUpdate:modelValue":e[14]||(e[14]=a=>y.name=a),placeholder:"請輸入分類名稱"},null,8,["modelValue"])]),_:1}),t(L,{label:"上級分類",prop:"parentId"},{default:s(()=>[t(Je,{modelValue:y.parentId,"onUpdate:modelValue":e[15]||(e[15]=a=>y.parentId=a),data:W.value,props:ue,placeholder:"請選擇上級分類（可選）",clearable:"",style:{width:"100%"}},null,8,["modelValue","data"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(ae,{modelValue:z.value,"onUpdate:modelValue":e[21]||(e[21]=a=>z.value=a),title:"選擇測點",width:"1400px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[t(o,{onClick:me},{default:s(()=>e[43]||(e[43]=[f("取消",-1)])),_:1,__:[43]}),t(o,{type:"primary",onClick:Ae},{default:s(()=>e[44]||(e[44]=[f("確認",-1)])),_:1,__:[44]})]),default:s(()=>[t(h(oe),{inline:!0},{default:s(()=>[t(L,{label:"搜尋"},{default:s(()=>[t(i,{modelValue:G.value,"onUpdate:modelValue":e[18]||(e[18]=a=>G.value=a),placeholder:"請輸入測點名稱或描述",clearable:""},null,8,["modelValue"])]),_:1}),t(L,{label:"地區"},{default:s(()=>[t(I,{modelValue:H.value,"onUpdate:modelValue":e[19]||(e[19]=a=>H.value=a),placeholder:"請選擇地區",clearable:""},{default:s(()=>[t(u,{label:"全部",value:""}),(g(!0),C(M,null,R(de.value,a=>(g(),T(u,{key:a.Id||a.id,label:a.Name||a.name,value:a.Id||a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(L,{label:"分類"},{default:s(()=>[t(I,{modelValue:B.value,"onUpdate:modelValue":e[20]||(e[20]=a=>B.value=a),placeholder:"請選擇分類",clearable:""},{default:s(()=>[t(u,{label:"全部",value:""}),(g(!0),C(M,null,R(S.value,a=>(g(),T(u,{key:a.id,label:a.displayName||a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),se((g(),T(D,{data:ze.value,stripe:"",border:"",style:{width:"100%","margin-top":"20px"},height:"400"},{default:s(()=>[t(p,{prop:"name",label:"測點名稱","min-width":"200"}),t(p,{prop:"description",label:"描述","min-width":"250"}),t(p,{prop:"tagType",label:"類型",width:"100"}),t(p,{prop:"dataType",label:"資料類型",width:"100"}),t(p,{prop:"status",label:"狀態",width:"100"},{default:s(({row:a})=>[t($,{type:a.status==="active"?"success":"danger"},{default:s(()=>[f(b(a.status),1)]),_:2},1032,["type"])]),_:1}),t(p,{label:"操作",width:"120",fixed:"right"},{default:s(({row:a})=>[t(o,{type:ge(a)?"success":"primary",size:"small",onClick:V=>Ue(a)},{default:s(()=>[f(b(ge(a)?"已選擇":"選擇"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[te,Q.value]]),n.tags.length>0?(g(),C("div",Da,[m("div",La,[t(r,null,{default:s(()=>[t(h(be))]),_:1}),f(" 已選擇 "+b(n.tags.length)+" 個測點： ",1)]),t(D,{data:n.tags,size:"small",style:{width:"100%"},"show-header":!1,border:!1},{default:s(()=>[t(p,{prop:"name","min-width":"300"},{default:s(({row:a})=>[m("div",Na,[t(r,{color:"#67c23a"},{default:s(()=>[t(h(be))]),_:1}),m("span",null,b(a.name),1)])]),_:1}),t(p,{prop:"description","min-width":"200"},{default:s(({row:a})=>[m("span",Va,b(a.description),1)]),_:1}),t(p,{label:"操作",width:"80",fixed:"right"},{default:s(({row:a})=>[t(o,{type:"danger",size:"small",onClick:V=>ve(a),icon:h(le)},{default:s(()=>e[42]||(e[42]=[f(" 移除 ",-1)])),_:2,__:[42]},1032,["onClick","icon"])]),_:1})]),_:1},8,["data"])])):ne("",!0)]),_:1},8,["modelValue"])])}}});const Ea=sa(wa,[["__scopeId","data-v-715a535f"]]);export{Ea as default};
