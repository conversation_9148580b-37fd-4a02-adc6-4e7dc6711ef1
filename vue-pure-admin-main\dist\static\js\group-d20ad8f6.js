import{v as F,a as fe,r as L,n as ge,y as ve,x as be,z as P}from"./index-0d74a956.js";import{d as he,r as h,G as T,o as ye,p as d,q as Ve,c as x,e as N,f as n,g as e,w as l,h as _,m as r,l as we,n as Ce,t as D,X as Me,Y as Se,E as f,aF as ke,_ as Te}from"./index-329ed960.js";const xe={class:"group-container"},De={class:"page-header"},ze={class:"header-right"},Ee={class:"card-header"},Ye={class:"header-actions"},Ue={class:"group-name"},Ne={class:"pagination-container"},Ie={class:"members-container"},$e={class:"members-header"},qe={class:"members-list"},He={class:"member-info"},Re={class:"member-name"},Be={class:"member-contact"},Fe={class:"dialog-footer"},<PERSON>={class:"dialog-footer"},Pe=he({__name:"group",setup(je){const z=h(!1),w=h(!1),C=h(!1),S=h("新增群組"),j=h([]),g=T({name:"",method:"",status:""}),u=T({currentPage:1,pageSize:10,total:0}),M=h([]),E=h(),I=h(),s=T({id:"",name:"",method:"",startTime:"",endTime:"",duration:1,members:[]}),y=T({name:"",contact:""}),G={name:[{required:!0,message:"請輸入群組名稱",trigger:"blur"}],method:[{required:!0,message:"請選擇通知方式",trigger:"change"}],startTime:[{required:!0,message:"請選擇開始時間",trigger:"change"}],endTime:[{required:!0,message:"請選擇結束時間",trigger:"change"}],duration:[{required:!0,message:"請輸入持續時間",trigger:"blur"}]},A={name:[{required:!0,message:"請輸入姓名",trigger:"blur"}],contact:[{required:!0,message:"請輸入聯絡方式",trigger:"blur"}]},O=o=>({SMS:"warning",LINE:"success",Email:"info"})[o]||"info",$=()=>{u.currentPage=1,V()},X=()=>{Object.assign(g,{name:"",method:"",status:""}),$()},J=()=>{V()},K=o=>{j.value=o},Q=async o=>{try{f.success("狀態更新成功")}catch{f.error("狀態更新失敗"),o.status=o.status==="active"?"inactive":"active"}},W=()=>{S.value="新增群組",q(),w.value=!0},Z=o=>{S.value="編輯群組",Object.assign(s,{...o}),w.value=!0},ee=o=>{f.info("查看成員功能開發中...")},te=async o=>{try{await ke.confirm(`確定要刪除群組 "${o.name}" 嗎？`,"確認刪除",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),f.success("刪除成功"),V()}catch{}},le=o=>{u.pageSize=o,V()},ae=o=>{u.currentPage=o,V()},oe=()=>{q()},q=()=>{Object.assign(s,{id:"",name:"",method:"",startTime:"",endTime:"",duration:1,members:[]}),E.value?.clearValidate()},ne=()=>{Object.assign(y,{name:"",contact:""}),C.value=!0},se=async()=>{try{await I.value?.validate(),s.members.push({...y}),C.value=!1,f.success("成員新增成功")}catch{}},de=o=>{s.members.splice(o,1)},re=async()=>{try{await E.value?.validate(),f.success(S.value==="新增群組"?"新增成功":"更新成功"),w.value=!1,V()}catch{}},V=async()=>{z.value=!0;try{const o=await plcDataService.get("/api/Message/GetMessageGroupDetailList",{pageIndex:u.currentPage,pageSize:u.pageSize});o&&o.data?(M.value=o.data.map(t=>({id:t.GroupId||t.Id,name:t.GroupName||t.Name,method:t.NotifyMethod||t.Method,memberCount:t.MemberCount||0,startTime:t.StartTime,endTime:t.EndTime,status:t.IsActive?"active":"inactive"})),u.total=o.total||M.value.length,f.success(`成功載入 ${M.value.length} 個通知群組`)):(M.value=[],u.total=0,f.warning("未找到通知群組數據"))}catch{f.error("載入數據失敗")}finally{z.value=!1}};return ye(()=>{V()}),(o,t)=>{const p=d("el-icon"),i=d("el-button"),k=d("el-input"),m=d("el-form-item"),c=d("el-option"),Y=d("el-select"),U=d("el-form"),H=d("el-card"),v=d("el-table-column"),ie=d("el-tag"),ue=d("el-switch"),me=d("el-table"),pe=d("el-pagination"),R=d("el-date-picker"),ce=d("el-input-number"),B=d("el-dialog"),_e=Ve("loading");return x(),N("div",xe,[n("div",De,[t[17]||(t[17]=n("div",{class:"header-left"},[n("h1",null,"群組管理"),n("p",null,"管理通知群組、成員設定和群組權限")],-1)),n("div",ze,[e(i,{type:"primary",onClick:W},{default:l(()=>[e(p,null,{default:l(()=>[e(_(F))]),_:1}),t[16]||(t[16]=r(" 新增群組 ",-1))]),_:1,__:[16]})])]),e(H,{class:"search-card",shadow:"never"},{default:l(()=>[e(U,{model:g,inline:""},{default:l(()=>[e(m,{label:"群組名稱"},{default:l(()=>[e(k,{modelValue:g.name,"onUpdate:modelValue":t[0]||(t[0]=a=>g.name=a),placeholder:"請輸入群組名稱",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(m,{label:"通知方式"},{default:l(()=>[e(Y,{modelValue:g.method,"onUpdate:modelValue":t[1]||(t[1]=a=>g.method=a),placeholder:"請選擇通知方式",clearable:"",style:{width:"150px"}},{default:l(()=>[e(c,{label:"全部",value:""}),e(c,{label:"SMS",value:"SMS"}),e(c,{label:"LINE",value:"LINE"}),e(c,{label:"Email",value:"Email"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"狀態"},{default:l(()=>[e(Y,{modelValue:g.status,"onUpdate:modelValue":t[2]||(t[2]=a=>g.status=a),placeholder:"請選擇狀態",clearable:"",style:{width:"120px"}},{default:l(()=>[e(c,{label:"全部",value:""}),e(c,{label:"啟用",value:"active"}),e(c,{label:"停用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(i,{type:"primary",onClick:$},{default:l(()=>[e(p,null,{default:l(()=>[e(_(fe))]),_:1}),t[18]||(t[18]=r(" 搜尋 ",-1))]),_:1,__:[18]}),e(i,{onClick:X},{default:l(()=>[e(p,null,{default:l(()=>[e(_(L))]),_:1}),t[19]||(t[19]=r(" 重置 ",-1))]),_:1,__:[19]})]),_:1})]),_:1},8,["model"])]),_:1}),e(H,{class:"table-card",shadow:"never"},{header:l(()=>[n("div",Ee,[t[21]||(t[21]=n("span",null,"群組列表",-1)),n("div",Ye,[e(i,{size:"small",onClick:J},{default:l(()=>[e(p,null,{default:l(()=>[e(_(L))]),_:1}),t[20]||(t[20]=r(" 重新整理 ",-1))]),_:1,__:[20]})])])]),default:l(()=>[we((x(),Ce(me,{data:M.value,stripe:"",style:{width:"100%"},onSelectionChange:K},{default:l(()=>[e(v,{type:"selection",width:"55"}),e(v,{prop:"name",label:"群組名稱","min-width":"150"},{default:l(({row:a})=>[n("div",Ue,[e(p,{class:"group-icon"},{default:l(()=>[e(_(ge))]),_:1}),r(" "+D(a.name),1)])]),_:1}),e(v,{prop:"method",label:"通知方式",width:"120"},{default:l(({row:a})=>[e(ie,{type:O(a.method)},{default:l(()=>[r(D(a.method),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"memberCount",label:"成員數量",width:"100",align:"center"}),e(v,{prop:"startTime",label:"開始時間",width:"180"}),e(v,{prop:"endTime",label:"結束時間",width:"180"}),e(v,{prop:"status",label:"狀態",width:"100",align:"center"},{default:l(({row:a})=>[e(ue,{modelValue:a.status,"onUpdate:modelValue":b=>a.status=b,"active-value":"active","inactive-value":"inactive",onChange:b=>Q(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(v,{label:"操作",width:"200",fixed:"right"},{default:l(({row:a})=>[e(i,{size:"small",type:"primary",onClick:b=>Z(a)},{default:l(()=>[e(p,null,{default:l(()=>[e(_(ve))]),_:1}),t[22]||(t[22]=r(" 編輯 ",-1))]),_:2,__:[22]},1032,["onClick"]),e(i,{size:"small",type:"info",onClick:b=>ee(a)},{default:l(()=>[e(p,null,{default:l(()=>[e(_(be))]),_:1}),t[23]||(t[23]=r(" 成員 ",-1))]),_:2,__:[23]},1032,["onClick"]),e(i,{size:"small",type:"danger",onClick:b=>te(a)},{default:l(()=>[e(p,null,{default:l(()=>[e(_(P))]),_:1}),t[24]||(t[24]=r(" 刪除 ",-1))]),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[_e,z.value]]),n("div",Ne,[e(pe,{"current-page":u.currentPage,"onUpdate:currentPage":t[3]||(t[3]=a=>u.currentPage=a),"page-size":u.pageSize,"onUpdate:pageSize":t[4]||(t[4]=a=>u.pageSize=a),total:u.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:le,onCurrentChange:ae},null,8,["current-page","page-size","total"])])]),_:1}),e(B,{modelValue:w.value,"onUpdate:modelValue":t[11]||(t[11]=a=>w.value=a),title:S.value,width:"600px",onClose:oe},{footer:l(()=>[n("div",Fe,[e(i,{onClick:t[10]||(t[10]=a=>w.value=!1)},{default:l(()=>t[27]||(t[27]=[r("取消",-1)])),_:1,__:[27]}),e(i,{type:"primary",onClick:re},{default:l(()=>t[28]||(t[28]=[r("確定",-1)])),_:1,__:[28]})])]),default:l(()=>[e(U,{ref_key:"formRef",ref:E,model:s,rules:G,"label-width":"100px"},{default:l(()=>[e(m,{label:"群組名稱",prop:"name"},{default:l(()=>[e(k,{modelValue:s.name,"onUpdate:modelValue":t[5]||(t[5]=a=>s.name=a),placeholder:"請輸入群組名稱",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),e(m,{label:"通知方式",prop:"method"},{default:l(()=>[e(Y,{modelValue:s.method,"onUpdate:modelValue":t[6]||(t[6]=a=>s.method=a),placeholder:"請選擇通知方式",style:{width:"100%"}},{default:l(()=>[e(c,{label:"SMS",value:"SMS"}),e(c,{label:"LINE",value:"LINE"}),e(c,{label:"Email",value:"Email"})]),_:1},8,["modelValue"])]),_:1}),e(m,{label:"開始時間",prop:"startTime"},{default:l(()=>[e(R,{modelValue:s.startTime,"onUpdate:modelValue":t[7]||(t[7]=a=>s.startTime=a),type:"datetime",placeholder:"請選擇開始時間",style:{width:"100%"},format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),e(m,{label:"結束時間",prop:"endTime"},{default:l(()=>[e(R,{modelValue:s.endTime,"onUpdate:modelValue":t[8]||(t[8]=a=>s.endTime=a),type:"datetime",placeholder:"請選擇結束時間",style:{width:"100%"},format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),e(m,{label:"持續時間",prop:"duration"},{default:l(()=>[e(ce,{modelValue:s.duration,"onUpdate:modelValue":t[9]||(t[9]=a=>s.duration=a),min:1,max:24,placeholder:"小時",style:{width:"100%"}},null,8,["modelValue"]),t[25]||(t[25]=n("span",{style:{"margin-left":"8px",color:"#909399"}},"小時",-1))]),_:1,__:[25]}),e(m,{label:"群組成員",prop:"members"},{default:l(()=>[n("div",Ie,[n("div",$e,[e(i,{size:"small",type:"primary",onClick:ne},{default:l(()=>[e(p,null,{default:l(()=>[e(_(F))]),_:1}),t[26]||(t[26]=r(" 新增成員 ",-1))]),_:1,__:[26]})]),n("div",qe,[(x(!0),N(Me,null,Se(s.members,(a,b)=>(x(),N("div",{key:b,class:"member-item"},[n("div",He,[n("span",Re,D(a.name),1),n("span",Be,D(a.contact),1)]),e(i,{size:"small",type:"danger",text:"",onClick:Ge=>de(b)},{default:l(()=>[e(p,null,{default:l(()=>[e(_(P))]),_:1})]),_:2},1032,["onClick"])]))),128))])])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(B,{modelValue:C.value,"onUpdate:modelValue":t[15]||(t[15]=a=>C.value=a),title:"新增成員",width:"400px"},{footer:l(()=>[n("div",Le,[e(i,{onClick:t[14]||(t[14]=a=>C.value=!1)},{default:l(()=>t[29]||(t[29]=[r("取消",-1)])),_:1,__:[29]}),e(i,{type:"primary",onClick:se},{default:l(()=>t[30]||(t[30]=[r("確定",-1)])),_:1,__:[30]})])]),default:l(()=>[e(U,{ref_key:"memberFormRef",ref:I,model:y,rules:A,"label-width":"80px"},{default:l(()=>[e(m,{label:"姓名",prop:"name"},{default:l(()=>[e(k,{modelValue:y.name,"onUpdate:modelValue":t[12]||(t[12]=a=>y.name=a),placeholder:"請輸入姓名"},null,8,["modelValue"])]),_:1}),e(m,{label:"聯絡方式",prop:"contact"},{default:l(()=>[e(k,{modelValue:y.contact,"onUpdate:modelValue":t[13]||(t[13]=a=>y.contact=a),placeholder:"請輸入電話或Email"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const Xe=Te(Pe,[["__scopeId","data-v-44d17957"]]);export{Xe as default};
