import{u as ee}from"./dataService-42a3bdf6.js";import{d as le,r as _,G as M,p as u,q as te,c as w,e as N,f as i,g as e,w as t,m as o,l as E,n as $,t as p,h as Y,aH as z,X as ae,Y as ie,j as re,_ as oe}from"./index-329ed960.js";const de={class:"waterbill-container"},ne={class:"tab-content"},se={class:"card-header"},ue={class:"tab-content"},pe={class:"card-header"},me={class:"tab-content"},fe={key:0,class:"bill-result"},ge={class:"result-item"},_e={class:"result-value"},be={class:"result-item"},ve={class:"result-value"},ye={class:"result-item"},Ve={class:"result-value"},we={class:"result-item"},Re={class:"result-value"},ke=le({__name:"waterbill",setup(Ce){ee();const j=_(),G=_(),H=_(),B=_("meter"),P=_(!1),X=_(!1),I=_(!1),D=_(!1),F=_(!1),h=_([]),J=_([]),s=M({id:"",name:"",meterNo:"",location:"",meterType:"",caliber:20,multiplier:1,installDate:"",description:"",status:"active"}),d=M({id:"",name:"",rateType:"",tier1Rate:0,tier1Limit:0,tier2Rate:0,tier2Limit:0,tier3Rate:0,basicFee:0,effectiveDate:"",status:"active"}),R=M({meterId:"",dateRange:[]}),y=_(null),K={name:[{required:!0,message:"請輸入水表名稱",trigger:"blur"}],meterNo:[{required:!0,message:"請輸入水表編號",trigger:"blur"}],location:[{required:!0,message:"請輸入安裝位置",trigger:"blur"}],meterType:[{required:!0,message:"請選擇水表類型",trigger:"change"}],caliber:[{required:!0,message:"請輸入口徑",trigger:"blur"}],multiplier:[{required:!0,message:"請輸入倍率",trigger:"blur"}],installDate:[{required:!0,message:"請選擇安裝日期",trigger:"change"}]},O={name:[{required:!0,message:"請輸入費率名稱",trigger:"blur"}],rateType:[{required:!0,message:"請選擇費率類型",trigger:"change"}],tier1Rate:[{required:!0,message:"請輸入第一階水價",trigger:"blur"}],tier1Limit:[{required:!0,message:"請輸入第一階上限",trigger:"blur"}],effectiveDate:[{required:!0,message:"請選擇生效日期",trigger:"change"}]},Q={meterId:[{required:!0,message:"請選擇水表",trigger:"change"}],dateRange:[{required:!0,message:"請選擇計算期間",trigger:"change"}]};return(m,l)=>{const f=u("el-button"),r=u("el-table-column"),T=u("el-tag"),x=u("el-table"),k=u("el-card"),L=u("el-tab-pane"),b=u("el-option"),q=u("el-select"),n=u("el-form-item"),g=u("el-col"),c=u("el-date-picker"),V=u("el-row"),W=u("el-tabs"),C=u("el-input"),v=u("el-input-number"),S=u("el-dialog"),Z=u("el-divider"),A=te("loading");return w(),N("div",de,[l[54]||(l[54]=i("div",{class:"page-header"},[i("h2",null,"水費計算"),i("p",null,"水費計算與管理，包含水表管理、水價設定、費用查詢等功能")],-1)),e(k,{class:"waterbill-card"},{default:t(()=>[e(W,{modelValue:B.value,"onUpdate:modelValue":l[4]||(l[4]=a=>B.value=a),type:"border-card"},{default:t(()=>[e(L,{label:"水表管理",name:"meter"},{default:t(()=>[i("div",ne,[e(k,{shadow:"never"},{header:t(()=>[i("div",se,[l[27]||(l[27]=i("span",null,"水表設定",-1)),e(f,{type:"primary",onClick:l[0]||(l[0]=a=>D.value=!0)},{default:t(()=>l[26]||(l[26]=[o(" 新增水表 ",-1)])),_:1,__:[26]})])]),default:t(()=>[E((w(),$(x,{data:h.value,stripe:"",border:""},{default:t(()=>[e(r,{prop:"name",label:"水表名稱",width:"200"}),e(r,{prop:"meterNo",label:"水表編號",width:"150"}),e(r,{prop:"location",label:"安裝位置",width:"200"}),e(r,{prop:"meterType",label:"水表類型",width:"120"},{default:t(({row:a})=>[e(T,null,{default:t(()=>[o(p(m.getMeterTypeText(a.meterType)),1)]),_:2},1024)]),_:1}),e(r,{prop:"caliber",label:"口徑(mm)",width:"100",align:"right"}),e(r,{prop:"multiplier",label:"倍率",width:"100",align:"right"}),e(r,{prop:"installDate",label:"安裝日期",width:"120"},{default:t(({row:a})=>[o(p(m.formatDate(a.installDate)),1)]),_:1}),e(r,{prop:"status",label:"狀態",width:"100"},{default:t(({row:a})=>[e(T,{type:a.status==="active"?"success":"danger"},{default:t(()=>[o(p(a.status==="active"?"正常":"異常"),1)]),_:2},1032,["type"])]),_:1}),e(r,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[e(f,{type:"primary",size:"small",onClick:U=>m.editMeter(a)},{default:t(()=>l[28]||(l[28]=[o(" 編輯 ",-1)])),_:2,__:[28]},1032,["onClick"]),e(f,{type:"info",size:"small",onClick:U=>m.viewMeterData(a)},{default:t(()=>l[29]||(l[29]=[o(" 數據 ",-1)])),_:2,__:[29]},1032,["onClick"]),e(f,{type:"danger",size:"small",onClick:U=>m.deleteMeter(a)},{default:t(()=>l[30]||(l[30]=[o(" 刪除 ",-1)])),_:2,__:[30]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,P.value]])]),_:1})])]),_:1}),e(L,{label:"水價設定",name:"rate"},{default:t(()=>[i("div",ue,[e(k,{shadow:"never"},{header:t(()=>[i("div",pe,[l[32]||(l[32]=i("span",null,"水價費率設定",-1)),e(f,{type:"primary",onClick:l[1]||(l[1]=a=>F.value=!0)},{default:t(()=>l[31]||(l[31]=[o(" 新增費率 ",-1)])),_:1,__:[31]})])]),default:t(()=>[E((w(),$(x,{data:J.value,stripe:"",border:""},{default:t(()=>[e(r,{prop:"name",label:"費率名稱",width:"200"}),e(r,{prop:"rateType",label:"費率類型",width:"150"},{default:t(({row:a})=>[e(T,null,{default:t(()=>[o(p(m.getRateTypeText(a.rateType)),1)]),_:2},1024)]),_:1}),e(r,{prop:"tier1Rate",label:"第一階(元/度)",width:"120",align:"right"},{default:t(({row:a})=>[o(p(a.tier1Rate?.toFixed(2)||"-"),1)]),_:1}),e(r,{prop:"tier1Limit",label:"第一階上限",width:"120",align:"right"}),e(r,{prop:"tier2Rate",label:"第二階(元/度)",width:"120",align:"right"},{default:t(({row:a})=>[o(p(a.tier2Rate?.toFixed(2)||"-"),1)]),_:1}),e(r,{prop:"tier2Limit",label:"第二階上限",width:"120",align:"right"}),e(r,{prop:"tier3Rate",label:"第三階(元/度)",width:"120",align:"right"},{default:t(({row:a})=>[o(p(a.tier3Rate?.toFixed(2)||"-"),1)]),_:1}),e(r,{prop:"basicFee",label:"基本費(元)",width:"100",align:"right"},{default:t(({row:a})=>[o(p(a.basicFee?.toFixed(0)||"-"),1)]),_:1}),e(r,{prop:"effectiveDate",label:"生效日期",width:"120"},{default:t(({row:a})=>[o(p(m.formatDate(a.effectiveDate)),1)]),_:1}),e(r,{prop:"status",label:"狀態",width:"100"},{default:t(({row:a})=>[e(T,{type:a.status==="active"?"success":"info"},{default:t(()=>[o(p(a.status==="active"?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),e(r,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[e(f,{type:"primary",size:"small",onClick:U=>m.editRate(a)},{default:t(()=>l[33]||(l[33]=[o(" 編輯 ",-1)])),_:2,__:[33]},1032,["onClick"]),e(f,{type:"warning",size:"small",onClick:U=>m.toggleRateStatus(a)},{default:t(()=>[o(p(a.status==="active"?"停用":"啟用"),1)]),_:2},1032,["onClick"]),e(f,{type:"danger",size:"small",onClick:U=>m.deleteRate(a)},{default:t(()=>l[34]||(l[34]=[o(" 刪除 ",-1)])),_:2,__:[34]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,X.value]])]),_:1})])]),_:1}),e(L,{label:"費用查詢",name:"calculate"},{default:t(()=>[i("div",me,[e(k,{shadow:"never"},{header:t(()=>l[35]||(l[35]=[i("div",{class:"card-header"},[i("span",null,"水費計算查詢")],-1)])),default:t(()=>[e(Y(z),{ref_key:"calculateFormRef",ref:H,model:R,rules:Q,"label-width":"120px",class:"calculate-form"},{default:t(()=>[e(V,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(n,{label:"選擇水表",prop:"meterId"},{default:t(()=>[e(q,{modelValue:R.meterId,"onUpdate:modelValue":l[2]||(l[2]=a=>R.meterId=a),placeholder:"請選擇水表",style:{width:"100%"}},{default:t(()=>[(w(!0),N(ae,null,ie(h.value,a=>(w(),$(b,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(n,{label:"計算期間",prop:"dateRange"},{default:t(()=>[e(c,{modelValue:R.dateRange,"onUpdate:modelValue":l[3]||(l[3]=a=>R.dateRange=a),type:"monthrange","range-separator":"至","start-placeholder":"開始月份","end-placeholder":"結束月份",format:"YYYY-MM","value-format":"YYYY-MM",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(n,null,{default:t(()=>[e(f,{type:"primary",loading:I.value,onClick:m.calculateBill},{default:t(()=>[o(p(I.value?"計算中...":"開始計算"),1)]),_:1},8,["loading","onClick"]),e(f,{type:"success",disabled:!y.value,onClick:m.exportBillResult},{default:t(()=>l[36]||(l[36]=[o(" 匯出結果 ",-1)])),_:1,__:[36]},8,["disabled","onClick"])]),_:1})]),_:1},8,["model"]),y.value?(w(),N("div",fe,[e(k,{shadow:"never"},{header:t(()=>l[37]||(l[37]=[i("span",null,"計算結果",-1)])),default:t(()=>[e(V,{gutter:20},{default:t(()=>[e(g,{span:6},{default:t(()=>[i("div",ge,[i("div",_e,p(y.value.totalUsage),1),l[38]||(l[38]=i("div",{class:"result-label"},"總用水量(度)",-1))])]),_:1}),e(g,{span:6},{default:t(()=>[i("div",be,[i("div",ve,p(y.value.totalAmount),1),l[39]||(l[39]=i("div",{class:"result-label"},"總水費(元)",-1))])]),_:1}),e(g,{span:6},{default:t(()=>[i("div",ye,[i("div",Ve,p(y.value.basicFee),1),l[40]||(l[40]=i("div",{class:"result-label"},"基本費(元)",-1))])]),_:1}),e(g,{span:6},{default:t(()=>[i("div",we,[i("div",Re,p(y.value.tax),1),l[41]||(l[41]=i("div",{class:"result-label"},"營業稅(元)",-1))])]),_:1})]),_:1}),e(x,{data:y.value.details,stripe:"",border:"",style:{"margin-top":"20px"}},{default:t(()=>[e(r,{prop:"month",label:"月份",width:"100"}),e(r,{prop:"usage",label:"用水量(度)",width:"120",align:"right"}),e(r,{prop:"tier1Usage",label:"第一階用量",width:"120",align:"right"}),e(r,{prop:"tier2Usage",label:"第二階用量",width:"120",align:"right"}),e(r,{prop:"tier3Usage",label:"第三階用量",width:"120",align:"right"}),e(r,{prop:"amount",label:"水費(元)",width:"120",align:"right"})]),_:1},8,["data"])]),_:1})])):re("",!0)]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(S,{modelValue:D.value,"onUpdate:modelValue":l[14]||(l[14]=a=>D.value=a),title:s.id?"編輯水表":"新增水表",width:"600px"},{footer:t(()=>[e(f,{onClick:l[13]||(l[13]=a=>D.value=!1)},{default:t(()=>l[43]||(l[43]=[o("取消",-1)])),_:1,__:[43]}),e(f,{type:"primary",onClick:m.saveMeter},{default:t(()=>l[44]||(l[44]=[o("確認",-1)])),_:1,__:[44]},8,["onClick"])]),default:t(()=>[e(Y(z),{ref_key:"meterFormRef",ref:j,model:s,rules:K,"label-width":"120px"},{default:t(()=>[e(n,{label:"水表名稱",prop:"name"},{default:t(()=>[e(C,{modelValue:s.name,"onUpdate:modelValue":l[5]||(l[5]=a=>s.name=a),placeholder:"請輸入水表名稱"},null,8,["modelValue"])]),_:1}),e(n,{label:"水表編號",prop:"meterNo"},{default:t(()=>[e(C,{modelValue:s.meterNo,"onUpdate:modelValue":l[6]||(l[6]=a=>s.meterNo=a),placeholder:"請輸入水表編號"},null,8,["modelValue"])]),_:1}),e(n,{label:"安裝位置",prop:"location"},{default:t(()=>[e(C,{modelValue:s.location,"onUpdate:modelValue":l[7]||(l[7]=a=>s.location=a),placeholder:"請輸入安裝位置"},null,8,["modelValue"])]),_:1}),e(n,{label:"水表類型",prop:"meterType"},{default:t(()=>[e(q,{modelValue:s.meterType,"onUpdate:modelValue":l[8]||(l[8]=a=>s.meterType=a),style:{width:"100%"}},{default:t(()=>[e(b,{label:"機械式水表",value:"mechanical"}),e(b,{label:"電子式水表",value:"electronic"}),e(b,{label:"智慧水表",value:"smart"}),e(b,{label:"超音波水表",value:"ultrasonic"})]),_:1},8,["modelValue"])]),_:1}),e(V,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(n,{label:"口徑",prop:"caliber"},{default:t(()=>[e(v,{modelValue:s.caliber,"onUpdate:modelValue":l[9]||(l[9]=a=>s.caliber=a),min:15,max:300,style:{width:"100%"}},null,8,["modelValue"]),l[42]||(l[42]=i("span",{style:{"margin-left":"8px"}},"mm",-1))]),_:1,__:[42]})]),_:1}),e(g,{span:12},{default:t(()=>[e(n,{label:"倍率",prop:"multiplier"},{default:t(()=>[e(v,{modelValue:s.multiplier,"onUpdate:modelValue":l[10]||(l[10]=a=>s.multiplier=a),min:.1,max:1e3,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(n,{label:"安裝日期",prop:"installDate"},{default:t(()=>[e(c,{modelValue:s.installDate,"onUpdate:modelValue":l[11]||(l[11]=a=>s.installDate=a),type:"date",placeholder:"選擇安裝日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"備註"},{default:t(()=>[e(C,{modelValue:s.description,"onUpdate:modelValue":l[12]||(l[12]=a=>s.description=a),type:"textarea",rows:3,placeholder:"請輸入備註"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(S,{modelValue:F.value,"onUpdate:modelValue":l[25]||(l[25]=a=>F.value=a),title:d.id?"編輯水價":"新增水價",width:"700px"},{footer:t(()=>[e(f,{onClick:l[24]||(l[24]=a=>F.value=!1)},{default:t(()=>l[52]||(l[52]=[o("取消",-1)])),_:1,__:[52]}),e(f,{type:"primary",onClick:m.saveRate},{default:t(()=>l[53]||(l[53]=[o("確認",-1)])),_:1,__:[53]},8,["onClick"])]),default:t(()=>[e(Y(z),{ref_key:"rateFormRef",ref:G,model:d,rules:O,"label-width":"120px"},{default:t(()=>[e(n,{label:"費率名稱",prop:"name"},{default:t(()=>[e(C,{modelValue:d.name,"onUpdate:modelValue":l[15]||(l[15]=a=>d.name=a),placeholder:"請輸入費率名稱"},null,8,["modelValue"])]),_:1}),e(n,{label:"費率類型",prop:"rateType"},{default:t(()=>[e(q,{modelValue:d.rateType,"onUpdate:modelValue":l[16]||(l[16]=a=>d.rateType=a),style:{width:"100%"}},{default:t(()=>[e(b,{label:"住宅用水",value:"residential"}),e(b,{label:"商業用水",value:"commercial"}),e(b,{label:"工業用水",value:"industrial"}),e(b,{label:"機關用水",value:"government"})]),_:1},8,["modelValue"])]),_:1}),e(Z,{"content-position":"left"},{default:t(()=>l[45]||(l[45]=[o("階梯水價設定",-1)])),_:1,__:[45]}),e(V,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(n,{label:"第一階水價",prop:"tier1Rate"},{default:t(()=>[e(v,{modelValue:d.tier1Rate,"onUpdate:modelValue":l[17]||(l[17]=a=>d.tier1Rate=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),l[46]||(l[46]=i("span",{style:{"margin-left":"8px"}},"元/度",-1))]),_:1,__:[46]})]),_:1}),e(g,{span:12},{default:t(()=>[e(n,{label:"第一階上限",prop:"tier1Limit"},{default:t(()=>[e(v,{modelValue:d.tier1Limit,"onUpdate:modelValue":l[18]||(l[18]=a=>d.tier1Limit=a),min:0,style:{width:"100%"}},null,8,["modelValue"]),l[47]||(l[47]=i("span",{style:{"margin-left":"8px"}},"度",-1))]),_:1,__:[47]})]),_:1})]),_:1}),e(V,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(n,{label:"第二階水價",prop:"tier2Rate"},{default:t(()=>[e(v,{modelValue:d.tier2Rate,"onUpdate:modelValue":l[19]||(l[19]=a=>d.tier2Rate=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),l[48]||(l[48]=i("span",{style:{"margin-left":"8px"}},"元/度",-1))]),_:1,__:[48]})]),_:1}),e(g,{span:12},{default:t(()=>[e(n,{label:"第二階上限",prop:"tier2Limit"},{default:t(()=>[e(v,{modelValue:d.tier2Limit,"onUpdate:modelValue":l[20]||(l[20]=a=>d.tier2Limit=a),min:0,style:{width:"100%"}},null,8,["modelValue"]),l[49]||(l[49]=i("span",{style:{"margin-left":"8px"}},"度",-1))]),_:1,__:[49]})]),_:1})]),_:1}),e(V,{gutter:20},{default:t(()=>[e(g,{span:12},{default:t(()=>[e(n,{label:"第三階水價",prop:"tier3Rate"},{default:t(()=>[e(v,{modelValue:d.tier3Rate,"onUpdate:modelValue":l[21]||(l[21]=a=>d.tier3Rate=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),l[50]||(l[50]=i("span",{style:{"margin-left":"8px"}},"元/度",-1))]),_:1,__:[50]})]),_:1}),e(g,{span:12},{default:t(()=>[e(n,{label:"基本費",prop:"basicFee"},{default:t(()=>[e(v,{modelValue:d.basicFee,"onUpdate:modelValue":l[22]||(l[22]=a=>d.basicFee=a),min:0,precision:0,style:{width:"100%"}},null,8,["modelValue"]),l[51]||(l[51]=i("span",{style:{"margin-left":"8px"}},"元",-1))]),_:1,__:[51]})]),_:1})]),_:1}),e(n,{label:"生效日期",prop:"effectiveDate"},{default:t(()=>[e(c,{modelValue:d.effectiveDate,"onUpdate:modelValue":l[23]||(l[23]=a=>d.effectiveDate=a),type:"date",placeholder:"選擇生效日期",style:{width:"300px"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const Fe=oe(ke,[["__scopeId","data-v-5f968382"]]);export{Fe as default};
