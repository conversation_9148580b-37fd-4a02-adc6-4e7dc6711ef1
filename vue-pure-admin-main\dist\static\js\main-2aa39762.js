import{d as c,u as d,p as t,c as r,e as i,g as a,w as s,f as e,t as p,_ as l}from"./index-329ed960.js";const m={class:"gui-main-container"},u={class:"card-header"},f={class:"content"},h=c({__name:"main",setup(v){return d(),(n,y)=>{const o=t("el-empty"),_=t("el-card");return r(),i("div",m,[a(_,null,{header:s(()=>[e("div",u,[e("span",null,"圖形監控 - "+p(n.$route.params.id),1)])]),default:s(()=>[e("div",f,[a(o,{description:"圖形監控功能開發中..."})])]),_:1})])}}});const x=l(h,[["__scopeId","data-v-ddc42a36"]]);export{x as default};
