import{a as ee}from"./index-0d74a956.js";import{u as le}from"./dataService-42a3bdf6.js";import{d as ae,r,G as N,D as te,p as n,q as oe,c as b,e as R,f as i,g as e,w as a,h as F,aH as x,X as de,Y as se,n as w,t as p,m as d,l as U,j as ne}from"./index-329ed960.js";const ie={class:"history-container"},ue={class:"tab-content"},re={style:{float:"left"}},pe={style:{float:"right",color:"#8492a6","font-size":"13px"}},me={key:0,class:"search-results"},fe={class:"card-header"},ce={class:"header-actions"},_e={class:"tab-content"},be={class:"schedule-header"},ge={class:"tab-content"},ve={class:"download-header"},ke=ae({__name:"history",setup(ye){le();const Q=r(),B=r(),z=r("search"),V=r(!1),M=r(!1),A=r(!1),E=r(!1),H=r(""),g=r(!1),f=N({selectedTags:[],dateRange:[],dataType:"raw",interval:"1m"}),c=N({name:"",description:"",frequency:"daily",executeTime:""}),I={selectedTags:[{required:!0,message:"請選擇至少一個標籤",trigger:"change"}],dateRange:[{required:!0,message:"請選擇時間範圍",trigger:"change"}]},j={name:[{required:!0,message:"請輸入排程名稱",trigger:"blur"}],frequency:[{required:!0,message:"請選擇執行頻率",trigger:"change"}],executeTime:[{required:!0,message:"請選擇執行時間",trigger:"change"}]},L=r([]),G=r([]),K=r([]),P=r([]),X=r([]),k=te(()=>L.value.length>0);return(o,t)=>{const u=n("el-option"),v=n("el-select"),_=n("el-form-item"),y=n("el-col"),J=n("el-date-picker"),Y=n("el-row"),m=n("el-button"),T=n("el-input"),s=n("el-table-column"),C=n("el-tag"),D=n("el-table"),$=n("el-card"),S=n("el-tab-pane"),O=n("el-tabs"),W=n("el-time-picker"),Z=n("el-dialog"),q=oe("loading");return b(),R("div",ie,[t[25]||(t[25]=i("div",{class:"page-header"},[i("h2",null,"歷史報表"),i("p",null,"查詢、排程和下載歷史數據報表")],-1)),e($,{class:"main-card"},{default:a(()=>[e(O,{modelValue:z.value,"onUpdate:modelValue":t[6]||(t[6]=l=>z.value=l),type:"border-card"},{default:a(()=>[e(S,{label:"查詢",name:"search"},{default:a(()=>[i("div",ue,[e(F(x),{ref_key:"searchFormRef",ref:Q,model:f,rules:I,"label-width":"120px",class:"search-form"},{default:a(()=>[e(Y,{gutter:20},{default:a(()=>[e(y,{span:12},{default:a(()=>[e(_,{label:"選擇標籤",prop:"selectedTags"},{default:a(()=>[e(v,{modelValue:f.selectedTags,"onUpdate:modelValue":t[0]||(t[0]=l=>f.selectedTags=l),multiple:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"請選擇要查詢的標籤","remote-method":o.searchTags,loading:E.value,style:{width:"100%"}},{default:a(()=>[(b(!0),R(de,null,se(K.value,l=>(b(),w(u,{key:l.id,label:l.name,value:l.id},{default:a(()=>[i("span",re,p(l.name),1),i("span",pe,p(l.description),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading"])]),_:1})]),_:1}),e(y,{span:12},{default:a(()=>[e(_,{label:"時間範圍",prop:"dateRange"},{default:a(()=>[e(J,{modelValue:f.dateRange,"onUpdate:modelValue":t[1]||(t[1]=l=>f.dateRange=l),type:"datetimerange","range-separator":"至","start-placeholder":"開始時間","end-placeholder":"結束時間",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(Y,{gutter:20},{default:a(()=>[e(y,{span:12},{default:a(()=>[e(_,{label:"數據類型"},{default:a(()=>[e(v,{modelValue:f.dataType,"onUpdate:modelValue":t[2]||(t[2]=l=>f.dataType=l),style:{width:"100%"}},{default:a(()=>[e(u,{label:"原始數據",value:"raw"}),e(u,{label:"平均值",value:"average"}),e(u,{label:"最大值",value:"max"}),e(u,{label:"最小值",value:"min"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:a(()=>[e(_,{label:"取樣間隔"},{default:a(()=>[e(v,{modelValue:f.interval,"onUpdate:modelValue":t[3]||(t[3]=l=>f.interval=l),style:{width:"100%"}},{default:a(()=>[e(u,{label:"1分鐘",value:"1m"}),e(u,{label:"5分鐘",value:"5m"}),e(u,{label:"15分鐘",value:"15m"}),e(u,{label:"30分鐘",value:"30m"}),e(u,{label:"1小時",value:"1h"}),e(u,{label:"1天",value:"1d"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(m,{type:"primary",loading:V.value,disabled:!f.selectedTags.length||!f.dateRange,onClick:o.handleSearch},{default:a(()=>[d(p(V.value?"查詢中...":"開始查詢"),1)]),_:1},8,["loading","disabled","onClick"]),e(m,{type:"success",disabled:!k.value,onClick:o.exportSearchData},{default:a(()=>t[13]||(t[13]=[d(" 匯出結果 ",-1)])),_:1,__:[13]},8,["disabled","onClick"]),e(m,{type:"warning",disabled:!k.value,onClick:o.clearSearchData},{default:a(()=>t[14]||(t[14]=[d(" 清除結果 ",-1)])),_:1,__:[14]},8,["disabled","onClick"])]),_:1})]),_:1},8,["model"]),k.value?(b(),R("div",me,[e($,null,{header:a(()=>[i("div",fe,[i("span",null,"查詢結果 ("+p(L.value.length)+" 筆)",1),i("div",ce,[e(T,{modelValue:H.value,"onUpdate:modelValue":t[4]||(t[4]=l=>H.value=l),placeholder:"搜尋標籤名稱...","prefix-icon":F(ee),clearable:"",style:{width:"250px"},onInput:o.handleSearchFilter},null,8,["modelValue","prefix-icon","onInput"])])])]),default:a(()=>[U((b(),w(D,{data:G.value,height:"400",stripe:"",border:""},{default:a(()=>[e(s,{prop:"TagName",label:"標籤名稱",width:"200",fixed:"left"}),e(s,{prop:"Value",label:"數值",width:"120",align:"right"},{default:a(({row:l})=>[d(p(o.formatValue(l.Value)),1)]),_:1}),e(s,{prop:"Unit",label:"單位",width:"80"}),e(s,{prop:"Timestamp",label:"時間戳",width:"180"},{default:a(({row:l})=>[d(p(o.formatDateTime(l.Timestamp)),1)]),_:1}),e(s,{prop:"Quality",label:"品質",width:"100"},{default:a(({row:l})=>[e(C,{type:o.getQualityType(l.Quality)},{default:a(()=>[d(p(o.getQualityText(l.Quality)),1)]),_:2},1032,["type"])]),_:1}),e(s,{prop:"Description",label:"描述","min-width":"200"})]),_:1},8,["data"])),[[q,V.value]])]),_:1})])):ne("",!0)])]),_:1}),e(S,{label:"排程",name:"schedule"},{default:a(()=>[i("div",_e,[i("div",be,[t[16]||(t[16]=i("h3",null,"報表排程管理",-1)),e(m,{type:"primary",onClick:t[5]||(t[5]=l=>g.value=!0)},{default:a(()=>t[15]||(t[15]=[d(" 新增排程 ",-1)])),_:1,__:[15]})]),U((b(),w(D,{data:P.value,stripe:"",border:""},{default:a(()=>[e(s,{prop:"name",label:"排程名稱",width:"200"}),e(s,{prop:"description",label:"描述","min-width":"200"}),e(s,{prop:"frequency",label:"頻率",width:"120"},{default:a(({row:l})=>[d(p(o.getFrequencyText(l.frequency)),1)]),_:1}),e(s,{prop:"nextRun",label:"下次執行",width:"180"},{default:a(({row:l})=>[d(p(o.formatDateTime(l.nextRun)),1)]),_:1}),e(s,{prop:"status",label:"狀態",width:"100"},{default:a(({row:l})=>[e(C,{type:l.status==="active"?"success":"danger"},{default:a(()=>[d(p(l.status==="active"?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),e(s,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[e(m,{type:"primary",size:"small",onClick:h=>o.editSchedule(l)},{default:a(()=>t[17]||(t[17]=[d(" 編輯 ",-1)])),_:2,__:[17]},1032,["onClick"]),e(m,{type:l.status==="active"?"warning":"success",size:"small",onClick:h=>o.toggleScheduleStatus(l)},{default:a(()=>[d(p(l.status==="active"?"停用":"啟用"),1)]),_:2},1032,["type","onClick"]),e(m,{type:"danger",size:"small",onClick:h=>o.deleteSchedule(l)},{default:a(()=>t[18]||(t[18]=[d(" 刪除 ",-1)])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[q,M.value]])])]),_:1}),e(S,{label:"下載",name:"download"},{default:a(()=>[i("div",ge,[i("div",ve,[t[20]||(t[20]=i("h3",null,"下載歷史",-1)),e(m,{type:"primary",onClick:o.refreshDownloadList},{default:a(()=>t[19]||(t[19]=[d(" 刷新列表 ",-1)])),_:1,__:[19]},8,["onClick"])]),U((b(),w(D,{data:X.value,stripe:"",border:""},{default:a(()=>[e(s,{prop:"fileName",label:"檔案名稱",width:"300"}),e(s,{prop:"fileSize",label:"檔案大小",width:"120"},{default:a(({row:l})=>[d(p(o.formatFileSize(l.fileSize)),1)]),_:1}),e(s,{prop:"createTime",label:"建立時間",width:"180"},{default:a(({row:l})=>[d(p(o.formatDateTime(l.createTime)),1)]),_:1}),e(s,{prop:"status",label:"狀態",width:"100"},{default:a(({row:l})=>[e(C,{type:o.getDownloadStatusType(l.status)},{default:a(()=>[d(p(o.getDownloadStatusText(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(s,{prop:"description",label:"描述","min-width":"200"}),e(s,{label:"操作",width:"150",fixed:"right"},{default:a(({row:l})=>[e(m,{type:"primary",size:"small",disabled:l.status!=="completed",onClick:h=>o.downloadFile(l)},{default:a(()=>t[21]||(t[21]=[d(" 下載 ",-1)])),_:2,__:[21]},1032,["disabled","onClick"]),e(m,{type:"danger",size:"small",onClick:h=>o.deleteDownloadFile(l)},{default:a(()=>t[22]||(t[22]=[d(" 刪除 ",-1)])),_:2,__:[22]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[q,A.value]])])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(Z,{modelValue:g.value,"onUpdate:modelValue":t[12]||(t[12]=l=>g.value=l),title:"新增報表排程",width:"600px"},{footer:a(()=>[e(m,{onClick:t[11]||(t[11]=l=>g.value=!1)},{default:a(()=>t[23]||(t[23]=[d("取消",-1)])),_:1,__:[23]}),e(m,{type:"primary",onClick:o.saveSchedule},{default:a(()=>t[24]||(t[24]=[d("保存",-1)])),_:1,__:[24]},8,["onClick"])]),default:a(()=>[e(F(x),{ref_key:"scheduleFormRef",ref:B,model:c,rules:j,"label-width":"120px"},{default:a(()=>[e(_,{label:"排程名稱",prop:"name"},{default:a(()=>[e(T,{modelValue:c.name,"onUpdate:modelValue":t[7]||(t[7]=l=>c.name=l),placeholder:"請輸入排程名稱"},null,8,["modelValue"])]),_:1}),e(_,{label:"描述",prop:"description"},{default:a(()=>[e(T,{modelValue:c.description,"onUpdate:modelValue":t[8]||(t[8]=l=>c.description=l),type:"textarea",rows:3,placeholder:"請輸入排程描述"},null,8,["modelValue"])]),_:1}),e(_,{label:"執行頻率",prop:"frequency"},{default:a(()=>[e(v,{modelValue:c.frequency,"onUpdate:modelValue":t[9]||(t[9]=l=>c.frequency=l),style:{width:"100%"}},{default:a(()=>[e(u,{label:"每日",value:"daily"}),e(u,{label:"每週",value:"weekly"}),e(u,{label:"每月",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"執行時間",prop:"executeTime"},{default:a(()=>[e(W,{modelValue:c.executeTime,"onUpdate:modelValue":t[10]||(t[10]=l=>c.executeTime=l),format:"HH:mm","value-format":"HH:mm",placeholder:"選擇執行時間",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{ke as default};
