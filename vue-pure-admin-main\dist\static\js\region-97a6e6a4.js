import{F as ne,a as se,r as re}from"./index-0d74a956.js";import{t as z}from"./tags-f37275ed.js";import{u as ie,p as T}from"./dataService-42a3bdf6.js";import{d as de,r as i,G as ue,D as ce,o as pe,p as r,q as me,c as v,e as E,f as u,g as t,w as l,n as w,X as ge,Y as _e,m as p,t as fe,j as F,h as I,l as ve,aH as ye,aF as be,E as m,_ as Re}from"./index-329ed960.js";const he={class:"region-container"},Ce={class:"card-header"},we={class:"header-left"},Ie={class:"header-right"},ke={class:"filter-section"},Le={class:"pagination-container"},Ve=de({__name:"region",setup(xe){ie();const k=i(),L=i(!1),_=i(!1),b=i(""),R=i(1),V=i(20),h=i(0),d=i([]),y=i(""),f=i([]),C=i([]),H={children:"children",label:"name",value:"id"},s=ue({id:"",name:"",parentId:""}),$={name:[{required:!0,message:"請輸入地區名稱",trigger:"blur"}]},U=ce(()=>{let a=f.value;return b.value&&(a=a.filter(e=>e.name.toLowerCase().includes(b.value.toLowerCase()))),a}),O=()=>{},j=a=>{V.value=a,R.value=1,c()},G=a=>{R.value=a,c()},q=()=>{c()},A=(a,e)=>{d.value=d.value.slice(0,e+1),y.value=a.id,c()},K=()=>{d.value.length>0&&(d.value.pop(),y.value=d.value.length>0?d.value[d.value.length-1].id:"",c())},X=a=>{Object.assign(s,{id:a.id,name:a.name,parentId:y.value}),_.value=!0},Y=async a=>{try{await be.confirm(`確定要刪除地區 "${a.name}" 嗎？此操作不可恢復！`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const e={RegionId:a.id},o=await z.deleteRegion(e);if(o&&(o.ReturnCode===1||o.success))m.success("地區刪除成功"),await c();else{const x=o?.Message||o?.message||"刪除失敗";throw new Error(x)}}catch(e){if(e!=="cancel"){console.error("刪除地區失敗:",e);let o="刪除地區失敗";e.message?o=e.message:e.response?.data?.Message?o=e.response.data.Message:e.response?.data?.message?o=e.response.data.message:typeof e=="string"&&(o=e),m.error(o)}}},J=async()=>{if(k.value)try{await k.value.validate(),!s.parentId&&y.value&&(s.parentId=y.value);let a;if(s.id){const e={RegionId:s.id,RegionName:s.name};a=await z.updateRegion(e)}else{const e={ParentId:s.parentId||void 0,RegionName:s.name};a=await z.createRegion(e)}if(a&&(a.ReturnCode===1||a.success))m.success(s.id?"地區更新成功":"地區新增成功"),_.value=!1,Object.assign(s,{id:"",name:"",code:"",parentId:"",description:"",status:"active",sortOrder:0}),await c();else{const e=a?.Message||a?.message||"保存失敗";throw new Error(e)}}catch(a){console.error("保存地區失敗:",a);let e="保存地區失敗";a.message?e=a.message:a.response?.data?.Message?e=a.response.data.Message:a.response?.data?.message?e=a.response.data.message:typeof a=="string"&&(e=a),m.error(e)}},c=async()=>{try{L.value=!0;const a=await T.get("/api/Tag/GetRegionHierarchyList");a&&a.Detail&&a.Detail.RegionHierarchyList?(f.value=a.Detail.RegionHierarchyList.map(e=>({id:e.RegionId||e.Id,name:e.RegionName||e.Name})),h.value=f.value.length,m.success(`成功載入 ${f.value.length} 個地區`)):(f.value=[],h.value=0,m.info("目前沒有地區資料"))}catch(a){console.error("載入地區列表失敗:",a),f.value=[],h.value=0,m.error("載入地區列表失敗")}finally{L.value=!1}},Q=async()=>{try{const a=await T.get("/api/Tag/GetRegionHierarchyList");if(a&&a.Detail&&a.Detail.RegionHierarchyList){const e=o=>({id:o.RegionId||o.Id,name:o.RegionName||o.Name,children:o.ChildList?o.ChildList.map(e):[]});C.value=a.Detail.RegionHierarchyList.map(e)}else C.value=[]}catch(a){console.error("載入地區樹選項失敗:",a),m.error("載入地區樹選項失敗"),C.value=[]}};return pe(async()=>{await Promise.all([c(),Q()])}),(a,e)=>{const o=r("el-breadcrumb-item"),x=r("el-breadcrumb"),D=r("el-icon"),g=r("el-button"),N=r("el-input"),B=r("el-col"),W=r("el-row"),P=r("el-table-column"),Z=r("el-table"),ee=r("el-pagination"),ae=r("el-card"),S=r("el-form-item"),te=r("el-tree-select"),le=r("el-dialog"),oe=me("loading");return v(),E("div",he,[e[16]||(e[16]=u("div",{class:"page-header"},[u("h2",null,"地區管理"),u("p",null,"管理系統地區結構，支援階層式地區組織")],-1)),t(ae,{class:"region-card"},{header:l(()=>[u("div",Ce,[u("div",we,[e[8]||(e[8]=u("span",null,"地區列表",-1)),d.value.length>0?(v(),w(x,{key:0,separator:"/",class:"breadcrumb"},{default:l(()=>[(v(!0),E(ge,null,_e(d.value,(n,M)=>(v(),w(o,{key:n.id,onClick:De=>A(n,M),class:"breadcrumb-item"},{default:l(()=>[p(fe(n.name),1)]),_:2},1032,["onClick"]))),128))]),_:1})):F("",!0)]),u("div",Ie,[d.value.length>0?(v(),w(g,{key:0,type:"info",onClick:K},{default:l(()=>[t(D,null,{default:l(()=>[t(I(ne))]),_:1}),e[9]||(e[9]=p(" 返回上級 ",-1))]),_:1,__:[9]})):F("",!0),t(g,{type:"primary",onClick:e[0]||(e[0]=n=>_.value=!0)},{default:l(()=>e[10]||(e[10]=[p(" 新增地區 ",-1)])),_:1,__:[10]})])])]),default:l(()=>[u("div",ke,[t(W,{gutter:20},{default:l(()=>[t(B,{span:8},{default:l(()=>[t(N,{modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=n=>b.value=n),placeholder:"搜尋地區名稱",style:{width:"300px"},clearable:"",onInput:O},{prefix:l(()=>[t(D,null,{default:l(()=>[t(I(se))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(B,{span:6},{default:l(()=>[t(g,{type:"info",onClick:q},{default:l(()=>[t(D,null,{default:l(()=>[t(I(re))]),_:1}),e[11]||(e[11]=p(" 刷新 ",-1))]),_:1,__:[11]})]),_:1})]),_:1})]),ve((v(),w(Z,{data:U.value,stripe:"",border:""},{default:l(()=>[t(P,{prop:"name",label:"地區名稱","min-width":"200"}),t(P,{label:"操作",width:"200",fixed:"right"},{default:l(({row:n})=>[t(g,{type:"primary",size:"small",onClick:M=>X(n)},{default:l(()=>e[12]||(e[12]=[p(" 編輯 ",-1)])),_:2,__:[12]},1032,["onClick"]),t(g,{type:"danger",size:"small",onClick:M=>Y(n)},{default:l(()=>e[13]||(e[13]=[p(" 刪除 ",-1)])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[oe,L.value]]),u("div",Le,[t(ee,{"current-page":R.value,"onUpdate:currentPage":e[2]||(e[2]=n=>R.value=n),"page-size":V.value,"onUpdate:pageSize":e[3]||(e[3]=n=>V.value=n),total:h.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:G},null,8,["current-page","page-size","total"])])]),_:1}),t(le,{modelValue:_.value,"onUpdate:modelValue":e[7]||(e[7]=n=>_.value=n),title:s.id?"編輯地區":"新增地區",width:"500px"},{footer:l(()=>[t(g,{onClick:e[6]||(e[6]=n=>_.value=!1)},{default:l(()=>e[14]||(e[14]=[p("取消",-1)])),_:1,__:[14]}),t(g,{type:"primary",onClick:J},{default:l(()=>e[15]||(e[15]=[p("確認",-1)])),_:1,__:[15]})]),default:l(()=>[t(I(ye),{ref_key:"regionFormRef",ref:k,model:s,rules:$,"label-width":"120px"},{default:l(()=>[t(S,{label:"地區名稱",prop:"name"},{default:l(()=>[t(N,{modelValue:s.name,"onUpdate:modelValue":e[4]||(e[4]=n=>s.name=n),placeholder:"請輸入地區名稱"},null,8,["modelValue"])]),_:1}),t(S,{label:"上級地區",prop:"parentId"},{default:l(()=>[t(te,{modelValue:s.parentId,"onUpdate:modelValue":e[5]||(e[5]=n=>s.parentId=n),data:C.value,props:H,placeholder:"請選擇上級地區（可選）",clearable:"",style:{width:"100%"}},null,8,["modelValue","data"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const Pe=Re(Ve,[["__scopeId","data-v-c8df9d53"]]);export{Pe as default};
