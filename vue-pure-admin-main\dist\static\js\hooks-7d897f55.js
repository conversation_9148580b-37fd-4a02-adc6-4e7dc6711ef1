import{d as f,ao as r,aV as u,aW as c,aX as l}from"./index-329ed960.js";function a(e,o){const t=/^IF-/;if(t.test(e)){const n=e.split(t)[1],i=n.slice(0,n.indexOf(" ")==-1?n.length:n.indexOf(" ")),s=n.slice(n.indexOf(" ")+1,n.length);return f({name:"FontIcon",render(){return r(u,{icon:i,iconType:s,...o})}})}else return typeof e=="function"||typeof e?.render=="function"?o?r(e,{...o}):e:typeof e=="object"?f({name:"OfflineIcon",render(){return r(c,{icon:e,...o})}}):f({name:"Icon",render(){if(!e)return;const n=e.includes(":")?l:c;return r(n,{icon:e,...o})}})}export{a as u};
