import{a as U<PERSON>,r as Se,m as <PERSON>}from"./index-0d74a956.js";import{t as M}from"./tags-f37275ed.js";import{u as Z}from"./dataService-42a3bdf6.js";import{d as $e,r as u,G as ee,D as Ae,o as ze,p as n,q as Oe,c as E,e as Re,f as p,g as t,w as a,m as r,h as L,l as te,n as le,t as D,aH as ae,aF as N,E as c,_ as Be}from"./index-329ed960.js";const Fe={class:"device-container"},Ee={class:"tab-content"},Ne={class:"card-header"},je={class:"filter-section"},He={class:"device-name"},qe={class:"pagination-container"},Qe={class:"tab-content"},Ge={class:"card-header"},Je={class:"tree-node"},Ke={class:"node-label"},We={class:"node-actions"},Xe=$e({__name:"device",setup(Ye){const oe=()=>{const l=localStorage.getItem("PLC_CUSTOMER_ID");if(l)return l;const e=Z();if(e.token)try{return JSON.parse(atob(e.token.split(".")[1])).CustomerId||"fdff1878-a54a-44e5-b5c7-123456789abc"}catch(s){console.error("解析token失敗:",s)}return"fdff1878-a54a-44e5-b5c7-123456789abc"};Z();const $=u(),A=u(),se=u(),j=u("list"),z=u(!1),O=u(!1),g=u(!1),_=u(!1),T=u(""),P=u(""),h=u(""),I=u(1),R=u(20),U=u(0),b=u([]),k=u([]),H={children:"children",label:"name",value:"id"},i=ee({id:"",name:"",deviceType:"",description:"",ipAddress:"",port:502,protocol:"",status:"active"}),m=ee({id:"",name:"",parentId:"",description:""}),ne={name:[{required:!0,message:"請輸入裝置名稱",trigger:"blur"}],deviceType:[{required:!0,message:"請選擇裝置類型",trigger:"change"}],ipAddress:[{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"請輸入正確的IP位址格式",trigger:"blur"}],port:[{type:"number",min:1,max:65535,message:"連接埠範圍為1-65535",trigger:"blur"}]},re={name:[{required:!0,message:"請輸入分類名稱",trigger:"blur"}]},ie=Ae(()=>{let l=b.value;return T.value&&(l=l.filter(e=>e.name.toLowerCase().includes(T.value.toLowerCase())||e.description?.toLowerCase().includes(T.value.toLowerCase()))),P.value&&(l=l.filter(e=>e.status===P.value)),h.value&&(l=l.filter(e=>e.deviceType===h.value)),l}),de=l=>({PLC:"primary",Sensor:"success",Actuator:"warning",Other:"info"})[l]||"default",ue=l=>({PLC:"PLC",Sensor:"感測器",Actuator:"執行器",Other:"其他"})[l]||l,pe=l=>l?new Date(l).toLocaleString("zh-TW"):"-",ce=()=>{},q=()=>{},me=l=>{R.value=l,I.value=1,y()},ve=l=>{I.value=l,y()},fe=()=>{y()},_e=l=>{Object.assign(i,l),g.value=!0},ge=async l=>{try{const e=l.status==="active"?"inactive":"active",s=e==="active"?"啟用":"停用";await N.confirm(`確定要${s}裝置 "${l.name}" 嗎？`,"狀態變更確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"}),l.status=e,c.success(`裝置${s}成功`)}catch(e){e!=="cancel"&&(console.error("切換裝置狀態失敗:",e),c.error("切換裝置狀態失敗"))}},be=async l=>{try{await N.confirm(`確定要刪除裝置 "${l.name}" 嗎？此操作不可恢復！`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const e={DeviceId:l.id},s=await M.deleteDevice(e);if(s&&(s.ReturnCode===1||s.success))c.success("裝置刪除成功"),await y();else{const x=s?.Message||s?.message||"刪除失敗";throw new Error(x)}}catch(e){if(e!=="cancel"){console.error("刪除裝置失敗:",e);let s="刪除裝置失敗";e.message?s=e.message:e.response?.data?.Message?s=e.response.data.Message:e.response?.data?.message?s=e.response.data.message:typeof e=="string"&&(s=e),c.error({message:s,duration:5e3,showClose:!0})}}},ye=async()=>{if($.value)try{await $.value.validate(),c.success("裝置保存成功"),g.value=!1,Object.assign(i,{id:"",name:"",deviceType:"",description:"",ipAddress:"",port:502,protocol:"",status:"active"}),await y()}catch(l){console.error("保存裝置失敗:",l),l.message&&c.error(l.message)}},Ce=l=>{Object.assign(m,l),_.value=!0},we=l=>{Object.assign(m,{id:"",name:"",parentId:l.id,description:""}),_.value=!0},Ve=async l=>{try{await N.confirm(`確定要刪除分類 "${l.name}" 嗎？此操作不可恢復！`,"刪除確認",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"});const e={id:l.id,customerId:oe()},s=await M.deleteDeviceCategory(e);if(s&&(s.ReturnCode===1||s.success))c.success("分類刪除成功"),await B();else throw new Error(s?.Message||"刪除失敗")}catch(e){e!=="cancel"&&(console.error("刪除分類失敗:",e),c.error(e.message||"刪除分類失敗"))}},De=async()=>{if(A.value)try{await A.value.validate(),c.success("分類保存成功"),_.value=!1,Object.assign(m,{id:"",name:"",parentId:"",description:""}),await B()}catch(l){console.error("保存分類失敗:",l),l.message&&c.error(l.message)}},y=async()=>{try{z.value=!0;const l=await M.getDevices();l&&l.Detail&&l.Detail.DeviceList?(b.value=l.Detail.DeviceList.map(e=>({id:e.DeviceId,name:e.DeviceName,type:e.TagChannel?.TypeName||"PLC",description:e.Description,ipAddress:e.Ip,port:e.Port,protocol:e.TagChannel?.TypeName||"",status:e.Status===1?"active":"inactive",createTime:new Date().toISOString()})),U.value=b.value.length,c.success(`成功載入 ${b.value.length} 個裝置`)):l&&l.Detail&&l.Detail.DeviceList&&l.Detail.DeviceList.length===0?(b.value=[],U.value=0,c.info("目前沒有裝置數據")):(b.value=[],U.value=0,c.warning("未找到裝置數據"))}catch(l){console.error("載入裝置列表失敗:",l),c.error(l.message||"載入裝置列表失敗")}finally{z.value=!1}},B=async()=>{try{O.value=!0;const l=await M.getDeviceCategoryHierarchy();if(l&&l.Detail&&l.Detail.DeviceCategoryHierarchyList){const e=s=>({id:s.Id||s.id,name:s.Name||s.name||"未命名分類",description:s.Description||s.description||"",children:s.ChildList?s.ChildList.map(e):[]});k.value=l.Detail.DeviceCategoryHierarchyList.map(e)}else k.value=[{id:"default",name:"預設裝置分類",description:"系統預設分類",children:[]}]}catch(l){console.error("載入分類樹失敗:",l),c.error(l.message||"載入分類樹失敗"),k.value=[{id:"default",name:"預設裝置分類",description:"系統預設分類",children:[]}]}finally{O.value=!1}};return ze(async()=>{await Promise.all([y(),B()])}),(l,e)=>{const s=n("el-button"),x=n("el-icon"),C=n("el-input"),w=n("el-col"),d=n("el-option"),S=n("el-select"),Q=n("el-row"),f=n("el-table-column"),G=n("el-tag"),Te=n("el-table"),ke=n("el-pagination"),F=n("el-card"),J=n("el-tab-pane"),xe=n("el-tree"),Le=n("el-tabs"),v=n("el-form-item"),Pe=n("el-input-number"),K=n("el-radio"),he=n("el-radio-group"),W=n("el-dialog"),Ie=n("el-tree-select"),X=Oe("loading");return E(),Re("div",Fe,[e[38]||(e[38]=p("div",{class:"page-header"},[p("h2",null,"裝置管理"),p("p",null,"管理 PLC 系統的裝置，包含裝置列表和裝置分類")],-1)),t(F,{class:"device-card"},{default:a(()=>[t(Le,{modelValue:j.value,"onUpdate:modelValue":e[7]||(e[7]=o=>j.value=o),type:"border-card"},{default:a(()=>[t(J,{label:"裝置列表",name:"list"},{default:a(()=>[p("div",Ee,[t(F,{shadow:"never"},{header:a(()=>[p("div",Ne,[e[23]||(e[23]=p("span",null,"裝置列表",-1)),t(s,{type:"primary",onClick:e[0]||(e[0]=o=>g.value=!0)},{default:a(()=>e[22]||(e[22]=[r(" 新增裝置 ",-1)])),_:1,__:[22]})])]),default:a(()=>[p("div",je,[t(Q,{gutter:20},{default:a(()=>[t(w,{span:8},{default:a(()=>[t(C,{modelValue:T.value,"onUpdate:modelValue":e[1]||(e[1]=o=>T.value=o),placeholder:"搜尋裝置名稱...",clearable:"",onInput:ce},{prefix:a(()=>[t(x,null,{default:a(()=>[t(L(Ue))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(w,{span:6},{default:a(()=>[t(S,{modelValue:P.value,"onUpdate:modelValue":e[2]||(e[2]=o=>P.value=o),placeholder:"狀態篩選",clearable:"",onChange:q},{default:a(()=>[t(d,{label:"全部",value:""}),t(d,{label:"啟用",value:"active"}),t(d,{label:"停用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1}),t(w,{span:6},{default:a(()=>[t(S,{modelValue:h.value,"onUpdate:modelValue":e[3]||(e[3]=o=>h.value=o),placeholder:"類型篩選",clearable:"",onChange:q},{default:a(()=>[t(d,{label:"全部",value:""}),t(d,{label:"PLC",value:"PLC"}),t(d,{label:"感測器",value:"Sensor"}),t(d,{label:"執行器",value:"Actuator"}),t(d,{label:"其他",value:"Other"})]),_:1},8,["modelValue"])]),_:1}),t(w,{span:4},{default:a(()=>[t(s,{type:"info",onClick:fe},{default:a(()=>[t(x,null,{default:a(()=>[t(L(Se))]),_:1}),e[24]||(e[24]=r(" 刷新 ",-1))]),_:1,__:[24]})]),_:1})]),_:1})]),te((E(),le(Te,{data:ie.value,stripe:"",border:""},{default:a(()=>[t(f,{prop:"name",label:"裝置名稱",width:"200"},{default:a(({row:o})=>[p("div",He,[t(x,{class:"device-icon"},{default:a(()=>[t(L(Me))]),_:1}),r(" "+D(o.name),1)])]),_:1}),t(f,{prop:"deviceType",label:"裝置類型",width:"120"},{default:a(({row:o})=>[t(G,{type:de(o.deviceType)},{default:a(()=>[r(D(ue(o.deviceType)),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"description",label:"描述","min-width":"200"}),t(f,{prop:"ipAddress",label:"IP位址",width:"150"}),t(f,{prop:"port",label:"連接埠",width:"100",align:"right"}),t(f,{prop:"protocol",label:"協議",width:"100"}),t(f,{prop:"status",label:"狀態",width:"100"},{default:a(({row:o})=>[t(G,{type:o.status==="active"?"success":"danger"},{default:a(()=>[r(D(o.status==="active"?"啟用":"停用"),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"createTime",label:"建立時間",width:"180"},{default:a(({row:o})=>[r(D(pe(o.createTime)),1)]),_:1}),t(f,{label:"操作",width:"200",fixed:"right"},{default:a(({row:o})=>[t(s,{type:"primary",size:"small",onClick:V=>_e(o)},{default:a(()=>e[25]||(e[25]=[r(" 編輯 ",-1)])),_:2,__:[25]},1032,["onClick"]),t(s,{type:"warning",size:"small",onClick:V=>ge(o)},{default:a(()=>[r(D(o.status==="active"?"停用":"啟用"),1)]),_:2},1032,["onClick"]),t(s,{type:"danger",size:"small",onClick:V=>be(o)},{default:a(()=>e[26]||(e[26]=[r(" 刪除 ",-1)])),_:2,__:[26]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,z.value]]),p("div",qe,[t(ke,{"current-page":I.value,"onUpdate:currentPage":e[4]||(e[4]=o=>I.value=o),"page-size":R.value,"onUpdate:pageSize":e[5]||(e[5]=o=>R.value=o),total:U.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:me,onCurrentChange:ve},null,8,["current-page","page-size","total"])])]),_:1})])]),_:1}),t(J,{label:"裝置分類",name:"class"},{default:a(()=>[p("div",Qe,[t(F,{shadow:"never"},{header:a(()=>[p("div",Ge,[e[28]||(e[28]=p("span",null,"裝置分類",-1)),t(s,{type:"primary",onClick:e[6]||(e[6]=o=>_.value=!0)},{default:a(()=>e[27]||(e[27]=[r(" 新增分類 ",-1)])),_:1,__:[27]})])]),default:a(()=>[te((E(),le(xe,{ref_key:"classTreeRef",ref:se,data:k.value,props:H,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},{default:a(({node:o,data:V})=>[p("div",Je,[p("span",Ke,D(o.label),1),p("div",We,[t(s,{type:"primary",size:"small",onClick:Y=>Ce(V)},{default:a(()=>e[29]||(e[29]=[r(" 編輯 ",-1)])),_:2,__:[29]},1032,["onClick"]),t(s,{type:"success",size:"small",onClick:Y=>we(V)},{default:a(()=>e[30]||(e[30]=[r(" 新增子分類 ",-1)])),_:2,__:[30]},1032,["onClick"]),t(s,{type:"danger",size:"small",onClick:Y=>Ve(V)},{default:a(()=>e[31]||(e[31]=[r(" 刪除 ",-1)])),_:2,__:[31]},1032,["onClick"])])])]),_:1},8,["data"])),[[X,O.value]])]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(W,{modelValue:g.value,"onUpdate:modelValue":e[16]||(e[16]=o=>g.value=o),title:i.id?"編輯裝置":"新增裝置",width:"700px"},{footer:a(()=>[t(s,{onClick:e[15]||(e[15]=o=>g.value=!1)},{default:a(()=>e[34]||(e[34]=[r("取消",-1)])),_:1,__:[34]}),t(s,{type:"primary",onClick:ye},{default:a(()=>e[35]||(e[35]=[r("確認",-1)])),_:1,__:[35]})]),default:a(()=>[t(L(ae),{ref_key:"deviceFormRef",ref:$,model:i,rules:ne,"label-width":"120px"},{default:a(()=>[t(v,{label:"裝置名稱",prop:"name"},{default:a(()=>[t(C,{modelValue:i.name,"onUpdate:modelValue":e[8]||(e[8]=o=>i.name=o),placeholder:"請輸入裝置名稱"},null,8,["modelValue"])]),_:1}),t(v,{label:"裝置類型",prop:"deviceType"},{default:a(()=>[t(S,{modelValue:i.deviceType,"onUpdate:modelValue":e[9]||(e[9]=o=>i.deviceType=o),style:{width:"100%"}},{default:a(()=>[t(d,{label:"PLC",value:"PLC"}),t(d,{label:"感測器",value:"Sensor"}),t(d,{label:"執行器",value:"Actuator"}),t(d,{label:"其他",value:"Other"})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"描述",prop:"description"},{default:a(()=>[t(C,{modelValue:i.description,"onUpdate:modelValue":e[10]||(e[10]=o=>i.description=o),type:"textarea",rows:3,placeholder:"請輸入裝置描述"},null,8,["modelValue"])]),_:1}),t(Q,{gutter:20},{default:a(()=>[t(w,{span:12},{default:a(()=>[t(v,{label:"IP位址",prop:"ipAddress"},{default:a(()=>[t(C,{modelValue:i.ipAddress,"onUpdate:modelValue":e[11]||(e[11]=o=>i.ipAddress=o),placeholder:"*************"},null,8,["modelValue"])]),_:1})]),_:1}),t(w,{span:12},{default:a(()=>[t(v,{label:"連接埠",prop:"port"},{default:a(()=>[t(Pe,{modelValue:i.port,"onUpdate:modelValue":e[12]||(e[12]=o=>i.port=o),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(v,{label:"協議",prop:"protocol"},{default:a(()=>[t(S,{modelValue:i.protocol,"onUpdate:modelValue":e[13]||(e[13]=o=>i.protocol=o),style:{width:"100%"}},{default:a(()=>[t(d,{label:"Modbus TCP",value:"ModbusTCP"}),t(d,{label:"Modbus RTU",value:"ModbusRTU"}),t(d,{label:"OPC UA",value:"OPCUA"}),t(d,{label:"MQTT",value:"MQTT"}),t(d,{label:"HTTP",value:"HTTP"})]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"狀態"},{default:a(()=>[t(he,{modelValue:i.status,"onUpdate:modelValue":e[14]||(e[14]=o=>i.status=o)},{default:a(()=>[t(K,{value:"active"},{default:a(()=>e[32]||(e[32]=[r("啟用",-1)])),_:1,__:[32]}),t(K,{value:"inactive"},{default:a(()=>e[33]||(e[33]=[r("停用",-1)])),_:1,__:[33]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(W,{modelValue:_.value,"onUpdate:modelValue":e[21]||(e[21]=o=>_.value=o),title:m.id?"編輯分類":"新增分類",width:"500px"},{footer:a(()=>[t(s,{onClick:e[20]||(e[20]=o=>_.value=!1)},{default:a(()=>e[36]||(e[36]=[r("取消",-1)])),_:1,__:[36]}),t(s,{type:"primary",onClick:De},{default:a(()=>e[37]||(e[37]=[r("確認",-1)])),_:1,__:[37]})]),default:a(()=>[t(L(ae),{ref_key:"classFormRef",ref:A,model:m,rules:re,"label-width":"120px"},{default:a(()=>[t(v,{label:"分類名稱",prop:"name"},{default:a(()=>[t(C,{modelValue:m.name,"onUpdate:modelValue":e[17]||(e[17]=o=>m.name=o),placeholder:"請輸入分類名稱"},null,8,["modelValue"])]),_:1}),t(v,{label:"上級分類",prop:"parentId"},{default:a(()=>[t(Ie,{modelValue:m.parentId,"onUpdate:modelValue":e[18]||(e[18]=o=>m.parentId=o),data:k.value,props:H,placeholder:"請選擇上級分類（可選）",clearable:"",style:{width:"100%"}},null,8,["modelValue","data"])]),_:1}),t(v,{label:"描述"},{default:a(()=>[t(C,{modelValue:m.description,"onUpdate:modelValue":e[19]||(e[19]=o=>m.description=o),type:"textarea",rows:3,placeholder:"請輸入分類描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});const at=Be(Xe,[["__scopeId","data-v-d13bb7f6"]]);export{at as default};
