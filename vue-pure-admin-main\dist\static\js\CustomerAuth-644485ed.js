import{d as f,u as p,a as h,b as g,r as u,o as I,E as v,c,e as l,f as s,g as d,w as C,h as i,i as E,t as _,j as b,_ as x}from"./index-329ed960.js";import{l as w}from"./index-0d74a956.js";const y={class:"customer-auth-container"},D={class:"auth-content"},R={class:"loading-spinner"},S={key:0,class:"error-message"},k=f({__name:"CustomerAuth",setup(M){const m=p(),a=h(),r=g(),n=u(""),t=u("");return I(async()=>{try{const e=m.params.customerId;if(!e)throw new Error("客戶ID不能為空");if(n.value=e,!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e))throw new Error("客戶ID格式不正確");localStorage.setItem("PLC_CUSTOMER_ID",e),r.setCustomerId&&r.setCustomerId(e),v.success(`客戶身份驗證成功: ${e}`),setTimeout(()=>{a.replace("/plc-dashboard")},1e3)}catch(e){console.error("客戶身份驗證失敗:",e),t.value=e instanceof Error?e.message:"未知錯誤",setTimeout(()=>{a.replace("/login")},3e3)}}),(e,o)=>(c(),l("div",y,[s("div",D,[s("div",R,[d(i(E),{class:"is-loading"},{default:C(()=>[d(i(w))]),_:1})]),o[0]||(o[0]=s("h2",null,"正在驗證客戶身份...",-1)),s("p",null,"客戶ID: "+_(n.value),1),t.value?(c(),l("p",S,_(t.value),1)):b("",!0)])]))}});const N=x(k,[["__scopeId","data-v-b126dabe"]]);export{N as default};
