import{c as x,g as I,r as ee}from"./index-0d74a956.js";import{d as le,r as c,G as S,o as ae,p as g,c as q,e as te,f as d,g as e,w as a,h as V,m as _,n as oe,j as re,E as s,aF as ne,_ as se}from"./index-329ed960.js";const de={class:"setting-container"},ue={class:"page-header"},ie={class:"header-right"},me={class:"card-header"},pe={class:"card-header"},ge={class:"card-header"},fe=le({__name:"setting",setup(be){const M=c("line"),b=S({line:!1,email:!1,sms:!1}),U=c(),E=c(),k=c(),P=c(),p=S({enabled:!0,accessToken:"",defaultGroup:"",timeout:30,retryCount:3}),n=S({enabled:!0,smtpServer:"",smtpPort:587,encryption:"tls",username:"",password:"",senderName:"",senderEmail:"",timeout:60}),u=S({enabled:!1,provider:"",apiUrl:"",account:"",password:"",senderNumber:"",timeout:30}),i=S({defaultPriority:"medium",messageRetentionDays:30,maxRetryCount:3,retryInterval:5,enableLogging:!0,logLevel:"info"}),h={accessToken:[{required:!0,message:"請輸入 Access Token",trigger:"blur"}]},F={smtpServer:[{required:!0,message:"請輸入 SMTP 伺服器",trigger:"blur"}],smtpPort:[{required:!0,message:"請輸入連接埠",trigger:"blur"}],username:[{required:!0,message:"請輸入使用者名稱",trigger:"blur"}],password:[{required:!0,message:"請輸入密碼",trigger:"blur"}],senderEmail:[{required:!0,message:"請輸入寄件者信箱",trigger:"blur"},{type:"email",message:"請輸入正確的信箱格式",trigger:"blur"}]},A={provider:[{required:!0,message:"請選擇服務提供商",trigger:"change"}],apiUrl:[{required:!0,message:"請輸入 API 網址",trigger:"blur"}],account:[{required:!0,message:"請輸入帳號",trigger:"blur"}],password:[{required:!0,message:"請輸入密碼",trigger:"blur"}]},B={defaultPriority:[{required:!0,message:"請選擇預設優先級",trigger:"change"}],messageRetentionDays:[{required:!0,message:"請輸入訊息保留天數",trigger:"blur"}],maxRetryCount:[{required:!0,message:"請輸入最大重試次數",trigger:"blur"}],retryInterval:[{required:!0,message:"請輸入重試間隔",trigger:"blur"}]},G=r=>{r||s.warning("LINE 服務已停用")},D=r=>{r||s.warning("Email 服務已停用")},j=r=>{r||s.warning("SMS 服務已停用")},O=async()=>{try{await U.value?.validate(),b.line=!0,await new Promise(r=>setTimeout(r,2e3)),s.success("LINE 連線測試成功")}catch{s.error("LINE 連線測試失敗")}finally{b.line=!1}},z=async()=>{try{await E.value?.validate(),b.email=!0,await new Promise(r=>setTimeout(r,2e3)),s.success("Email 連線測試成功")}catch{s.error("Email 連線測試失敗")}finally{b.email=!1}},H=async()=>{try{await k.value?.validate(),b.sms=!0,await new Promise(r=>setTimeout(r,2e3)),s.success("SMS 連線測試成功")}catch{s.error("SMS 連線測試失敗")}finally{b.sms=!1}},J=async()=>{try{await U.value?.validate(),s.success("LINE 設定儲存成功")}catch{s.error("LINE 設定儲存失敗")}},K=async()=>{try{await E.value?.validate(),s.success("Email 設定儲存成功")}catch{s.error("Email 設定儲存失敗")}},Q=async()=>{try{await k.value?.validate(),s.success("SMS 設定儲存成功")}catch{s.error("SMS 設定儲存失敗")}},W=async()=>{try{await P.value?.validate(),s.success("一般設定儲存成功")}catch{s.error("一般設定儲存失敗")}},X=async()=>{try{await ne.confirm("確定要重置為預設值嗎？","確認重置",{confirmButtonText:"確定",cancelButtonText:"取消",type:"warning"}),Object.assign(i,{defaultPriority:"medium",messageRetentionDays:30,maxRetryCount:3,retryInterval:5,enableLogging:!0,logLevel:"info"}),s.success("已重置為預設值")}catch{}},Y=async()=>{try{const r=[];p.enabled&&r.push(U.value?.validate()),n.enabled&&r.push(E.value?.validate()),u.enabled&&r.push(k.value?.validate()),r.push(P.value?.validate()),await Promise.all(r),s.success("所有設定儲存成功")}catch{s.error("設定儲存失敗，請檢查表單內容")}},Z=async()=>{};return ae(()=>{Z()}),(r,l)=>{const y=g("el-icon"),v=g("el-button"),T=g("el-switch"),f=g("el-input"),o=g("el-form-item"),w=g("el-input-number"),C=g("el-form"),L=g("el-card"),N=g("el-tab-pane"),m=g("el-option"),R=g("el-select"),$=g("el-tabs");return q(),te("div",de,[d("div",ue,[l[29]||(l[29]=d("div",{class:"header-left"},[d("h1",null,"通知設定"),d("p",null,"設定 LINE、Email、SMS 等通知服務參數")],-1)),d("div",ie,[e(v,{type:"primary",onClick:Y},{default:a(()=>[e(y,null,{default:a(()=>[e(V(x))]),_:1}),l[28]||(l[28]=_(" 儲存所有設定 ",-1))]),_:1,__:[28]})])]),e($,{modelValue:M.value,"onUpdate:modelValue":l[27]||(l[27]=t=>M.value=t),class:"setting-tabs"},{default:a(()=>[e(N,{label:"LINE 設定",name:"line"},{default:a(()=>[e(L,{class:"setting-card",shadow:"never"},{header:a(()=>[d("div",me,[l[30]||(l[30]=d("span",null,"LINE Notify 服務設定",-1)),e(T,{modelValue:p.enabled,"onUpdate:modelValue":l[0]||(l[0]=t=>p.enabled=t),"active-text":"啟用","inactive-text":"停用",onChange:G},null,8,["modelValue"])])]),default:a(()=>[e(C,{ref_key:"lineFormRef",ref:U,model:p,rules:h,"label-width":"120px",disabled:!p.enabled},{default:a(()=>[e(o,{label:"Access Token",prop:"accessToken"},{default:a(()=>[e(f,{modelValue:p.accessToken,"onUpdate:modelValue":l[1]||(l[1]=t=>p.accessToken=t),placeholder:"請輸入 LINE Notify Access Token",type:"password","show-password":"",maxlength:"100"},null,8,["modelValue"]),l[31]||(l[31]=d("div",{class:"form-tip"}," 請至 LINE Notify 官網申請 Access Token ",-1))]),_:1,__:[31]}),e(o,{label:"預設群組",prop:"defaultGroup"},{default:a(()=>[e(f,{modelValue:p.defaultGroup,"onUpdate:modelValue":l[2]||(l[2]=t=>p.defaultGroup=t),placeholder:"請輸入預設群組名稱",maxlength:"50"},null,8,["modelValue"])]),_:1}),e(o,{label:"連線逾時",prop:"timeout"},{default:a(()=>[e(w,{modelValue:p.timeout,"onUpdate:modelValue":l[3]||(l[3]=t=>p.timeout=t),min:5,max:60,step:5,style:{width:"200px"}},null,8,["modelValue"]),l[32]||(l[32]=d("span",{style:{"margin-left":"8px",color:"#909399"}},"秒",-1))]),_:1,__:[32]}),e(o,{label:"重試次數",prop:"retryCount"},{default:a(()=>[e(w,{modelValue:p.retryCount,"onUpdate:modelValue":l[4]||(l[4]=t=>p.retryCount=t),min:0,max:5,style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(o,null,{default:a(()=>[e(v,{type:"primary",onClick:O,loading:b.line},{default:a(()=>[e(y,null,{default:a(()=>[e(V(I))]),_:1}),l[33]||(l[33]=_(" 測試連線 ",-1))]),_:1,__:[33]},8,["loading"]),e(v,{onClick:J},{default:a(()=>[e(y,null,{default:a(()=>[e(V(x))]),_:1}),l[34]||(l[34]=_(" 儲存設定 ",-1))]),_:1,__:[34]})]),_:1})]),_:1},8,["model","disabled"])]),_:1})]),_:1}),e(N,{label:"Email 設定",name:"email"},{default:a(()=>[e(L,{class:"setting-card",shadow:"never"},{header:a(()=>[d("div",pe,[l[35]||(l[35]=d("span",null,"SMTP 郵件服務設定",-1)),e(T,{modelValue:n.enabled,"onUpdate:modelValue":l[5]||(l[5]=t=>n.enabled=t),"active-text":"啟用","inactive-text":"停用",onChange:D},null,8,["modelValue"])])]),default:a(()=>[e(C,{ref_key:"emailFormRef",ref:E,model:n,rules:F,"label-width":"120px",disabled:!n.enabled},{default:a(()=>[e(o,{label:"SMTP 伺服器",prop:"smtpServer"},{default:a(()=>[e(f,{modelValue:n.smtpServer,"onUpdate:modelValue":l[6]||(l[6]=t=>n.smtpServer=t),placeholder:"請輸入 SMTP 伺服器地址",maxlength:"100"},null,8,["modelValue"])]),_:1}),e(o,{label:"連接埠",prop:"smtpPort"},{default:a(()=>[e(w,{modelValue:n.smtpPort,"onUpdate:modelValue":l[7]||(l[7]=t=>n.smtpPort=t),min:1,max:65535,style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(o,{label:"加密方式",prop:"encryption"},{default:a(()=>[e(R,{modelValue:n.encryption,"onUpdate:modelValue":l[8]||(l[8]=t=>n.encryption=t),placeholder:"請選擇加密方式",style:{width:"200px"}},{default:a(()=>[e(m,{label:"無",value:"none"}),e(m,{label:"SSL",value:"ssl"}),e(m,{label:"TLS",value:"tls"})]),_:1},8,["modelValue"])]),_:1}),e(o,{label:"使用者名稱",prop:"username"},{default:a(()=>[e(f,{modelValue:n.username,"onUpdate:modelValue":l[9]||(l[9]=t=>n.username=t),placeholder:"請輸入使用者名稱",maxlength:"100"},null,8,["modelValue"])]),_:1}),e(o,{label:"密碼",prop:"password"},{default:a(()=>[e(f,{modelValue:n.password,"onUpdate:modelValue":l[10]||(l[10]=t=>n.password=t),placeholder:"請輸入密碼",type:"password","show-password":"",maxlength:"100"},null,8,["modelValue"])]),_:1}),e(o,{label:"寄件者名稱",prop:"senderName"},{default:a(()=>[e(f,{modelValue:n.senderName,"onUpdate:modelValue":l[11]||(l[11]=t=>n.senderName=t),placeholder:"請輸入寄件者名稱",maxlength:"50"},null,8,["modelValue"])]),_:1}),e(o,{label:"寄件者信箱",prop:"senderEmail"},{default:a(()=>[e(f,{modelValue:n.senderEmail,"onUpdate:modelValue":l[12]||(l[12]=t=>n.senderEmail=t),placeholder:"請輸入寄件者信箱",maxlength:"100"},null,8,["modelValue"])]),_:1}),e(o,{label:"連線逾時",prop:"timeout"},{default:a(()=>[e(w,{modelValue:n.timeout,"onUpdate:modelValue":l[13]||(l[13]=t=>n.timeout=t),min:10,max:120,step:10,style:{width:"200px"}},null,8,["modelValue"]),l[36]||(l[36]=d("span",{style:{"margin-left":"8px",color:"#909399"}},"秒",-1))]),_:1,__:[36]}),e(o,null,{default:a(()=>[e(v,{type:"primary",onClick:z,loading:b.email},{default:a(()=>[e(y,null,{default:a(()=>[e(V(I))]),_:1}),l[37]||(l[37]=_(" 測試連線 ",-1))]),_:1,__:[37]},8,["loading"]),e(v,{onClick:K},{default:a(()=>[e(y,null,{default:a(()=>[e(V(x))]),_:1}),l[38]||(l[38]=_(" 儲存設定 ",-1))]),_:1,__:[38]})]),_:1})]),_:1},8,["model","disabled"])]),_:1})]),_:1}),e(N,{label:"SMS 設定",name:"sms"},{default:a(()=>[e(L,{class:"setting-card",shadow:"never"},{header:a(()=>[d("div",ge,[l[39]||(l[39]=d("span",null,"SMS 簡訊服務設定",-1)),e(T,{modelValue:u.enabled,"onUpdate:modelValue":l[14]||(l[14]=t=>u.enabled=t),"active-text":"啟用","inactive-text":"停用",onChange:j},null,8,["modelValue"])])]),default:a(()=>[e(C,{ref_key:"smsFormRef",ref:k,model:u,rules:A,"label-width":"120px",disabled:!u.enabled},{default:a(()=>[e(o,{label:"服務提供商",prop:"provider"},{default:a(()=>[e(R,{modelValue:u.provider,"onUpdate:modelValue":l[15]||(l[15]=t=>u.provider=t),placeholder:"請選擇服務提供商",style:{width:"200px"}},{default:a(()=>[e(m,{label:"中華電信",value:"cht"}),e(m,{label:"台灣大哥大",value:"twm"}),e(m,{label:"遠傳電信",value:"fet"}),e(m,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(o,{label:"API 網址",prop:"apiUrl"},{default:a(()=>[e(f,{modelValue:u.apiUrl,"onUpdate:modelValue":l[16]||(l[16]=t=>u.apiUrl=t),placeholder:"請輸入 API 網址",maxlength:"200"},null,8,["modelValue"])]),_:1}),e(o,{label:"帳號",prop:"account"},{default:a(()=>[e(f,{modelValue:u.account,"onUpdate:modelValue":l[17]||(l[17]=t=>u.account=t),placeholder:"請輸入帳號",maxlength:"50"},null,8,["modelValue"])]),_:1}),e(o,{label:"密碼",prop:"password"},{default:a(()=>[e(f,{modelValue:u.password,"onUpdate:modelValue":l[18]||(l[18]=t=>u.password=t),placeholder:"請輸入密碼",type:"password","show-password":"",maxlength:"100"},null,8,["modelValue"])]),_:1}),e(o,{label:"發送者號碼",prop:"senderNumber"},{default:a(()=>[e(f,{modelValue:u.senderNumber,"onUpdate:modelValue":l[19]||(l[19]=t=>u.senderNumber=t),placeholder:"請輸入發送者號碼",maxlength:"20"},null,8,["modelValue"])]),_:1}),e(o,{label:"連線逾時",prop:"timeout"},{default:a(()=>[e(w,{modelValue:u.timeout,"onUpdate:modelValue":l[20]||(l[20]=t=>u.timeout=t),min:10,max:120,step:10,style:{width:"200px"}},null,8,["modelValue"]),l[40]||(l[40]=d("span",{style:{"margin-left":"8px",color:"#909399"}},"秒",-1))]),_:1,__:[40]}),e(o,null,{default:a(()=>[e(v,{type:"primary",onClick:H,loading:b.sms},{default:a(()=>[e(y,null,{default:a(()=>[e(V(I))]),_:1}),l[41]||(l[41]=_(" 測試連線 ",-1))]),_:1,__:[41]},8,["loading"]),e(v,{onClick:Q},{default:a(()=>[e(y,null,{default:a(()=>[e(V(x))]),_:1}),l[42]||(l[42]=_(" 儲存設定 ",-1))]),_:1,__:[42]})]),_:1})]),_:1},8,["model","disabled"])]),_:1})]),_:1}),e(N,{label:"一般設定",name:"general"},{default:a(()=>[e(L,{class:"setting-card",shadow:"never"},{header:a(()=>l[43]||(l[43]=[d("span",null,"一般通知設定",-1)])),default:a(()=>[e(C,{ref_key:"generalFormRef",ref:P,model:i,rules:B,"label-width":"120px"},{default:a(()=>[e(o,{label:"預設優先級",prop:"defaultPriority"},{default:a(()=>[e(R,{modelValue:i.defaultPriority,"onUpdate:modelValue":l[21]||(l[21]=t=>i.defaultPriority=t),placeholder:"請選擇預設優先級",style:{width:"200px"}},{default:a(()=>[e(m,{label:"低",value:"low"}),e(m,{label:"中",value:"medium"}),e(m,{label:"高",value:"high"}),e(m,{label:"緊急",value:"urgent"})]),_:1},8,["modelValue"])]),_:1}),e(o,{label:"訊息保留天數",prop:"messageRetentionDays"},{default:a(()=>[e(w,{modelValue:i.messageRetentionDays,"onUpdate:modelValue":l[22]||(l[22]=t=>i.messageRetentionDays=t),min:1,max:365,style:{width:"200px"}},null,8,["modelValue"]),l[44]||(l[44]=d("span",{style:{"margin-left":"8px",color:"#909399"}},"天",-1))]),_:1,__:[44]}),e(o,{label:"最大重試次數",prop:"maxRetryCount"},{default:a(()=>[e(w,{modelValue:i.maxRetryCount,"onUpdate:modelValue":l[23]||(l[23]=t=>i.maxRetryCount=t),min:0,max:10,style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(o,{label:"重試間隔",prop:"retryInterval"},{default:a(()=>[e(w,{modelValue:i.retryInterval,"onUpdate:modelValue":l[24]||(l[24]=t=>i.retryInterval=t),min:1,max:60,style:{width:"200px"}},null,8,["modelValue"]),l[45]||(l[45]=d("span",{style:{"margin-left":"8px",color:"#909399"}},"分鐘",-1))]),_:1,__:[45]}),e(o,{label:"啟用日誌記錄",prop:"enableLogging"},{default:a(()=>[e(T,{modelValue:i.enableLogging,"onUpdate:modelValue":l[25]||(l[25]=t=>i.enableLogging=t),"active-text":"啟用","inactive-text":"停用"},null,8,["modelValue"])]),_:1}),i.enableLogging?(q(),oe(o,{key:0,label:"日誌等級",prop:"logLevel"},{default:a(()=>[e(R,{modelValue:i.logLevel,"onUpdate:modelValue":l[26]||(l[26]=t=>i.logLevel=t),placeholder:"請選擇日誌等級",style:{width:"200px"}},{default:a(()=>[e(m,{label:"錯誤",value:"error"}),e(m,{label:"警告",value:"warning"}),e(m,{label:"資訊",value:"info"}),e(m,{label:"除錯",value:"debug"})]),_:1},8,["modelValue"])]),_:1})):re("",!0),e(o,null,{default:a(()=>[e(v,{type:"primary",onClick:W},{default:a(()=>[e(y,null,{default:a(()=>[e(V(x))]),_:1}),l[46]||(l[46]=_(" 儲存設定 ",-1))]),_:1,__:[46]}),e(v,{onClick:X},{default:a(()=>[e(y,null,{default:a(()=>[e(V(ee))]),_:1}),l[47]||(l[47]=_(" 重置為預設值 ",-1))]),_:1,__:[47]})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}});const Ve=se(fe,[["__scopeId","data-v-6c37841f"]]);export{Ve as default};
