import{G as u,I as o,$ as n,ba as d,r as g,bb as C,A as f,c as v,e as p,f as h}from"./index-329ed960.js";const w=/^\d{6}$/,m=/^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)]|[()])+$)(?!^.*[\u4E00-\u9FA5].*$)([^(0-9a-zA-Z)]|[()]|[a-z]|[A-Z]|[0-9]){8,18}$/,I=u({password:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.purePassWordReg")))):e()},trigger:"blur"}],verifyCode:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.pureVerifyCodeReg")))):r.length!==4||!/^\d{4}$/.test(r)?e(new Error("請輸入4位數字驗證碼")):e()},trigger:"blur"}]}),x=u({phone:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.purePhoneReg")))):d(r)?e():e(new Error(o(n("login.purePhoneCorrectReg"))))},trigger:"blur"}],verifyCode:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.pureVerifyCodeReg")))):w.test(r)?e():e(new Error(o(n("login.pureVerifyCodeSixReg"))))},trigger:"blur"}]}),_=u({phone:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.purePhoneReg")))):d(r)?e():e(new Error(o(n("login.purePhoneCorrectReg"))))},trigger:"blur"}],verifyCode:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.pureVerifyCodeReg")))):w.test(r)?e():e(new Error(o(n("login.pureVerifyCodeSixReg"))))},trigger:"blur"}],password:[{validator:(t,r,e)=>{r===""?e(new Error(o(n("login.purePassWordReg")))):m.test(r)?e():e(new Error(o(n("login.purePassWordRuleReg"))))},trigger:"blur"}]}),a=g(!1),i=g(null),l=g(""),B=()=>({isDisabled:a,timer:i,text:l,start:async(e,R,s=60)=>{if(!e)return;const E=C(s,!0);await e.validateField(R,$=>{$&&(clearInterval(i.value),a.value=!0,l.value=`${s}`,i.value=setInterval(()=>{s>0?(s-=1,l.value=`${s}`):(l.value="",a.value=!1,clearInterval(i.value),s=E)},1e3))})},end:()=>{l.value="",a.value=!1,clearInterval(i.value)}}),y={viewBox:"0 0 1024 1024",width:"1em",height:"1em"};function V(t,r){return v(),p("svg",y,r[0]||(r[0]=[h("path",{fill:"currentColor",d:"M224 768v96.064a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V768zm0-64h576V160a64 64 0 0 0-64-64H288a64 64 0 0 0-64 64zm32 288a96 96 0 0 1-96-96V128a96 96 0 0 1 96-96h512a96 96 0 0 1 96 96v768a96 96 0 0 1-96 96zm304-144a48 48 0 1 1-96 0a48 48 0 0 1 96 0"},null,-1)]))}const c=f({name:"ep-iphone",render:V}),P={viewBox:"0 0 24 24",width:"1em",height:"1em"};function z(t,r){return v(),p("svg",P,r[0]||(r[0]=[h("path",{fill:"currentColor",d:"m12 1l8.217 1.826a1 1 0 0 1 .783.976v9.987a6 6 0 0 1-2.672 4.992L12 23l-6.328-4.219A6 6 0 0 1 3 13.79V3.802a1 1 0 0 1 .783-.976zm0 2.049L5 4.604v9.185a4 4 0 0 0 1.781 3.328L12 20.597l5.219-3.48A4 4 0 0 0 19 13.79V4.604zM12 7a2 2 0 0 1 1.001 3.732L13 15h-2v-4.268A2 2 0 0 1 12 7"},null,-1)]))}const L=f({name:"ri-shield-keyhole-line",render:z});export{c as I,L as K,_ as a,I as l,x as p,B as u};
