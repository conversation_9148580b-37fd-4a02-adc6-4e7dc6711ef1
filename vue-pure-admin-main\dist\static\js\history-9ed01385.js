import{a as fe,r as ve,e as ye,f as G,c as Ae}from"./index-0d74a956.js";import{a as Y}from"./alarm-635d9769.js";import{d as he,r as k,G as F,D as we,o as be,p as u,q as ke,c as p,e as f,g as e,w as a,h as y,m as o,f as I,t as s,l as Ie,n as $,j as U,E as C,aF as J,_ as Ce}from"./index-329ed960.js";import"./dataService-42a3bdf6.js";function M(R,L="YYYY-MM-DD HH:mm:ss"){if(!R)return"-";try{const g=typeof R=="string"?new Date(R):R;if(isNaN(g.getTime()))return"-";const z=g.getFullYear(),N=String(g.getMonth()+1).padStart(2,"0"),S=String(g.getDate()).padStart(2,"0"),d=String(g.getHours()).padStart(2,"0"),m=String(g.getMinutes()).padStart(2,"0"),w=String(g.getSeconds()).padStart(2,"0");return L.replace("YYYY",z.toString()).replace("MM",N).replace("DD",S).replace("HH",d).replace("mm",m).replace("ss",w)}catch(g){return console.error("日期格式化錯誤:",g),"-"}}const Se={class:"alarm-history-container"},Te={class:"card-header"},Ve={class:"header-actions"},xe={key:0},De={key:1},Le={key:0},ze={key:1},Me={key:0},Re={key:1},Ne={key:0},Ye={key:1},Be={class:"pagination-container"},Ue={key:0,class:"batch-actions"},He={class:"batch-content"},je={class:"batch-buttons"},Ee={key:0,class:"alarm-detail"},Fe={class:"dialog-footer"},$e=he({__name:"history",setup(R){const L=k(!1),g=k(!1),z=k(!1),N=k(!1),S=k(!1),d=F({dateRange:[],alarmLevel:void 0,tagName:""}),m=F({pageIndex:1,pageSize:20,total:0}),w=k([]),T=k([]),r=k(null),A=F({totalAlarms:0,criticalAlarms:0,warningAlarms:0,infoAlarms:0}),K=we(()=>T.value.some(l=>!l.IsAcknowledged)),V=async()=>{try{L.value=!0;const l={pageIndex:m.pageIndex,pageSize:m.pageSize,alarmLevel:d.alarmLevel,tagName:d.tagName||void 0};d.dateRange&&d.dateRange.length===2&&(l.startTime=d.dateRange[0],l.endTime=d.dateRange[1]);const t=await Y.getAlarmHistory(l);w.value=t.Items||[],m.total=t.TotalCount||0,W()}catch(l){console.error("載入警報歷史失敗:",l),C.error("載入警報歷史失敗")}finally{L.value=!1}},Q=async()=>{try{const l=await Y.getAlarmStatistics();Object.assign(A,l)}catch(l){console.error("載入統計資訊失敗:",l)}},W=()=>{A.totalAlarms=w.value.length,A.criticalAlarms=w.value.filter(l=>l.AlarmLevel===1).length,A.warningAlarms=w.value.filter(l=>l.AlarmLevel===2).length,A.infoAlarms=w.value.filter(l=>l.AlarmLevel===3).length},X=()=>{m.pageIndex=1,V()},Z=()=>{d.dateRange=[],d.alarmLevel=void 0,d.tagName="",m.pageIndex=1,V()},ee=async()=>{try{g.value=!0;const l={pageIndex:1,pageSize:1e4,alarmLevel:d.alarmLevel,tagName:d.tagName||void 0};d.dateRange&&d.dateRange.length===2&&(l.startTime=d.dateRange[0],l.endTime=d.dateRange[1]);const x=(await Y.getAlarmHistory(l)).Items||[],D=ae(x),c=new Blob([D],{type:"text/csv;charset=utf-8;"}),v=document.createElement("a"),j=URL.createObjectURL(c);v.setAttribute("href",j),v.setAttribute("download",`alarm_history_${new Date().getTime()}.csv`),v.style.visibility="hidden",document.body.appendChild(v),v.click(),document.body.removeChild(v),C.success("匯出成功")}catch(l){console.error("匯出失敗:",l),C.error("匯出失敗")}finally{g.value=!1}},ae=l=>{const t=["警報時間","標籤名稱","警報訊息","警報等級","警報類型","當前值","警報值","單位","確認狀態","確認人員","確認時間"],x=l.map(c=>[M(c.AlarmTime),c.TagName,c.AlarmMessage,H(c.AlarmLevel),c.AlarmType,c.CurrentValue??"",c.AlarmValue??"",c.Unit||"",c.IsAcknowledged?"已確認":"未確認",c.AcknowledgedBy||"",c.AcknowledgedTime?M(c.AcknowledgedTime):""]);return"\uFEFF"+[t,...x].map(c=>c.map(v=>`"${v}"`).join(",")).join(`
`)},te=l=>{m.pageSize=l,m.pageIndex=1,V()},le=l=>{m.pageIndex=l,V()},ne=l=>{T.value=l},oe=()=>{T.value=[]},O=async l=>{try{await J.confirm(`確定要確認警報「${l.AlarmMessage}」嗎？`,"確認警報",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"}),z.value=!0,await Y.acknowledgeAlarm({AlarmSummaryId:l.AlarmSummaryId}),C.success("警報確認成功"),V()}catch(t){t!=="cancel"&&(console.error("確認警報失敗:",t),C.error("確認警報失敗"))}finally{z.value=!1}},se=async()=>{const l=T.value.filter(t=>!t.IsAcknowledged);if(l.length===0){C.warning("沒有需要確認的警報");return}try{await J.confirm(`確定要批量確認 ${l.length} 個警報嗎？`,"批量確認警報",{confirmButtonText:"確認",cancelButtonText:"取消",type:"warning"}),N.value=!0;const t=l.map(x=>x.AlarmSummaryId);await Y.batchAcknowledgeAlarms({alarmSummaryIds:t}),C.success("批量確認成功"),T.value=[],V()}catch(t){t!=="cancel"&&(console.error("批量確認失敗:",t),C.error("批量確認失敗"))}finally{N.value=!1}},re=l=>{r.value=l,S.value=!0},de=async()=>{r.value&&(await O(r.value),S.value=!1)},P=l=>{switch(l){case 1:return"danger";case 2:return"warning";case 3:return"info";default:return"info"}},H=l=>{switch(l){case 1:return"嚴重";case 2:return"警告";case 3:return"資訊";default:return"未知"}},B=l=>{switch(l){case"total":return"info";case"critical":return"danger";case"warning":return"warning";case"info":return"success";default:return"info"}};return be(()=>{const l=new Date,t=new Date;t.setDate(t.getDate()-7),d.dateRange=[t.toISOString().slice(0,19).replace("T"," "),l.toISOString().slice(0,19).replace("T"," ")],V(),Q()}),(l,t)=>{const x=u("el-date-picker"),D=u("el-form-item"),c=u("el-option"),v=u("el-select"),j=u("el-input"),h=u("el-button"),ie=u("el-form"),E=u("el-card"),b=u("el-tag"),_=u("el-table-column"),q=u("el-icon"),ce=u("el-table"),ue=u("el-pagination"),i=u("el-descriptions-item"),me=u("el-descriptions"),pe=u("el-dialog"),ge=ke("loading");return p(),f("div",Se,[e(E,{class:"search-card",shadow:"never"},{default:a(()=>[e(ie,{model:d,inline:!0,"label-width":"80px"},{default:a(()=>[e(D,{label:"時間範圍"},{default:a(()=>[e(x,{modelValue:d.dateRange,"onUpdate:modelValue":t[0]||(t[0]=n=>d.dateRange=n),type:"datetimerange","range-separator":"至","start-placeholder":"開始時間","end-placeholder":"結束時間",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)],style:{width:"350px"}},null,8,["modelValue","default-time"])]),_:1}),e(D,{label:"警報等級"},{default:a(()=>[e(v,{modelValue:d.alarmLevel,"onUpdate:modelValue":t[1]||(t[1]=n=>d.alarmLevel=n),placeholder:"請選擇警報等級",clearable:"",style:{width:"150px"}},{default:a(()=>[e(c,{label:"全部",value:void 0}),e(c,{label:"嚴重",value:1}),e(c,{label:"警告",value:2}),e(c,{label:"資訊",value:3})]),_:1},8,["modelValue"])]),_:1}),e(D,{label:"標籤名稱"},{default:a(()=>[e(j,{modelValue:d.tagName,"onUpdate:modelValue":t[2]||(t[2]=n=>d.tagName=n),placeholder:"請輸入標籤名稱",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(D,null,{default:a(()=>[e(h,{type:"primary",icon:y(fe),onClick:X,loading:L.value},{default:a(()=>t[7]||(t[7]=[o(" 搜尋 ",-1)])),_:1,__:[7]},8,["icon","loading"]),e(h,{icon:y(ve),onClick:Z},{default:a(()=>t[8]||(t[8]=[o(" 重置 ",-1)])),_:1,__:[8]},8,["icon"]),e(h,{icon:y(ye),onClick:ee,loading:g.value},{default:a(()=>t[9]||(t[9]=[o(" 匯出 ",-1)])),_:1,__:[9]},8,["icon","loading"])]),_:1})]),_:1},8,["model"])]),_:1}),e(E,{class:"table-card"},{header:a(()=>[I("div",Te,[t[10]||(t[10]=I("span",null,"警報歷史記錄",-1)),I("div",Ve,[e(b,{type:B("total"),size:"large"},{default:a(()=>[o(" 總計: "+s(A.totalAlarms),1)]),_:1},8,["type"]),e(b,{type:B("critical"),size:"large"},{default:a(()=>[o(" 嚴重: "+s(A.criticalAlarms),1)]),_:1},8,["type"]),e(b,{type:B("warning"),size:"large"},{default:a(()=>[o(" 警告: "+s(A.warningAlarms),1)]),_:1},8,["type"]),e(b,{type:B("info"),size:"large"},{default:a(()=>[o(" 資訊: "+s(A.infoAlarms),1)]),_:1},8,["type"])])])]),default:a(()=>[Ie((p(),$(ce,{data:w.value,stripe:"",border:"",height:"600",onSelectionChange:ne},{default:a(()=>[e(_,{type:"selection",width:"55"}),e(_,{prop:"AlarmTime",label:"警報時間",width:"180",sortable:""},{default:a(({row:n})=>[e(q,null,{default:a(()=>[e(y(G))]),_:1}),o(" "+s(y(M)(n.AlarmTime)),1)]),_:1}),e(_,{prop:"TagName",label:"標籤名稱",width:"150","show-overflow-tooltip":""}),e(_,{prop:"AlarmMessage",label:"警報訊息","min-width":"200","show-overflow-tooltip":""}),e(_,{prop:"AlarmLevel",label:"警報等級",width:"100",align:"center"},{default:a(({row:n})=>[e(b,{type:P(n.AlarmLevel),size:"small"},{default:a(()=>[o(s(H(n.AlarmLevel)),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"AlarmType",label:"警報類型",width:"120",align:"center"}),e(_,{prop:"CurrentValue",label:"當前值",width:"100",align:"center"},{default:a(({row:n})=>[n.CurrentValue!==null&&n.CurrentValue!==void 0?(p(),f("span",xe,s(n.CurrentValue)+" "+s(n.Unit||""),1)):(p(),f("span",De,"-"))]),_:1}),e(_,{prop:"AlarmValue",label:"警報值",width:"100",align:"center"},{default:a(({row:n})=>[n.AlarmValue!==null&&n.AlarmValue!==void 0?(p(),f("span",Le,s(n.AlarmValue)+" "+s(n.Unit||""),1)):(p(),f("span",ze,"-"))]),_:1}),e(_,{prop:"IsAcknowledged",label:"確認狀態",width:"100",align:"center"},{default:a(({row:n})=>[e(b,{type:n.IsAcknowledged?"success":"danger",size:"small"},{default:a(()=>[o(s(n.IsAcknowledged?"已確認":"未確認"),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"AcknowledgedBy",label:"確認人員",width:"120","show-overflow-tooltip":""},{default:a(({row:n})=>[n.AcknowledgedBy?(p(),f("span",Me,s(n.AcknowledgedBy),1)):(p(),f("span",Re,"-"))]),_:1}),e(_,{prop:"AcknowledgedTime",label:"確認時間",width:"180"},{default:a(({row:n})=>[n.AcknowledgedTime?(p(),f("span",Ne,[e(q,null,{default:a(()=>[e(y(G))]),_:1}),o(" "+s(y(M)(n.AcknowledgedTime)),1)])):(p(),f("span",Ye,"-"))]),_:1}),e(_,{label:"操作",width:"120",fixed:"right"},{default:a(({row:n})=>[n.IsAcknowledged?U("",!0):(p(),$(h,{key:0,type:"primary",size:"small",onClick:_e=>O(n),loading:z.value},{default:a(()=>t[11]||(t[11]=[o(" 確認 ",-1)])),_:2,__:[11]},1032,["onClick","loading"])),e(h,{type:"info",size:"small",onClick:_e=>re(n)},{default:a(()=>t[12]||(t[12]=[o(" 詳情 ",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ge,L.value]]),I("div",Be,[e(ue,{"current-page":m.pageIndex,"onUpdate:currentPage":t[3]||(t[3]=n=>m.pageIndex=n),"page-size":m.pageSize,"onUpdate:pageSize":t[4]||(t[4]=n=>m.pageSize=n),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:te,onCurrentChange:le},null,8,["current-page","page-size","total"])])]),_:1}),T.value.length>0?(p(),f("div",Ue,[e(E,{shadow:"never"},{default:a(()=>[I("div",He,[I("span",null,"已選擇 "+s(T.value.length)+" 項",1),I("div",je,[e(h,{type:"primary",icon:y(Ae),onClick:se,loading:N.value,disabled:!K.value},{default:a(()=>t[13]||(t[13]=[o(" 批量確認 ",-1)])),_:1,__:[13]},8,["icon","loading","disabled"]),e(h,{onClick:oe},{default:a(()=>t[14]||(t[14]=[o(" 取消選擇 ",-1)])),_:1,__:[14]})])])]),_:1})])):U("",!0),e(pe,{modelValue:S.value,"onUpdate:modelValue":t[6]||(t[6]=n=>S.value=n),title:"警報詳情",width:"600px","close-on-click-modal":!1},{footer:a(()=>[I("div",Fe,[e(h,{onClick:t[5]||(t[5]=n=>S.value=!1)},{default:a(()=>t[15]||(t[15]=[o("關閉",-1)])),_:1,__:[15]}),r.value&&!r.value.IsAcknowledged?(p(),$(h,{key:0,type:"primary",onClick:de,loading:z.value},{default:a(()=>t[16]||(t[16]=[o(" 確認警報 ",-1)])),_:1,__:[16]},8,["loading"])):U("",!0)])]),default:a(()=>[r.value?(p(),f("div",Ee,[e(me,{column:2,border:""},{default:a(()=>[e(i,{label:"警報ID"},{default:a(()=>[o(s(r.value.AlarmSummaryId),1)]),_:1}),e(i,{label:"標籤ID"},{default:a(()=>[o(s(r.value.TagId),1)]),_:1}),e(i,{label:"標籤名稱"},{default:a(()=>[o(s(r.value.TagName),1)]),_:1}),e(i,{label:"警報訊息"},{default:a(()=>[o(s(r.value.AlarmMessage),1)]),_:1}),e(i,{label:"警報時間"},{default:a(()=>[o(s(y(M)(r.value.AlarmTime)),1)]),_:1}),e(i,{label:"警報等級"},{default:a(()=>[e(b,{type:P(r.value.AlarmLevel)},{default:a(()=>[o(s(H(r.value.AlarmLevel)),1)]),_:1},8,["type"])]),_:1}),e(i,{label:"警報類型"},{default:a(()=>[o(s(r.value.AlarmType),1)]),_:1}),e(i,{label:"當前值"},{default:a(()=>[o(s(r.value.CurrentValue??"-")+" "+s(r.value.Unit||""),1)]),_:1}),e(i,{label:"警報值"},{default:a(()=>[o(s(r.value.AlarmValue??"-")+" "+s(r.value.Unit||""),1)]),_:1}),e(i,{label:"確認狀態"},{default:a(()=>[e(b,{type:r.value.IsAcknowledged?"success":"danger"},{default:a(()=>[o(s(r.value.IsAcknowledged?"已確認":"未確認"),1)]),_:1},8,["type"])]),_:1}),e(i,{label:"確認人員"},{default:a(()=>[o(s(r.value.AcknowledgedBy||"-"),1)]),_:1}),e(i,{label:"確認時間"},{default:a(()=>[o(s(r.value.AcknowledgedTime?y(M)(r.value.AcknowledgedTime):"-"),1)]),_:1}),e(i,{label:"客戶ID"},{default:a(()=>[o(s(r.value.CustomerId),1)]),_:1}),e(i,{label:"伺服器IP"},{default:a(()=>[o(s(r.value.ServerIp),1)]),_:1}),e(i,{label:"數值位址"},{default:a(()=>[o(s(r.value.ValueAddress||"-"),1)]),_:1}),e(i,{label:"設備ID"},{default:a(()=>[o(s(r.value.DeviceId||"-"),1)]),_:1}),e(i,{label:"區域ID"},{default:a(()=>[o(s(r.value.RegionId||"-"),1)]),_:1}),e(i,{label:"測量單位"},{default:a(()=>[o(s(r.value.MeasurementUnit||"-"),1)]),_:1}),e(i,{label:"資料型態"},{default:a(()=>[o(s(r.value.DataType||"-"),1)]),_:1}),e(i,{label:"測點類型"},{default:a(()=>[o(s(r.value.Type||"-"),1)]),_:1}),e(i,{label:"測點分類"},{default:a(()=>[o(s(r.value.TagCategoryIdList?.join(", ")||"-"),1)]),_:1}),e(i,{label:"CCTV列表"},{default:a(()=>[o(s(r.value.CCTVIdList?.join(", ")||"-"),1)]),_:1})]),_:1})])):U("",!0)]),_:1},8,["modelValue"])])}}});const Je=Ce($e,[["__scopeId","data-v-d915c214"]]);export{Je as default};
